<div id="smooth-wrapper">
<div id="smooth-content">
<div
    data-portal="destination"
    data-portal-namespace="secondary"
    class="post-template-default single single-post postid-15508 single-format-standard -mb-px"
    >

<div
    x-data
    x-init="
    $store.header.bg_color = 'white';
    $store.header.theme = 'light';
    $store.header.bg = true;
    $store.header.border = true;
    $store.header.border_color = 'neutral';
    "
    x-cloak
    class="hidden"
    ></div>
<div id="maincontent"></div>
<main class="pb-12">
    <div class="fluid-container pb-20">
        <div
            x-data
            x-ref="container"
            class="mt-16"
            :style="{ paddingTop: `${$store.header.heightTop}px` }"
            >
            <h1
                x-data='fadeIn()'
                x-init='
                scrollTrigger = false;
                delay = avalanche.delay.enter + 0.4;
                mounted();
                '
                x-cloak
                x-ref='element'
                class="
                text-primary-600
                font-sans leading-none font-semibold
                text-5xl md:text-6xl
                mb-9 lg:text-center text-balance
                "
                >
                Our History
            </h1>
            <div class="my-12">
                <div
                    x-data="revealer()"
                    x-init="
                    setTrigger($refs.container);
                    element = $refs.media;
                    mounted();
                    "
                    x-ref="container"
                    class="max-w-5xl mx-auto relative aspect-16/9 short-desktop:aspect-16/7 overflow-hidden"
                    >
                    <div
                        x-ref="media"
                        class="absolute inset-0 w-full h-full"
                        >
                        <div
                            x-data="animate()"
                            x-init="
                            opacity.active = false;
                            scale = {
                            ...scale, active: true,
                            start: 1.08,
                            end: 1.08,
                            ease: 'none',
                            };
                            yPercent = {
                            ...yPercent, active: true,
                            start: -4,
                            end: 4,
                            ease: 'none',
                            };
                            trigger = $refs.container;
                            scrub = 1.2;
                            start = 'top bottom';
                            mounted();
                            "
                            class="absolute inset-0 w-full h-full overflow-hidden"
                            >
                            <div
                                x-ref='element'
                                class="
                                absolute inset-0
                                w-full h-full
                                "
                                >
                                <picture>
                                    <img
                                        src="/images/7113.jpg"
                                        class="
                                        absolute inset-0 w-full h-full object-cover
                                        w-full h-full
                                        "
                                        alt="A group of people on stage cutting an orange ribbon at the Nieri Family Clemson Alumni and Visitors Center opening ceremony, with balloons in the background and the Nieri Family present, as the audience watches from their seats."
                                        />
                                </picture>
                            </div>
                        </div>
                    </div>
                    <div
                        x-ref="cover"
                        class="bg-neutral absolute inset-0"
                        ></div>
                    <div
                        x-ref="curtain"
                        class="bg-primary absolute inset-0"
                        ></div>
                </div>
            </div>
        </div>
        <div
            class="prose max-w-2xl mx-auto mb-8 lg:mb-0"
            >
            <p>For the next eight years, Peter worked, ate, and slept at every job site as he continued to build more school rooftops and then libraries, police stations, hotels, and hospitals. As his portfolio expanded, so did his vision. In 2014, Eastern Glass and Aluminum was launched, expanding from primarily roofing to a full-service envelope contractor with roofing, siding, and glass/glazing capabilities. EGA’s aim broadened to target commercial, education, government, hospitality, facility, and sports stadium markets.</p>
            <p>With multi-state reach and a resume of successful projects, Eastern secured the largest project in its history in 2018 as the general contractor for Phase I of the SK Battery America plant in Commerce, Georgia. Born from the success of the SK Battery project was the desire to expand in a novel direction— the implementation of commercial and industrial general contractor units. Ultimately, iugis Construction Corporation was created from this newfound growth. As a general contracting company, iugis specializes in commercial, K-12, higher education, healthcare, religious, multi-purpose, retail, multi-family, mixed-use, senior living, and student housing facilities. Rounding out the full construction arena and focusing on the specialized needs of industrialized conglomerates, Eastern Contractors Corporation originated.</p>
        </div>
    </div>
</main>
<script>
$(document).ready(function() {
  // Set the initial src
  $("#site-logo").attr("src", "/images/logo/site-logo.png");
  
  // Add event listener to catch property changes
  $("#site-logo").on("load error", function() {
    if ($(this).attr("src") !== "/images/logo/site-logo.png") {
      $(this).attr("src", "/images/logo/site-logo.png");
    }
  });
});
</script>