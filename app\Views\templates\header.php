<html lang="en-US" style="overscroll-behavior: none; scroll-behavior: auto;">
<head>
    <title>
        Eastern Companies
    </title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="description" content="">
    <link rel="canonical" href="">
    <meta property="og:locale" content="en_US">
    <meta property="og:type" content="website">
    <meta property="og:title" content="Home">
    <meta property="og:description" content="">
    <meta property="og:url" content="">
    <meta property="og:site_name" content="">
    <meta property="og:image" content="">
    <meta property="og:image:width" content="1500">
    <meta property="og:image:height" content="1012">
    <meta property="og:image:type" content="image/jpeg">
    <meta name="twitter:card" content="summary_large_image">
    <script src="/scripts/jquery-3.6.0.min.js"></script>
    <script type="text/javascript">
        carouselActiveIndex = 1;
        getCarouselGrayedIndexDesktop = 1;
        videoCarouselActiveIndex = 0;
    </script>
    <style id="wp-emoji-styles-inline-css" type="text/css">
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
     <link rel="stylesheet" href="/styles/global.css" type="text/css" media="all">
    <link rel="stylesheet" id="wp-block-library-css" href="/styles/style.min.css" type="text/css" media="all">
    <style id="safe-svg-svg-icon-style-inline-css" type="text/css">
        .safe-svg-cover {
            text-align: center
        }

        .safe-svg-cover .safe-svg-inside {
            display: inline-block;
            max-width: 100%
        }

        .safe-svg-cover svg {
            height: 100%;
            max-height: 100%;
            max-width: 100%;
            width: 100%
        }
    </style>
    <style id="classic-theme-styles-inline-css" type="text/css">
        /*! This file is auto-generated */
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id="global-styles-inline-css" type="text/css">
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :where(.is-layout-flex) {
            gap: 0.5em;
        }

        :where(.is-layout-grid) {
            gap: 0.5em;
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }

        .myul {
            list-style-type: disc;
            padding-left: 0;
            margin-left: 20px;
            font-size: 15px;
        }

        .myheaderbg {
            background-color: rgb(52 105 174 / 65%) !important;
        }
    </style>
    <link rel="stylesheet" id="weglot-css-css" href="/styles/front-css.css" type="text/css" media="all">
    <link rel="stylesheet" id="new-flag-css-css" href="/styles/new-flags.css" type="text/css" media="all">
    <link rel="stylesheet" id="launchframe-styles-css" href="/styles/site.css" type="text/css" media="all">
    <link rel="stylesheet" id="launchframe-styles-css" href="/styles/forms.css" type="text/css" media="all">

</head>

<body data-portal="realm" class="text-base antialiased" style="height: 5881px; overscroll-behavior: none; scroll-behavior: auto;" aria-live="polite">
    <div id="primary-once" class="fixed z-50 inset-0 grid grid-cols-2 w-full h-screen overflow-hidden" style="opacity: 0; visibility: hidden;">
        <div data-shadow="" class="absolute inset-0 bg-primary-900" style="opacity: 0;"></div>

        <div data-pillar-left="" class="relative bg-primary-800 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0%, 101%);">
            <div data-curtain-one="" class="absolute inset-0 w-full h-full bg-primary-600" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>

            <div class="absolute inset-0">
                <div data-logo-one="" class="ml-[-60%] w-[180%] fill-current text-secondary translate-y-over" style="translate: none; rotate: none; scale: none; transform: translate(0px, -1870px);">
                    <svg viewBox="0 0 390 327" stroke="currentColor" stroke-width="0.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" vector-effect="non-scaling-stroke" />
                    </svg>

                </div>
            </div>
        </div>

        <div data-pillar-right="" class="relative bg-primary-600 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0%, -101%);">
            <div data-curtain-two="" class="absolute inset-0 w-full h-full bg-primary-800" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>

            <div class="absolute inset-0">
                <div data-logo-two="" class="ml-[-15%] w-[200%] fill-current text-primary -translate-y-over" style="translate: none; rotate: none; scale: none; transform: translate(0px, 1998px);">
                    <svg viewBox="0 0 390 327" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" />
                    </svg>

                </div>
            </div>
        </div>
    </div>

    <div id="primary-loader" class="fixed z-50 inset-0 grid grid-cols-2 w-full h-screen overflow-hidden opacity-0 invisible">
        <div data-shadow="" class="absolute inset-0 bg-primary-900/70 opacity-0"></div>

        <div data-pillar-left="" class="relative overflow-hidden">
            <div data-curtain-one="" class="absolute inset-0 w-full h-full bg-primary-600"></div>

            <div class="absolute inset-0">
                <div data-logo-one="" class="ml-[-60%] w-[180%] translate-y-over">
                    <div data-logo-one-inner="" class="w-full fill-current text-secondary">
                        <svg viewBox="0 0 390 327" stroke="currentColor" stroke-width="0.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" vector-effect="non-scaling-stroke" />
                        </svg>

                    </div>
                </div>
            </div>
        </div>

        <div data-pillar-right="" class="relative overflow-hidden">
            <div data-curtain-two="" class="absolute inset-0 w-full h-full bg-primary-800"></div>

            <div class="absolute inset-0">
                <div data-logo-two="" class="ml-[-15%] w-[200%] -translate-y-over">
                    <div data-logo-two-inner="" class="w-full fill-current text-primary">
                        <svg viewBox="0 0 390 327" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" />
                        </svg>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="loader-bar" class="fixed z-50 inset-x-0 bottom-0 h-1 lg:h-1.5 overflow-hidden invisible opacity-0">
        <div data-progress="" class="w-full h-full bg-secondary"></div>
    </div>

    <div id="secondary-loader" class="fixed z-50 inset-0 bg-white invisible opacity-0"></div>

    <div id="tertiary-loader" class="fixed z-40 inset-0 grid grid-cols-2 w-full h-screen overflow-hidden opacity-0 invisible">
        <div data-shadow="" class="absolute inset-0 bg-primary-900/70 opacity-0"></div>

        <div class="relative overflow-hidden">
            <div data-curtain-one="" class="absolute inset-0 w-full h-full bg-primary-600"></div>
        </div>

        <div class="relative overflow-hidden">
            <div data-curtain-two="" class="absolute inset-0 w-full h-full bg-primary-800"></div>
        </div>

        <div class="absolute inset-0 grid grid-cols-2">
            <div class="flex justify-end items-center overflow-hidden">
                <div data-logo-one="" class="w-full max-w-sm fill-current text-secondary">
                    <svg viewBox="0 0 390 327" stroke="currentColor" stroke-width="0.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" vector-effect="non-scaling-stroke" />
                    </svg>

                </div>
            </div>

            <div class="flex justify-start items-center overflow-hidden">
                <div data-logo-two="" class="w-full max-w-sm fill-current text-primary">
                    <svg viewBox="0 0 390 327" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" />
                    </svg>

                </div>
            </div>
        </div>
    </div>

    <a class="fixed top-4 left-8 z-50 px-4 py-2 bg-primary rounded-lg text-white text-sm opacity-0 focus-visible:opacity-100 focus:outline-none focus-visible:ring-2 ring-primary -translate-y-24 focus-visible:translate-y-0 motion-safe:transition-transform" href="#maincontent">
        Skip to Content
    </a>
    <?php
    $myclass="text-white";
    if ($page == 'our-companies' || $page == 'our-history-2') {
        $myclass="";
    }

    // Set logo source based on page
    $logo_src = "/images/logo/site-logo.png";
    if ($page == 'leadership' || $page == 'contact') {
        $logo_src = "/images/logo/site-logo-white.png";
    }
    ?>

    <header x-data="header()" x-init="
        setTimeout(() => {
            $store.header.heightTop = $refs.header.clientHeight;
            $store.header.height = $refs.header.clientHeight;
        }, 1000);
        " x-ref="header" class="fixed z-30 top-0 w-full <?= $myclass ?>">

        <div class="bg-gradient-to-b from-primary-900 w-full h-full absolute inset-0 opactiy-80 transition duration-500 hidden">
        </div>

        <div class="absolute bottom-0 inset-x-0 overflow-hidden transition opacity-0">

            <div x-data="animate()" x-init="
                opacity.active = false;
                xPercent = {
                    ...xPercent, active: true,
                    start: -101,
                    end: 0,
                    duration: 0.8,
                    ease: 'circ.inOut',
                };

                setTrigger($refs.header);
                delay = delay + 0.3;
                mounted();
            " x-ref="element" class="relative w-full h-px bg-white/10" :class="{
                'hidden' : !$store.header.border,
                'bg-white' : $store.header.border_color === 'white',
                'bg-white/10' : $store.header.border_color === 'white_opaque',
                'bg-primary-600' : $store.header.border_color === 'primary',
                'bg-primary-800' : $store.header.border_color === 'primary_dark',
                'bg-neutral' : $store.header.border_color === 'neutral',
                'z-10' : $store.header.bg,
            }" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px); background-color: #ffffff9c;"></div>
        </div>

        <div class="absolute inset-0 transition ease-in-out-circ bg-white !bg-white" :class="{
            'bg-white' : $store.header.bg_color === 'white',
            'bg-primary-800' : $store.header.bg_color === 'primary_dark',
            'opacity-0' : !sticky && !$store.header.bg,
            '!bg-white' : sticky,
        }">
        </div>

        <div class="fluid-container relative z-10 flex" x-data="{ searchOpen: false }">
            <div class="relative flex-grow flex justify-between items-center transition-all duration-300 py-3 !text-primary-800" :class="{
                'py-6' : !sticky,
                'py-3 !text-primary-800' : sticky,
            }">

                <div x-data="animate()" x-init="
                    setTrigger($refs.header);
                    delay = delay + 0.2;
                    mounted();
                " :class="{
                    '!text-primary-600' : sticky,
                    'text-white' : $store.header.theme === 'dark',
                    'text-primary-600' : $store.header.theme === 'light',
                }" class="!text-primary-600 text-white">

                    <div x-ref="element" style="opacity: 1;">

                        <a href="/" data-portal-prevent="self" class="w-48 xl:w-64 relative block focus:outline-none transition duration-75 group" aria-label="Eastern Builders">
                            <div class="absolute inset-0 border-2 border-primary-400/60 rounded-full opacity-0 group-focus-visible:opacity-100 group-focus-visible:scale-125 transition"></div>
                            <img src="<?= $logo_src ?>" alt="Eastern Builders Logo" class="w-full h-auto" id="site-logo">
                        </a>

                    </div>
                </div>

                <div class="flex items-center space-x-6 2xl:space-x-10">

                    <div x-show="!searchOpen" x-transition:enter.opacity.duration.500ms="">
                        <nav aria-label="Primary Navigation" x-data="animate()" x-init="
                            element = [...$refs.menu.querySelectorAll('[data-nav-item]')];
                            opacity.active = false;
                            xPercent = {
                                ...xPercent, active: true,
                                start: -101,
                            };
                            stagger = 0.1;
                            setTrigger($refs.menu);
                            delay = delay + 0.2;
                            mounted();
                        " x-ref="menu">
                            <ul class="hidden lg:flex items-center space-x-6 2xl:space-x-10 main-nav">

                                <li class="relative">
                                    <div class="relative z-10 overflow-hidden">
                                        <div data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                            <a data-portal-prevent="self" class="relative text-xs font-medium uppercase tracking-wide transition duration-75 text-center leading-none block main-nav-item focus:ring-secondary focus:ring-2 lg:hover:text-primary" href="/our-history">
                                                Our History
                                            </a>
                                        </div>
                                    </div>
                                </li>

                                <li class="relative">
                                    <div class="relative z-10 overflow-hidden">
                                        <div data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                            <a data-portal-prevent="self" class="relative text-xs font-medium uppercase tracking-wide transition duration-75 text-center leading-none block main-nav-item focus:ring-secondary focus:ring-2 lg:hover:text-primary" href="/leadership">
                                                Leadership
                                            </a>
                                        </div>
                                    </div>
                                </li>

                                <li class="relative">
                                    <div class="relative z-10 overflow-hidden">
                                        <div data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                            <a data-portal-prevent="self" class="relative text-xs font-medium uppercase tracking-wide transition duration-75 text-center leading-none block main-nav-item focus:ring-secondary focus:ring-2 lg:hover:text-primary" href="/our-companies">
                                                Our Companies
                                            </a>
                                        </div>
                                    </div>
                                </li>

                                <li class="relative">
                                    <div class="relative z-10 overflow-hidden">
                                        <div data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                            <a data-portal-prevent="self" class="relative text-xs font-medium uppercase tracking-wide transition duration-75 text-center leading-none block main-nav-item focus:ring-secondary focus:ring-2 lg:hover:text-primary" href="/join-our-team">
                                                Join Our Team
                                            </a>
                                        </div>
                                    </div>
                                </li>

                                <li class="relative">
                                    <div class="relative z-10 overflow-hidden">
                                        <div data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                            <a data-portal-prevent="self" class="relative text-xs font-medium uppercase tracking-wide transition duration-75 text-center leading-none block main-nav-item focus:ring-secondary focus:ring-2 lg:hover:text-primary" href="/contact">
                                                Connect With Us
                                            </a>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </nav>
                    </div>

                </div>
            </div>

        </div>
    </header>
