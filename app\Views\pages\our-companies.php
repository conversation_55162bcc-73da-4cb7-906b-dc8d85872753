<div id="smooth-wrapper" style="inset: 0px; width: 100%; height: 100%; position: fixed; overflow: hidden;">
<div id="smooth-content" style="transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -2586.49, 0, 1); box-sizing: border-box; width: 100%; overflow: visible;">
<div data-portal="destination" data-portal-namespace="secondary" class="archive post-type-archive post-type-archive-market -mb-px">

<div x-data="" x-init="
    $store.header.bg_color = 'white';
    $store.header.theme = 'light';
    $store.header.bg = true;
    $store.header.border = true;
    $store.header.border_color = 'neutral';
    " class="hidden"></div>
<div id="maincontent"></div>
<main>
    <section x-data="{ 'showMarketGrid': false }" x-ref="hero" class="pt-20 md:pb-0 xl:pb-24">
        <div x-ref="container" class="relative xl:mb-9 py-16 overflow-hidden">
            <div class="absolute top-0 inset-x-0 overflow-hidden">
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -101,
                    end: 0,
                    duration: 0.8,
                    ease: 'quad.inOut',
                    };
                    setTrigger($refs.container);
                    delay = delay + 0.3;
                    mounted();
                    " x-ref="element" class="w-full h-px bg-neutral md:h-0 xl:h-px" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
            </div>
            <div class="absolute bottom-0 inset-x-0 overflow-hidden">
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: 101,
                    end: 0,
                    duration: 0.8,
                    ease: 'quad.inOut',
                    };
                    setTrigger($refs.container);
                    delay = delay + 0.4;
                    mounted();
                    " x-ref="element" class="w-full h-px bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
            </div>
            <div x-data="animate()" x-init="
                opacity.active = false;
                yPercent = {
                ...yPercent, active: true,
                start: 40,
                end: -20,
                ease: 'none',
                };
                trigger = $refs.container;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0">
                <div x-ref="element" class="fluid-container opacity-20" style="translate: none; rotate: none; scale: none; transform: translate(0%, -20%);">
                    <div x-data="animate()" x-init="
                        yPercent = {
                        ...yPercent, active: true,
                        start: 0,
                        end: -20,
                        duration: 1.2,
                        ease: 'quad.out',
                        };
                        setTrigger($refs.container);
                        element = $refs.amp;
                        delay = delay + 0.4;
                        mounted();
                        " x-ref="amp" class="text-primary" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0%, -20%);">
                        <svg viewBox="0 0 390 327" stroke="currentColor" stroke-width="0.5" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" vector-effect="non-scaling-stroke" />
                        </svg>
                    </div>
                </div>
            </div>
            <h1 x-data="animateText()" x-init="
                setTrigger($refs.container);
                opacity.active = false;
                yPercent = {
                ...yPercent, active: true,
                start: 101,
                };
                type = `chars`;
                stagger = 0.012;
                mounted();
                " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-balance text-4.5xl md:text-6xl lg:text-7xl relative text-center">
                <div class="overflow-hidden" style="display: block; text-align: center; position: relative;">
                    <div style="position:relative;display:inline-block;">
                        Explore
                    </div>
                    <div style="position:relative;display:inline-block;">
                        Our
                    </div>
                    <div style="position:relative;display:inline-block;">
                        Companies
                    </div>
                </div>
            </h1>
        </div>
        <div class="hidden xl:block">
            <div x-data="animate()" x-init="
                opacity.active = false;
                x = {
                ...x, active: true,
                start: '0',
                end: -window.innerWidth / 4,
                ease: 'none',
                };
                trigger = $refs.hero;
                element = $refs.marquee;
                scrub = 1.2;
                start = 'top top';
                mounted();
                " x-ref="marquee" class="mb-4 relative z-10 flex" style="translate: none; rotate: none; scale: none; transform: translate3d(-385.022px, 0px, 0px);">
                <div x-ref="scroll" class="flex gap-4">
                    <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                        ...xPercent, active: true,
                        start: 0,
                        end: -50,
                        ease: 'none',
                        duration: 3 * 20,
                        repeat: -1,
                        };
                        scrollTrigger = false;
                        mounted();
                        " x-ref="element" class="flex gap-4 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(-18.8975%, 0%) translate3d(0px, 0px, 0px);">
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Infrastructure" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/industrial.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a red arch pedestrian bridge over a busy highway intersection at dusk, symbolizing the connection between network security and our daily travels.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Industrial
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Industrial
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Industrial Construction
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 18;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/manufacturing-and-metals.html" aria-label="Manufacturing and Metals" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/commercial.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Interior of a large, modern cryogenic facility in Theodore, Alabama with high ceilings and overhead cranes">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Commercial
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Commercial
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Commercial Construction
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 17;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Mission Critical" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/education.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a large industrial complex with Brasfield & Gorrie's mission critical projects, showcasing multiple long warehouse buildings under construction, surrounded by a flat landscape and clear blue sky.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Education
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Education
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Education
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 16;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/multifamily.html" aria-label="Multifamily" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/government.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Modern multi-story multifamily housing with mixed materials and a curved facade, located at the corner of a city street.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Government
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Government
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Government & Public Safety
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 15;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Science and Technology" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/healthcare.jpg" class="absolute inset-0 w-full h-full object-cover" alt="In a Brasfield and Gorrie Science and Technology project, a laboratory bustles with five individuals in white lab coats, each diligently working at various stations surrounded by shelves and tables.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Healthcare
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Healthcare
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Healthcare
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 14;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Senior Living" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/hospitality.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Modern multi-story senior living building at twilight with illuminated windows and surrounding cityscape in the distance.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Hospitality
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Hospitality
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Hospitality
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 13;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/site-civil.html" aria-label="Site/Civil" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/religious.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a construction site with heavy machinery, featuring dirt roads being built and the ground excavated, showcasing an auto draft process in action.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Religious
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Religious
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Religious & Community Facilities
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 12;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/sports-and-entertainment.html" aria-label="Sports and Entertainment" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/family.jpg" class="absolute inset-0 w-full h-full object-cover" alt="A vintage photo of the empty baseball field at Truist Park.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Multi-Family
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Multi-Family
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Multi-Family & Senior Living
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 11;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/water.html" aria-label="Water" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/renewable.jpg" class="absolute inset-0 w-full h-full object-cover" aria-hidden="true">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Renewable
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                View
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Renewable Energy Development
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 10;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-data="animate()" x-init="
                opacity.active = false;
                x = {
                ...x, active: true,
                start: -$refs.scroll.offsetWidth + window.innerWidth,
                end: -$refs.scroll.offsetWidth + window.innerWidth*1.5,
                ease: 'none',
                };
                trigger = $refs.hero;
                element = $refs.marquee;
                scrub = 1.2;
                start = 'top top';
                mounted();
                " x-ref="marquee" class="relative z-10 flex" style="translate: none; rotate: none; scale: none; transform: translate3d(-9109.96px, 0px, 0px);">
                <div x-ref="scroll" class="flex gap-4">
                    <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                        ...xPercent, active: true,
                        start: 0,
                        end: 50,
                        ease: 'none',
                        duration: 3 * 18,
                        repeat: -1,
                        };
                        scrollTrigger = false;
                        mounted();
                        " x-ref="element" class="flex gap-4 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(20.9972%, 0%) translate3d(0px, 0px, 0px);">
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Infrastructure" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                                <img src="/images/work/industrial.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a red arch pedestrian bridge over a busy highway intersection at dusk, symbolizing the connection between network security and our daily travels.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Industrial
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Industrial
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Industrial Construction
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 18;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/manufacturing-and-metals.html" aria-label="Manufacturing and Metals" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/commercial.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Interior of a large, modern cryogenic facility in Theodore, Alabama with high ceilings and overhead cranes">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Commercial
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Commercial
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Commercial Construction
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 17;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Mission Critical" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/education.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a large industrial complex with Brasfield & Gorrie's mission critical projects, showcasing multiple long warehouse buildings under construction, surrounded by a flat landscape and clear blue sky.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Education
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Education
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Education
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 16;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/multifamily.html" aria-label="Multifamily" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/government.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Modern multi-story multifamily housing with mixed materials and a curved facade, located at the corner of a city street.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Government
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Government
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Government & Public Safety
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 15;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Science and Technology" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/healthcare.jpg" class="absolute inset-0 w-full h-full object-cover" alt="In a Brasfield and Gorrie Science and Technology project, a laboratory bustles with five individuals in white lab coats, each diligently working at various stations surrounded by shelves and tables.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Healthcare
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Healthcare
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Healthcare
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 14;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Senior Living" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/hospitality.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Modern multi-story senior living building at twilight with illuminated windows and surrounding cityscape in the distance.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Hospitality
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Hospitality
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Hospitality
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 13;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="#" aria-label="Site/Civil" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/religious.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a construction site with heavy machinery, featuring dirt roads being built and the ground excavated, showcasing an auto draft process in action.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Religious
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Religious
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Religious & Community Facilities
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 12;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/sports-and-entertainment.html" aria-label="Sports and Entertainment" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/family.jpg" class="absolute inset-0 w-full h-full object-cover" alt="A vintage photo of the empty baseball field at Truist Park.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Multi-Family
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Multi-Family
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Multi-Family & Senior Living
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 11;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                        <div x-data="{
                            hovered: false,
                            }" class="relative">
                            <a x-data="magnetic()" @mousemove="
                            magnetize($event, $el, $refs.button, -16);
                            magnetize($event, $el, $refs.card, 16);
                            magnetize($event, $el, $refs.view, -20);
                            " @mouseenter="
                            if (!ScrollTrigger.isTouch) {
                            hovered = true;
                            }
                            " @mouseleave="
                            demagnetize($refs.button);
                            demagnetize($refs.card);
                            demagnetize($refs.view);
                            if (!ScrollTrigger.isTouch) {
                            hovered = false;
                            }
                            " href="./html/water.html" aria-label="Water" class="relative flex-shrink-0 flex justify-center items-center w-[70vw] lg:w-[40vw] ultrawide:w-[30vw] aspect-63/50 sm:aspect-151/100 focus:outline-none overflow-hidedn group">
                            <div x-ref="card" class="absolute inset-0">
                                <div class="absolute inset-0 overflow-hidden group-hover:scale-[95%] transition duration-500 ease-in-out-circ">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.18,
                                        end: 1.18,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="container" class="absolute inset-0 overflow-hidden group-hover:scale-110 transition duration-500 ease-in-out-circ">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.18, 1.18);">
                                            <picture>
                                                <img src="/images/work/renewable.jpg" class="absolute inset-0 w-full h-full object-cover" aria-hidden="true">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="project-card-gradient absolute inset-0 z-10 w-full h-full opacity-0 group-hover:opacity-100 transition"></div>
                                </div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-center">
                                <div x-ref="button">
                                    <div class="relative flex items-center justify-center w-36 h-36 rounded-full transition">
                                        <div class="absolute inset-0 w-full h-full border border-secondary rounded-full opacity-0 group-hover:opacity-100 scale-150 group-hover:scale-100 transition"></div>
                                        <div x-ref="view" class="relative select-none overflow-hidden">
                                            <div x-show="hovered" x-transition:enter="transition-transform duration-300" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center" style="display: none;">
                                                <p class="text-white relative text-center text-xl font-medium uppercase tracking-widest">
                                                    Renewable
                                                </p>
                                            </div>
                                            <p class="text-current invisible opacity-0 text-xl font-medium uppercase tracking-widest">
                                                Renewable
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-white font-semibold relative z-10 px-8 text-center leading-tight group-hover:opacity-0 group-hover:scale-90 group-hover:translate-y-1/4 transition text-4.5xl sm:text-5.5xl">
                                Renewable Energy Development
                            </p>
                            </a>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                yPercent = {
                                ...yPercent, active: true,
                                start: 0,
                                end: 110,
                                duration: 0.8,
                                ease: 'expo.inOut',
                                };
                                setTrigger($refs.container);
                                element = $refs.cover;
                                delay = delay + 0.2 * 10;
                                mounted();
                                " x-ref="cover" class="absolute z-10 inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);">
                                <div class="absolute z-10 inset-0 bg-white scale-105"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="bg-neutral pb-11 sm:pb-18">
        <?php echo view('pages/_companies'); ?>
    </div>
</main>
<script>
$(document).ready(function() {
  // Set the initial src
  $("#site-logo").attr("src", "/images/logo/site-logo.png");
  
  // Add event listener to catch property changes
  $("#site-logo").on("load error", function() {
    if ($(this).attr("src") !== "/images/logo/site-logo.png") {
      $(this).attr("src", "/images/logo/site-logo.png");
    }
  });
});
</script>