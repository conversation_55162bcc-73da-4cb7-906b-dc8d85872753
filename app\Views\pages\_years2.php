<section x-data="" class="-mt-20 md:-mt-24 lg:-mt-24">
    <div class="overflow-hidden pt-14 pb-8 xl:py-20 gray-mode-block bg-neutral" x-data="sliders()" x-init="
        carouselSliderHash = '384642784';
        isCarouselLandscape = 'false' == 'true';
        carouselSlideLength = 4;
        mounted();
        ">
        <div class="fluid-container max-w-none hidden lg:block">
            <div class="border-t border-black border-opacity-10"></div>
        </div>
        <div class="lg:flex justify-between">
            <div class="lg:w-1/3 flex-shrink-0 sm:mb-9 lg:mb-0 fluid-container lg:pr-0 lg:mr-0 lg:max-w-none lg:border-r border-black border-opacity-10 relative" x-ref="intro">
                <div class="lg:pr-8 lg:mt-14">
                    <h2 class="text-primary-600 font-sans leading-none font-semibold text-4.5xl sm:text-5.5xl">
                        Our Dynamic Companies
                    </h2>
                    <p class="text-primary-800 mt-4 text-base leading-relaxed font-normal">
                        Our story is about the outstanding results that come from hard work, bold vision, and perseverance.
                    </p>
                    <div class="hidden lg:flex justify-start gap-4 mt-14 lg:mb-8">
                        <button class="carousel-slider-prev-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" title="Previous">
                            <span class="text-primary w-4 h-4">
                                <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                </svg>
                            </span>
                        </button>
                        <button class="carousel-slider-next-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary" title="Next">
                            <span class="text-primary w-4 h-4">
                                <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="sm:border-t lg:border-none border-black border-opacity-10">
                <div class="swiper carousel-slider-384642784 w-screen lg:max-w-[72.6vw] !mr-0 flex swiper-initialized swiper-horizontal swiper-pointer-events swiper-autoheight swiper-backface-hidden">
                    <div class="swiper-wrapper" x-init="
                        $nextTick(() => {
                        let maxHeight = 0;
                        Array.from($el.children).forEach((e) => {
                        const height = e.getBoundingClientRect().height;
                        maxHeight = height > maxHeight ? height : maxHeight;
                        });
                        const introHeight = $refs.intro.getBoundingClientRect().height;
                        $el.style.height = introHeight > maxHeight && $store.getBreakpoint.isDesktop ? `${introHeight}px` : `${maxHeight}px`;
                        });
                        " style="transition-duration: 0ms; transform: translate3d(-920.727px, 0px, 0px); height: 535px;">
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-duplicate-next" data-swiper-slide-index="1" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Glass and Aluminum/EGA
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Evolving from a metal roofing company that began almost 25 years ago to being a sought-after specialist in building envelope systems, EGA is the go-to contractor for countless clients and contractor partners.</B>With vast experience gained from our portfolio of completed projects, we deliver comprehensive envelope design assistance such as waterproofing, roofing, wall panels, and glass/glazing systems.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate" data-swiper-slide-index="2" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Contractors Corporation
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Eastern Contractors Corporation focuses on industrial building throughout the Southeastern United States and wherever our clients or partners take us.</b>
                                        Having installed roofing and metal panels for many industrial and commercial clients, the 2018 expansion of Hyundai in Montgomery, AL allowed Eastern to showcase its full industrial contracting capabilities. Next, we successfully executed the $200M for SK Battery project in Commerce, GA as general contractor.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-prev" data-swiper-slide-index="3" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 3 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Builders
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Seeking continued growth and diversification, it was time to expand beyond industrial clients.</b>
                                        As a result, Peter’s next move was to seek opportunities in commercial construction. The commercial business unit was created and called iugis, meaning dependable, sure, and trustworthy. iugis focuses on the commercial and institutional side of the construction business such as education, healthcare, housing, hospitality, and more.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-active" data-swiper-slide-index="0" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-transparent" :class="carouselActiveIndex == 0 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Power<br>Solutions
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Recognizing and anticipating the ever-growing renewable energy market, Eastern Companies created Eastern Power Solutions to meet the demand of the renewable clean energy market.</b>
                                        EPS is devoted to helping its clients “go green” by developing and maintaining successful utility-grade solar projects located in various environmental terrains, while ensuring control over safety, quality, timeliness, and productivity. Experienced in solar power generation, EPS is making significant headway in the renewable energy markets.
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-next" data-swiper-slide-index="1" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Glass and Aluminum/EGA
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Evolving from a metal roofing company that began almost 25 years ago to being a sought-after specialist in building envelope systems, EGA is the go-to contractor for countless clients and contractor partners.</B>With vast experience gained from our portfolio of completed projects, we deliver comprehensive envelope design assistance such as waterproofing, roofing, wall panels, and glass/glazing systems.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto" data-swiper-slide-index="2" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Builders Construction
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Seeking continued growth and diversification, it was time to expand beyond industrial clients.</b>
                                        As a result, Peter’s next move was to seek opportunities in commercial construction. The commercial business unit was created and called iugis, meaning dependable, sure, and trustworthy. iugis focuses on the commercial and institutional side of the construction business such as education, healthcare, housing, hospitality, and more.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate-prev" data-swiper-slide-index="3" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 3 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Eastern Contractors Corporation
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Eastern Contractors Corporation focuses on industrial building throughout the Southeastern United States and wherever our clients or partners take us.</b>
                                        Having installed roofing and metal panels for many industrial and commercial clients, the 2018 expansion of Hyundai in Montgomery, AL allowed Eastern to showcase its full industrial contracting capabilities. Next, we successfully executed the $200M for SK Battery project in Commerce, GA as general contractor.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-duplicate-active" data-swiper-slide-index="0" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-transparent" :class="carouselActiveIndex == 0 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Project programming services
                                        </p>
                                    </div>
                                    <p  class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>We fast-track project success by comparing your plan against thousands of similar projects. </b>
                                        This information allows us to build a budget and plan that fits your needs, translates easily into design, and reduces costly rework.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-duplicate-next" data-swiper-slide-index="1" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Design management
                                        </p>
                                    </div>
                                    <p  class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>The earlier we get involved, the better the outcome—and design is where it all begins.</b>
                                        Using tried and true methods like integrated project delivery and design-build, we operate at a higher level and deliver a streamlined, stress-free process with your goals as our guide.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate" data-swiper-slide-index="2" style="width: 306.909px;">
                            <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-2">
                                    <div>
                                        <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                            Engineering services
                                        </p>
                                    </div>
                                    <p class="text-current transition text-sm leading-relaxed text-primary-800">
                                        <b>Our engineers are problem solvers who come up with creative construction solutions and lead design-assist and design-build efforts across a range of projects.</b>
                                        By working hand-in-hand with our construction teams, they’re uniquely empowered to offer innovative solutions that others can’t.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="border-t border-black border-opacity-10 hidden sm:block lg:hidden"></div>
        <div class="fluid-container max-w-none hidden lg:block">
            <div class="border-t border-black border-opacity-10"></div>
        </div>
        <div class="fluid-container flex lg:hidden justify-between gap-4 sm:mt-9">
            <button class="carousel-slider-prev-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" title="Previous">
                <span class="text-primary w-4 h-4">
                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                    </svg>
                </span>
            </button>
            <button class="carousel-slider-next-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary" title="Next">
                <span class="text-primary w-4 h-4">
                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                    </svg>
                </span>
            </button>
        </div>
    </div>
</section>