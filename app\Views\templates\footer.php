<footer x-data="" x-ref="footer" class="relative flex w-full min-h-sm pt-16 sm:pt-20 pb-8 sm:pb-12 text-white overflow-hidden">
    <div class="absolute inset-x-0 top-0 h-[110%] bg-primary-800"></div>

    <div class="absolute bottom-0 inset-x-0 h-full">
        <div class="fluid-container sm:mt-48 h-full mt-[90%]">
            <div class="lg:w-3/4 translate-x-[-8%] relative w-[115%] sm:w-4/5">
                <div x-data="animate()" x-init="
                        opacity.active = false;

                        yPercent = {
                            ...yPercent, active: true,
                            start: 25,
                            end: 0,
                            ease: 'none',
                        };

                        trigger = $refs.footer;
                        scrub = 1.2;
                        start = 'top bottom';
                        end = 'bottom bottom';
                        mounted();
                    " x-ref="element" class="absolute top-0 inset-x-0 text-primary-600" style="translate: none; rotate: none; scale: none; transform: translate(0%, 3.858%) translate3d(0px, 0px, 0px);">
                    <img src="/images/site-graphic.png" alt="Eastern Builders Logo" class="w-full h-auto">

                </div>
            </div>
        </div>
    </div>

    <div class="absolute bottom-0 inset-x-0 h-3/4 bg-gradient-to-t from-primary-800 to-transparent"></div>

    <div class="fluid-container relative flex flex-col justify-between w-full">
        <div class="flex flex-col sm:flex-row gap-16 sm:gap-5 justify-between mb-32">
            <div x-data="animate()" x-init="
                    setTrigger($refs.footer);
                    trigger = $refs.footer;
                    delay = delay + 0.2;
                    mounted();
                ">

                <div x-ref="element" style="opacity: 0.2222;">

                    <a href="/" class="w-36 relative block focus:outline-none transition duration-75 group" aria-label="Brasfield & Gorrie">
                        <div class="absolute inset-0 border-2 border-primary-400/60 rounded-full opacity-0 group-focus-visible:opacity-100 group-focus-visible:scale-125 transition"></div>
                        <img src="/images/logo/site-logo-white.png" alt="Eastern Builders Logo" class="w-full h-auto" style="min-width:300px">

                    </a>

                </div>
            </div>

            <div class="flex gap-12 xl:gap-24">

                <nav aria-label="Footer Navigation" x-data="animate()" x-init="
        element = [...$refs.menu.querySelectorAll('[data-nav-item]')];
        opacity.active = false;
        xPercent = {
            ...xPercent, active: true,
            start: -101,
        };
        stagger = 0.05;
        setTrigger($refs.menu);
        delay = delay + 0.2;
        mounted();
    " x-ref="menu" class="flex gap-6">
                    <ul class="flex flex-col gap-4 overflow-hidden">

                        <li data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0, 0%) translate3d(0px, 0px, 0px);">
                            <a class="relative lg:hover:text-secondary focus-visible:text-primary-400 focus:outline-none text-base lg:text-sm leading-none transition duration-200" href="/our-history">
                                Our History
                            </a>
                        </li>

                        <li data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%) translate3d(0px, 0px, 0px);">
                            <a class="relative lg:hover:text-secondary focus-visible:text-primary-400 focus:outline-none text-base lg:text-sm leading-none transition duration-200" href="/leadership">
                                Leadership
                            </a>
                        </li>

                        <li data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
                            <a class="relative lg:hover:text-secondary focus-visible:text-primary-400 focus:outline-none text-base lg:text-sm leading-none transition duration-200" href="/our-companies">
                                Our Companinies
                            </a>
                        </li>

                        <li data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
                            <a class="relative lg:hover:text-secondary focus-visible:text-primary-400 focus:outline-none text-base lg:text-sm leading-none transition duration-200" href="/join-our-team">
                                Join Our Team
                            </a>
                        </li>

                        <li data-nav-item="" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
                            <a data-portal-prevent="self" class="relative lg:hover:text-secondary focus-visible:text-primary-400 focus:outline-none text-base lg:text-sm leading-none transition duration-200" href="/contact">
                                Contact Us
                            </a>
                        </li>

                    </ul>

                </nav>
            </div>
        </div>

        <div class="flex flex-col-reverse md:flex-row items-center md:items-start md:justify-between gap-5">
            <p x-data="animateText()" x-init="
                        setTrigger($refs.footer);
                        trigger = $refs.footer;
                        delay = delay + 0.6;
                        stagger = 0.02;
                        mounted();
                    " x-ref="element" class="text-current m-0 text-sm leading-relaxed">© Eastern Builders 2025. All rights reserved.</p>
            <div x-data="animate()" x-init="
        setTrigger($refs.socials);
        element = $refs.socials;
        start = 'top bottom';
        delay = delay + 0.2;
        mounted();
    " x-ref="socials" class="flex items-center space-x-8 xl:space-x-4" style="opacity: 0;">

                <a href="#" target="_blank" class="w-6 block focus:outline-none lg:hover:text-secondary focus-visible:text-primary-400 transition-200">
                    <svg class="w-full fill-current" viewBox="0 0 512 512">
                        <path d="M288,192v-38.1c0-17.2,3.8-25.9,30.5-25.9H352V64h-55.9c-68.5,0-91.1,31.4-91.1,85.3V192h-45v64h45v192h83V256h56.4l7.6-64
    H288z" />
                    </svg>

                </a>




                <a href="#" target="_blank" class="w-5 block focus:outline-none lg:hover:text-secondary focus-visible:text-primary-400 transition-200">
                    <svg viewBox="0 0 25 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <title>YouTube</title>
                        <path d="M24.1859 2.99711C24.0488 2.52364 23.7801 2.09186 23.4065 1.74499C23.0329 1.39811 22.5677 1.14831 22.0573 1.02059C20.1787 0.552002 12.648 0.552002 12.648 0.552002C12.648 0.552002 5.11725 0.552002 3.2386 1.01809C2.72801 1.14541 2.26252 1.39507 1.8889 1.74201C1.51527 2.08894 1.24664 2.52094 1.11001 2.99462C0.607422 4.73935 0.607422 8.37835 0.607422 8.37835C0.607422 8.37835 0.607422 12.0174 1.11001 13.7596C1.38683 14.7217 2.20387 15.4794 3.2386 15.7361C5.11725 16.2047 12.648 16.2047 12.648 16.2047C12.648 16.2047 20.1787 16.2047 22.0573 15.7361C23.0947 15.4794 23.9091 14.7217 24.1859 13.7596C24.6885 12.0174 24.6885 8.37835 24.6885 8.37835C24.6885 8.37835 24.6885 4.73935 24.1859 2.99711ZM10.256 11.7183V5.03845L16.4913 8.35343L10.256 11.7183Z" fill="currentColor" />
                    </svg>

                </a>


                <a href="#" target="_blank" class="w-5 block focus:outline-none lg:hover:text-secondary focus-visible:text-primary-400 transition-200">
                    <svg class="w-full fill-current" viewBox="0 0 44 40" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(-702.000000, -265.000000)">
                            <path d="M746,305 L736.2754,305 L736.2754,290.9384 C736.2754,287.257796 734.754233,284.74515 731.409219,284.74515 C728.850659,284.74515 727.427799,286.440738 726.765522,288.074854 C726.517168,288.661395 726.555974,289.478453 726.555974,290.295511 L726.555974,305 L716.921919,305 C716.921919,305 717.046096,280.091247 716.921919,277.827047 L726.555974,277.827047 L726.555974,282.091631 C727.125118,280.226996 730.203669,277.565794 735.116416,277.565794 C741.21143,277.565794 746,281.474355 746,289.890824 L746,305 L746,305 Z M707.17921,274.428187 L707.117121,274.428187 C704.0127,274.428187 702,272.350964 702,269.717936 C702,267.033681 704.072201,265 707.238711,265 C710.402634,265 712.348071,267.028559 712.41016,269.710252 C712.41016,272.34328 710.402634,274.428187 707.17921,274.428187 L707.17921,274.428187 L707.17921,274.428187 Z M703.109831,277.827047 L711.685795,277.827047 L711.685795,305 L703.109831,305 L703.109831,277.827047 L703.109831,277.827047 Z" />
                        </g>
                    </svg>

                </a>


                <a href="#" target="_blank" class="w-5 block focus:outline-none lg:hover:text-secondary focus-visible:text-primary-400 transition-200">
                    <svg viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <title>Instagram</title>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.32959 1.01706C7.44116 0.965885 7.79558 0.954514 10.6271 0.954514C13.4586 0.954514 13.8131 0.966833 14.9237 1.01706C16.0343 1.06728 16.7924 1.24449 17.4558 1.5013C18.1504 1.76379 18.7806 2.17412 19.3018 2.7048C19.8324 3.22505 20.2418 3.85428 20.5034 4.54984C20.7611 5.21319 20.9374 5.97129 20.9886 7.08003C21.0397 8.1935 21.0511 8.54791 21.0511 11.3785C21.0511 14.21 21.0388 14.5645 20.9886 15.676C20.9383 16.7848 20.7611 17.5429 20.5034 18.2062C20.2418 18.9019 19.8318 19.5321 19.3018 20.0532C18.7806 20.5838 18.1504 20.9932 17.4558 21.2548C16.7924 21.5125 16.0343 21.6888 14.9256 21.7399C13.8131 21.7911 13.4586 21.8025 10.6271 21.8025C7.79558 21.8025 7.44116 21.7902 6.32959 21.7399C5.22085 21.6897 4.46275 21.5125 3.7994 21.2548C3.10376 20.9932 2.47349 20.5831 1.95246 20.0532C1.42214 19.5326 1.01176 18.9026 0.749911 18.2072C0.493101 17.5438 0.316841 16.7857 0.265669 15.677C0.214497 14.5635 0.203125 14.2091 0.203125 11.3785C0.203125 8.54697 0.215444 8.19255 0.265669 7.08192C0.315894 5.97129 0.493101 5.21319 0.749911 4.54984C1.01214 3.85435 1.42284 3.2244 1.95341 2.70385C2.4737 2.17365 3.10334 1.76327 3.79845 1.5013C4.4618 1.24449 5.21991 1.06823 6.32864 1.01706H6.32959ZM14.8394 2.89338C13.7401 2.84315 13.4103 2.83273 10.6271 2.83273C7.84391 2.83273 7.51413 2.84315 6.41487 2.89338C5.39806 2.93981 4.84654 3.10944 4.47886 3.25253C3.99272 3.44206 3.64494 3.66665 3.2801 4.03149C2.93425 4.36794 2.6681 4.77754 2.50114 5.23024C2.35805 5.59793 2.18842 6.14945 2.14199 7.16626C2.09176 8.26552 2.08134 8.5953 2.08134 11.3785C2.08134 14.1617 2.09176 14.4915 2.14199 15.5907C2.18842 16.6076 2.35805 17.1591 2.50114 17.5268C2.66792 17.9788 2.93421 18.3891 3.2801 18.7255C3.61651 19.0714 4.02683 19.3377 4.47886 19.5045C4.84654 19.6476 5.39806 19.8172 6.41487 19.8636C7.51413 19.9139 7.84296 19.9243 10.6271 19.9243C13.4113 19.9243 13.7401 19.9139 14.8394 19.8636C15.8562 19.8172 16.4077 19.6476 16.7754 19.5045C17.2615 19.3149 17.6093 19.0904 17.9741 18.7255C18.32 18.3891 18.5863 17.9788 18.7531 17.5268C18.8962 17.1591 19.0658 16.6076 19.1122 15.5907C19.1625 14.4915 19.1729 14.1617 19.1729 11.3785C19.1729 8.5953 19.1625 8.26552 19.1122 7.16626C19.0658 6.14945 18.8962 5.59793 18.7531 5.23024C18.5636 4.74411 18.339 4.39633 17.9741 4.03149C17.6376 3.68567 17.2281 3.41951 16.7754 3.25253C16.4077 3.10944 15.8562 2.93981 14.8394 2.89338ZM9.29569 14.5919C10.0393 14.9015 10.8672 14.9432 11.6382 14.7101C12.4091 14.477 13.0752 13.9835 13.5227 13.3138C13.9702 12.6441 14.1713 11.8399 14.0917 11.0384C14.0121 10.2369 13.6567 9.48794 13.0862 8.91939C12.7226 8.55595 12.2828 8.27766 11.7987 8.10456C11.3146 7.93146 10.7981 7.86785 10.2864 7.91832C9.77477 7.96879 9.28067 8.13207 8.83969 8.39642C8.39871 8.66076 8.02182 9.0196 7.73616 9.44708C7.45051 9.87457 7.26319 10.3601 7.18769 10.8686C7.11219 11.3772 7.15038 11.8962 7.29953 12.3882C7.44868 12.8803 7.70506 13.3331 8.05023 13.7142C8.39539 14.0952 8.82075 14.395 9.29569 14.5919ZM6.83847 7.58986C7.336 7.09232 7.92666 6.69766 8.57671 6.4284C9.22677 6.15913 9.9235 6.02055 10.6271 6.02055C11.3307 6.02055 12.0275 6.15913 12.6775 6.4284C13.3276 6.69766 13.9182 7.09232 14.4158 7.58986C14.9133 8.08739 15.308 8.67804 15.5772 9.3281C15.8465 9.97816 15.9851 10.6749 15.9851 11.3785C15.9851 12.0821 15.8465 12.7788 15.5772 13.4289C15.308 14.079 14.9133 14.6696 14.4158 15.1671C13.4109 16.172 12.0481 16.7365 10.6271 16.7365C9.2061 16.7365 7.84328 16.172 6.83847 15.1671C5.83366 14.1623 5.26916 12.7995 5.26916 11.3785C5.26916 9.95748 5.83366 8.59467 6.83847 7.58986ZM17.1734 6.81848C17.2967 6.70218 17.3954 6.56232 17.4636 6.40718C17.5319 6.25205 17.5684 6.08479 17.5708 5.91532C17.5733 5.74584 17.5417 5.5776 17.478 5.42054C17.4143 5.26348 17.3197 5.1208 17.1999 5.00095C17.08 4.8811 16.9373 4.78652 16.7803 4.7228C16.6232 4.65908 16.455 4.62753 16.2855 4.63C16.116 4.63247 15.9488 4.66892 15.7936 4.73719C15.6385 4.80546 15.4987 4.90416 15.3823 5.02745C15.1562 5.26723 15.0323 5.58572 15.0371 5.91532C15.0419 6.24491 15.175 6.55966 15.4081 6.79275C15.6412 7.02583 15.9559 7.1589 16.2855 7.1637C16.6151 7.16851 16.9336 7.04467 17.1734 6.81848Z" fill="currentColor" />
                    </svg>

                </a>


            </div>
        </div>
    </div>
</footer>

<div x-data="{
        offset: '-70px',

        init() {
            if (!document.getElementById('current-dev')) return
            this.$watch('avalanche.loaded', value => {
                if (value) {
                    smoother.scrollTo('#current-dev', true, this.offset)
                }
            })
        },
    }"></div>
</div>
</div>
</div>
<script type="text/javascript" src="/scripts/site.js"></script>
</html>