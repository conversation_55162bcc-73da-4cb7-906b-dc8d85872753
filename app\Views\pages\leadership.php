<div id="smooth-wrapper" style="inset: 0px; width: 100%; height: 100%; position: fixed; overflow: hidden;">
<div id="smooth-content" style="transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -9535.37, 0, 1); box-sizing: border-box; width: 100%; overflow: visible;">
<div data-portal="destination" data-portal-namespace="secondary" class="page-template-default page page-id-13 page-parent -mb-px">
<div x-data="" x-init="
    $store.header.bg_color = 'white';
    $store.header.theme = 'dark';
    $store.header.bg = false;
    $store.header.border_color = 'primary_dark';
    $store.header.border = true;
    " class="hidden"></div>
<div id="maincontent"></div>
<section x-data="" x-ref="hero" class="text-white relative pt-32 md:pt-36 -mb-8 md:-mb-40">
    <div class="bg-primary-600 w-full absolute -top-px inset-x-0 h-full"></div>
    <div x-data="animate()" x-init="
        mm.add(avalanche.breakpoint(avalanche.screens.md), () => {
        element = $refs.content;
        opacity.active = false;
        yPercent = {
        ...yPercent, active: true,
        start: 0,
        end: 0,
        ease: 'none',
        };
        trigger = $refs.hero;
        scrub = 1;
        start = 'top top';
        mounted();
        });
        " x-ref="content" class="relative pb-16 sm:pb-20" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
        <div class="fluid-container">
            <div class="max-w-3xl">
                <p x-data="animateText()" x-init="
                    xPercent = {
                    ...xPercent, active: true,
                    start: -100,
                    duration: 0.4,
                    };
                    scale = {
                    ...scale, active: true,
                    start: 0.5,
                    duration: 0.4,
                    };
                    type = `chars`;
                    stagger = 0.03;
                    setTrigger($refs.hero);
                    trigger = $refs.hero;
                    mounted();
                    " x-ref="element" class="text-secondary mb-3 text-base font-medium uppercase tracking-widest"></p>
                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                    <div style="position:relative;display:inline-block;">
                        Our Team
                    </div>
                </div>
                <p></p>
                <h1 x-data="fadeIn()" x-init="
                    scrollTrigger = false;
                    delay = avalanche.delay.enter + 0.4;
                    mounted();
                    " x-ref="element" class="text-current font-sans leading-none font-semibold text-balance text-4.5xl md:text-6xl lg:text-7xl" style="opacity: 1;">
                    Leadership that Builds with Purpose
                </h1>
                <p x-data="fadeIn()" x-init="
                    scrollTrigger = false;
                    delay = avalanche.delay.enter + 0.4;
                    mounted();
                    " x-ref="element" class="text-current font-medium mt-4 md:mt-6 text-lg leading-relaxed" style="opacity: 1;">
                    “It isn't what you do, but how you do it.” - John Wooden
                </p>
            </div>
        </div>
    </div>
    <div x-data="animate()" x-init="
        mm.add(avalanche.breakpoint(avalanche.screens.md), () => {
        element = $refs.gallery;
        opacity.active = false;
        yPercent = {
        ...yPercent, active: true,
        start: 0,
        end: 0,
        ease: 'none',
        };
        trigger = $refs.hero;
        scrub = 1;
        start = 'top top';
        mounted();
        });
        " x-ref="gallery" class="relative z-10" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
        <div class="mt-0 overflow-hidden" x-data="sliders()" x-init="
            gallerySpeed = 2000;
            galleryCarouselHash = '1840276718';
            mounted();
            " x-ref="slider">
            <div x-data="animate()" x-init="
                element = [...$refs.slider.querySelectorAll('[data-slide-cover]')];
                opacity.active = false;
                yPercent = {
                ...yPercent, active: true,
                start: 0,
                end: 110,
                duration: 0.8,
                ease: 'expo.inOut',
                };
                setTrigger($refs.slider);
                stagger = 0.1;
                delay = delay + 0;
                mounted();
                " class="swiper gallery-carousel-1840276718 w-screen swiper-initialized swiper-horizontal swiper-pointer-events">
                <?php echo view('pages/_swiper'); ?>
            </div>
            <div class="flex gap-10 items-center justify-center relative my-8 md:mb-16 fluid-container">
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -101,
                    end: 0,
                    duration: 0.3,
                    };
                    setTrigger($refs.content);
                    delay = delay + 0.5;
                    start = 'top bottom';
                    mounted();
                    " x-ref="element" class="hidden sm:block" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                    <div x-data="magnetic()" class="relative inline-flex hover:z-10">
                        <button x-data="{
                        hovered: false,
                        }" @mousemove="
                        magnetize($event, $el, $refs.arrow, -12);
                        magnetize($event, $el, $refs.box);
                        " @mouseenter="
                        if (!ScrollTrigger.isTouch) {
                        hovered = true;
                        }
                        " @mouseleave="
                        demagnetize($refs.arrow);
                        demagnetize($refs.box);
                        if (!ScrollTrigger.isTouch) {
                        hovered = false;
                        }
                        " x-ref="box" class="gallery-carousel-prev-1840276718 relative w-14 h-14 focus:outline-none group" aria-label="Previous">
                        <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                        <div class="absolute inset-0 border-2 transition-all border-primary/40" :class="{
                            'border-primary/40' : !hovered,
                            'border-4 border-secondary scale-90' : hovered,
                            }"></div>
                        <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                            'opacity-0 scale-50' : !hovered,
                            'opacity-40 scale-100' : hovered,
                            }"></div>
                        <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                            <div class="-scale-x-100">
                                <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                    <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary relative w-full">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                    <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary/40 absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <div class="swiper-pagination-gallery-carousel-1840276718 items-center justify-center flex-wrap gap-5 h-6 flex swiper-pagination-clickable swiper-pagination-bullets swiper-pagination-horizontal"><button class="swiper-pagination-bullet" title="Slide 1"></button><button class="swiper-pagination-bullet" title="Slide 2"></button><button class="swiper-pagination-bullet" title="Slide 3"></button><button class="swiper-pagination-bullet" title="Slide 4"></button><button class="swiper-pagination-bullet" title="Slide 5"></button><button class="swiper-pagination-bullet" title="Slide 6"></button><button class="swiper-pagination-bullet swiper-pagination-bullet-active" title="Slide 7"></button></div>
                </div>
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: 101,
                    end: 0,
                    duration: 0.3,
                    };
                    setTrigger($refs.content);
                    delay = delay + 0.5;
                    start = 'top bottom';
                    mounted();
                    " x-ref="element" class="hidden sm:block" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                    <div x-data="magnetic()" class="relative inline-flex hover:z-10">
                        <button x-data="{
                        hovered: false,
                        }" @mousemove="
                        magnetize($event, $el, $refs.arrow, -12);
                        magnetize($event, $el, $refs.box);
                        " @mouseenter="
                        if (!ScrollTrigger.isTouch) {
                        hovered = true;
                        }
                        " @mouseleave="
                        demagnetize($refs.arrow);
                        demagnetize($refs.box);
                        if (!ScrollTrigger.isTouch) {
                        hovered = false;
                        }
                        " x-ref="box" class="gallery-carousel-next-1840276718 relative w-14 h-14 focus:outline-none group" aria-label="Next">
                        <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                        <div class="absolute inset-0 border-2 transition-all border-primary/40" :class="{
                            'border-primary/40' : !hovered,
                            'border-4 border-secondary scale-90' : hovered,
                            }"></div>
                        <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                            'opacity-0 scale-50' : !hovered,
                            'opacity-40 scale-100' : hovered,
                            }"></div>
                        <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                            <div>
                                <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                    <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary relative w-full">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                    <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary/40 absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex sm:hidden gap-5 items-center justify-center mt-8 sm:mt-14 fluid-container">
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -101,
                    end: 0,
                    duration: 0.3,
                    };
                    setTrigger($refs.content);
                    delay = delay + 0.5;
                    start = 'top bottom';
                    mounted();
                    " x-ref="element" class="sm:hidden" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                    <div x-data="magnetic()" class="relative inline-flex hover:z-10">
                        <button x-data="{
                        hovered: false,
                        }" @mousemove="
                        magnetize($event, $el, $refs.arrow, -12);
                        magnetize($event, $el, $refs.box);
                        " @mouseenter="
                        if (!ScrollTrigger.isTouch) {
                        hovered = true;
                        }
                        " @mouseleave="
                        demagnetize($refs.arrow);
                        demagnetize($refs.box);
                        if (!ScrollTrigger.isTouch) {
                        hovered = false;
                        }
                        " x-ref="box" class="gallery-carousel-prev-1840276718 relative w-14 h-14 focus:outline-none group" aria-label="Previous">
                        <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                        <div class="absolute inset-0 border-2 transition-all border-primary/40" :class="{
                            'border-primary/40' : !hovered,
                            'border-4 border-secondary scale-90' : hovered,
                            }"></div>
                        <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                            'opacity-0 scale-50' : !hovered,
                            'opacity-40 scale-100' : hovered,
                            }"></div>
                        <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                            <div class="-scale-x-100">
                                <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                    <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary relative w-full">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                    <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary/40 absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </button>
                    </div>
                </div>
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: 101,
                    end: 0,
                    duration: 0.3,
                    };
                    setTrigger($refs.content);
                    delay = delay + 0.5;
                    start = 'top bottom';
                    mounted();
                    " x-ref="element" class="sm:hidden" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                    <div x-data="magnetic()" class="relative inline-flex hover:z-10">
                        <button x-data="{
                        hovered: false,
                        }" @mousemove="
                        magnetize($event, $el, $refs.arrow, -12);
                        magnetize($event, $el, $refs.box);
                        " @mouseenter="
                        if (!ScrollTrigger.isTouch) {
                        hovered = true;
                        }
                        " @mouseleave="
                        demagnetize($refs.arrow);
                        demagnetize($refs.box);
                        if (!ScrollTrigger.isTouch) {
                        hovered = false;
                        }
                        " x-ref="box" class="gallery-carousel-next-1840276718 relative w-14 h-14 focus:outline-none group" aria-label="Next">
                        <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                        <div class="absolute inset-0 border-2 transition-all border-primary/40" :class="{
                            'border-primary/40' : !hovered,
                            'border-4 border-secondary scale-90' : hovered,
                            }"></div>
                        <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                            'opacity-0 scale-50' : !hovered,
                            'opacity-40 scale-100' : hovered,
                            }"></div>
                        <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                            <div>
                                <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                    <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary relative w-full">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                    <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-primary/40 absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="h-16 md:h-60 bg-white w-full absolute bottom-0"></div>
</section>
<main class="!pb-0 relative outer-grid overflow-hidden -mb-px">
    <div x-data="{
        activeTab: 1
        }">
        
    </div>
    <!-- New Team Section-->
    <div class="py-10 xl:py-20 -mt-20 md:-mt-24 lg:-mt-24" x-data="">
        <div class="fluid-container">
            <div class="max-w-5xl mx-auto">
                <h2 class="text-primary-600 !leading-tight font-sans font-semibold text-3xl md:text-4xl xl:text-5.5xl max-w-[40rem] mx-auto mb-6 text-center text-balance">
                    Meet Our Team
                </h2>
                <div class="gallery-container">
                    <article class="team-gallery">
                        <img src="/images/team-images/1.jpg" alt="Name" />
                        <img src="/images/team-images/2.jpg" alt="Name" />
                        <img src="/images/team-images/3.jpg" alt="Name" />
                        <img src="/images/team-images/4.jpg" alt="Name" />
                        <img src="/images/team-images/5.jpg" alt="Name" />
                        <img src="/images/team-images/6.jpg" alt="Name" />
                        <img src="/images/team-images/7.jpg" alt="Name" />
                        <img src="/images/team-images/8.jpg" alt="Name" />
                        <img src="/images/team-images/9.jpg" alt="Name" />
                        <img src="/images/team-images/10.jpg" alt="Name" />
                        <img src="/images/team-images/11.jpg" alt="Name" />
                        <img src="/images/team-images/12.jpg" alt="Name" />
                        <img src="/images/team-images/13.jpg" alt="Name" />
                        <img src="/images/team-images/14.jpg" alt="Brian Hwang" />
                        <img src="/images/team-images/AlissonMontero.jpg" alt="Alisson Montero" />
                        <img src="/images/team-images/AndrewPark.jpg" alt="Andrew Park" />
                        <img src="/images/team-images/AndyKim.jpg" alt="Andy Kim" />
                        <img src="/images/team-images/BaibhavDas.jpg" alt="Baibhav Das" />
                        <img src="/images/team-images/BoMun.jpg" alt="Bo Mun" />
                        <img src="/images/team-images/ByungchulKim.jpg" alt="Byungchul Kim" />
                        <img src="/images/team-images/CarmenDiaz.jpg" alt="Carmen Diaz" />
                        <img src="/images/team-images/CharleIngram.jpg" alt="Charle Ingram" />
                        <img src="/images/team-images/ChrisJoo.jpg" alt="Chris Joo" />
                        <img src="/images/team-images/ChrisShin.jpg" alt="Chris Shin" />
                        <img src="/images/team-images/CollinBailey.jpg" alt="Collin Bailey" />
                        <img src="/images/team-images/CoreySease.jpg" alt="Corey Sease" />
                        <img src="/images/team-images/DaeshaSimmons.jpg" alt="Daesha Simmons" />
                        <img src="/images/team-images/DanielPark.jpg" alt="Daniel Park" />
                        <img src="/images/team-images/DaoTran.jpg" alt="Dao Tran" />
                        <img src="/images/team-images/DenisMejia.jpg" alt="Denis Mejia" />
                        <img src="/images/team-images/DerekBarker.jpg" alt="Derek Barker" />
                        <img src="/images/team-images/DonDavis.jpg" alt="Don Davis" />
                        <img src="/images/team-images/Donna.jpg" alt="Donna" />
                        <img src="/images/team-images/DucNguyen.jpg" alt="Duc Nguyen" />
                        <img src="/images/team-images/Ed.jpg" alt="Ed" />
                        <img src="/images/team-images/EddiePark.jpg" alt="Eddie Park" />
                        <img src="/images/team-images/EllieKim.jpg" alt="Ellie Kim" />
                        <img src="/images/team-images/EmanuelGacea.jpg" alt="Emanuel Gacea" />
                        <img src="/images/team-images/FrederickTai.jpg" alt="Frederick Tai" />
                        <img src="/images/team-images/GabrielaPadilla.jpg" alt="Gabriela Padilla" />
                        <img src="/images/team-images/HaiTran.jpg" alt="Hai Tran" />
                        <img src="/images/team-images/HauNguyen.jpg" alt="Hau Nguyen" />
                        <img src="/images/team-images/HemelyHomez.jpg" alt="Hemely Homez" />
                        <img src="/images/team-images/HungNguyen.jpg" alt="Hung Nguyen" />
                        <img src="/images/team-images/HuuSonTran.jpg" alt="Huu Son Tran" />
                        <img src="/images/team-images/HuyHoangNguyen.jpg" alt="Huy Hoang Nguyen" />
                        <img src="/images/team-images/HuyMai.jpg" alt="Huy Mai" />
                        <img src="/images/team-images/JacquelineVilchez.jpg" alt="Jacqueline Vilchez" />
                        <img src="/images/team-images/JasmineNa.jpg" alt="Jasmine Na" />
                        <img src="/images/team-images/JaydaKimAI.jpg" alt="Jayda Kim AI" />
                        <img src="/images/team-images/JeffreyAdams.jpg" alt="Jeffrey Adams" />
                        <img src="/images/team-images/JennaWendel.jpg" alt="Jenna Wendel" />
                        <img src="/images/team-images/Jesse.jpg" alt="Jesse" />
                        <img src="/images/team-images/JiSunHwang.jpg" alt="Ji Sun Hwang" />
                        <img src="/images/team-images/JoeAhn.jpg" alt="Joe Ahn" />
                        <img src="/images/team-images/JonHayward.jpg" alt="Jon Hayward" />
                        <img src="/images/team-images/JooheeLee.jpg" alt="JooheeLee" />
                        <img src="/images/team-images/JordanPerez.jpg" alt="Jordan Perez" />
                        <img src="/images/team-images/JorgeDeLaLuzPedraza.jpg" alt="Jorge De La Luz Pedraza" />
                        <img src="/images/team-images/JorgeMedina.jpg" alt="Jorge Medina" />
                        <img src="/images/team-images/JoseDiaz.jpg" alt="JoseDiaz" />
                        <img src="/images/team-images/JoseMendiola.jpg" alt="Jose Mendiola" />
                        <img src="/images/team-images/JoseRicardoRodriguez.jpg" alt="JoseRicardoRodriguez" />
                        <img src="/images/team-images/JudyRah.jpg" alt="Judy Rah" />
                        <img src="/images/team-images/JuliaAnnWillingham.jpg" alt="Julia Ann Willingham" />
                        <img src="/images/team-images/KellyMiller.jpg" alt="Kelly Miller" />
                        <img src="/images/team-images/KeonwooKim.jpg" alt="Keonwoo Kim" />
                        <img src="/images/team-images/KhaiChau.jpg" alt="Khai Chau" />
                        <img src="/images/team-images/KyleighPorter.jpg" alt="Kyleigh Porter" />
                        <img src="/images/team-images/KyuLee.png" alt="KyuLee" />
                        <img src="/images/team-images/LaiKhanh.jpg" alt="Lai Khanh" />
                        <img src="/images/team-images/LinaZarour.jpg" alt="Lina Zarour" />
                        <img src="/images/team-images/LuisanaMunoz.jpg" alt="Luisana Munoz" />
                        <img src="/images/team-images/LukeSkinner.jpg" alt="Luke Skinner" />
                        <img src="/images/team-images/LydiaWexel.jpg" alt="Lydia Wexel" />
                        <img src="/images/team-images/MarialisAltuve.jpg" alt="Marialis Altuve" />
                        <img src="/images/team-images/MarienysTrujillo.png" alt="Marienys Trujillo" />
                        <img src="/images/team-images/MarilolyAltuve.jpg" alt="Mariloly Altuve" />
                        <img src="/images/team-images/MariugeniaMartinez.jpg" alt="Mariugenia Martinez" />
                        <img src="/images/team-images/MarkCapodice.jpg" alt="Mark Capodice" />
                        <img src="/images/team-images/Marta.jpg" alt="Marta" />
                        <img src="/images/team-images/MaryWexel.jpg" alt="Mary Wexel" />
                        <img src="/images/team-images/MattNord.jpg" alt="Matt Nord" />
                        <img src="/images/team-images/MattStainback.jpg" alt="Matt Stainback" />
                        <img src="/images/team-images/MichaelLooney.jpg" alt="Michael Looney" />
                        <img src="/images/team-images/MiguelGarcia.jpg" alt="Miguel Garcia" />
                        <img src="/images/team-images/OmarAnderson.jpg" alt="OmarAnderson" />
                        <img src="/images/team-images/PeterKim.png" alt="Peter Kim" />
                        <img src="/images/team-images/PhucDuong.jpg" alt="Phuc Duong" />
                        <img src="/images/team-images/PhuocTran.jpg" alt="Phuoc Tran" />
                        <img src="/images/team-images/QuangTran.jpg" alt="Quang Tran" />
                        <img src="/images/team-images/RajGurav.jpg" alt="Raj Gurav" />
                        <img src="/images/team-images/RicardoLaRocca.jpg" alt="RicardoLaRocca" />
                        <img src="/images/team-images/RitaKim.jpg" alt="Rita Kim" />
                        <img src="/images/team-images/RobertErwinJr.jpg" alt="Robert Erwin Jr" />
                        <img src="/images/team-images/RobertOConnor.jpg" alt="Robert O Connor" />
                        <img src="/images/team-images/RonnieCharette.jpg" alt="Ronnie Charette" />
                        <img src="/images/team-images/SalvadorTeo.jpg" alt="Salvador Teo" />
                        <img src="/images/team-images/SeanHan.jpg" alt="Sean Han" />
                        <img src="/images/team-images/Sharf.jpg" alt="Sharf" />
                        <img src="/images/team-images/StephanieChoe.jpg" alt="Stephanie Choe" />
                        <img src="/images/team-images/SujinKim.jpg" alt="Sujin Kim" />
                        <img src="/images/team-images/ThaoNguyen.jpg" alt="Thao Nguyen" />
                        <img src="/images/team-images/ThaoPham.jpg" alt="Thao Pham" />
                        <img src="/images/team-images/ThoTuyenMai.jpg" alt="Tho Tuyen Mai" />
                        <img src="/images/team-images/ThomasLoudermilk.png" alt="Thomas Loudermilk" />
                        <img src="/images/team-images/ThuanPhongChau.jpg" alt="Thuan Phong Chau" />
                        <img src="/images/team-images/TimJoo.jpg" alt="Tim Joo" />
                        <img src="/images/team-images/TinhTran.jpg" alt="Tinh Tran" />
                        <img src="/images/team-images/TriNguyen.jpg" alt="Tri Nguyen" />
                        <img src="/images/team-images/TrinhTran.jpg" alt="Trinh Tran" />
                        <img src="/images/team-images/TrungNguyen.jpg" alt="Trung Nguyen" />
                         <img src="/images/team-images/TrungTrucPhan.jpg" alt="Trung Truc Phan" />
                        <img src="/images/team-images/VanLam.jpg" alt="Van Lam" />
                        <img src="/images/team-images/VinhTaiTruong.jpg" alt="Vinh Tai Truong" />
                        <img src="/images/team-images/WilliamBarnes.jpg" alt="William Barnes" />
                        <img src="/images/team-images/WilliamMcFall.jpg" alt="William McFall" />
                        <img src="/images/team-images/WonSeokPark.jpg" alt="Won Seok Park" />
                        <img src="/images/team-images/WonjungKo.jpg" alt="Wonjung Ko" />
                    </article>
                </div>
            </div>
        </div>
    </div>
    <!-- End Team Section -->

    <div x-data="">
        <div class="fluid-container flex flex-col gap-8 xl:gap-28 xl:flex-row-reverse xl:justify-between">
            <div class="relative xl:w-[65%] shrink-0 w-full">
                <div class="absolute z-20 left-0 md:bottom-10 md:left-10 flex justify-center md:justify-end -top-16 md:top-auto w-full md:w-auto">
                </div>
                <img src="/images/map.png" alt="Map of the United States with various states shaded in different shades of blue and black map pins placed on several states in the southeastern region, highlighting hubs for careers and employment opportunities." class="w-full relative z-10">
            </div>
            <div class="lg:py-6 xl:py-20 xl:w-[30%]">
                <div x-ref="content" class="text-left mb-8">
                    <h2 x-data="animateText()" x-init="
                        opacity.active = false;
                        yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                        };
                        type = `words`;
                        wordsClass = avalanche.textClass.words.h1;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                        Where we work
                    </h2>
                    <p class="text-current mt-4 text-base leading-relaxed font-normal">
                        Our reach across the U.S <br><br>
                        <style>
                            ul.custom-bullet-list {
                            list-style-type: disc;
                            /* Keeps bullet points */
                            padding-left: 0;
                            /* Removes default padding */
                            margin-left: 20px;
                            /* Moves the entire list further from the edge */
                            }
                            .custom-bullet-list li {
                            padding-left: 15px;
                            /* Decreases space between the text and the bullet */
                            font-weight: bold;
                            font-size: 1.1em;
                            /* Matching H5 styling */
                            color: #002070;
                            /* Default text color */
                            }
                            .custom-bullet-list li.current-work {
                            color: #3888FF;
                            /* Blue color for CURRENT WORK */
                            }
                            .custom-bullet-list li.performed-work {
                            color: #002070;
                            /* Dark blue color for PERFORMED WORK */
                            }
                        </style>
                    </p>
                    <p></p>
                </div>
                <div class="flex justify-center w-full md:justify-start items-center gap-6 mt-8">
                    <div x-data="animate()" x-init="
                        setTrigger($refs.buttons);
                        element = [...$refs.buttons.querySelectorAll('[data-button]')];
                        stagger = 0.2;
                        delay = delay + 0.1;
                        mounted();
                        " x-ref="buttons" class="w-full items-center justify-center md:justify-start mt-3 sm:mt-4 relative z-10 flex flex-wrap gap-6">
                        <div data-button="" class="w-full md:w-fit" style="opacity: 1;">
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="relative inline-flex hover:z-10">
                                    <a data-portal-prevent="self" x-data="{ hovered: false }" @mouseenter="hovered = true" @mouseleave="hovered = false" class="items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-primary-600 border-current hover:text-current" href="/our-companies">
                                        Explore our Companies
                                        <div class="ml-4 w-8 aspect-square border group-hover:border-primary group-hover:border-opacity-100 group-hover:bg-secondary overflow-hidden border-primary/40 relative grid items-center place-content-center transition">
                                            <div class="w-4 h-4 transition text-primary" :class="hovered && 'translate-x-[100%] opacity-0'">
                                                <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                </svg>
                                            </div>
                                            <div class="w-4 h-4 transition absolute opacity-0 text-primary -left-1/2" :class="hovered && 'translate-x-[calc(100%+0.4rem)] opacity-100'">
                                                <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="relative overflow-hidden">
        <div x-data="sliders()" x-init="noImagesTestimonialSliderHash = '1289207547'; mounted();">
            <div>
                <div class="w-[calc(100vw-4rem)] lg:w-[72.5vw] mx-auto text-left md:text-center">
                    <div class="swiper no-image-testimonial-slider-1289207547 swiper-fade swiper-initialized swiper-horizontal swiper-pointer-events swiper-watch-progress swiper-backface-hidden">
                        <div class="swiper-wrapper no-image-testimonial-slider" style="transition-duration: 0ms;">
                            <div class="swiper-slide swiper-slide-prev" data-swiper-slide-index="0" style="width: 1117px; transition-duration: 0ms; opacity: 0; transform: translate3d(-1117px, 0px, 0px);">
                                <blockquote class="text-base md:text-xl mb-3 border-b border-light-gray lg:border-none py-3 lg:py-0">
                                    "The core value of Eastern has been to satisfy the needs of our clients."
                                </blockquote>
                                <p class="text-primary font-semibold mb-0 text-xl lg:text-2xl leading-tight">
                                    Hoon Kim 
                                </p>
                                <p class="text-current mb-0 text-sm font-medium uppercase tracking-widest">
                                    Chairman/Co-Founder
                                </p>
                            </div>
                            <div class="swiper-slide swiper-slide-next" data-swiper-slide-index="1" style="width: 1117px; transition-duration: 0ms; opacity: 0; transform: translate3d(-3351px, 0px, 0px);">
                                <blockquote class="text-base md:text-xl mb-3 border-b border-light-gray lg:border-none py-3 lg:py-0">
                                    "My passion is bringing our clients’ construction visions to life and turning them into tangible investments"
                                </blockquote>
                                <p class="text-primary font-semibold mb-0 text-xl lg:text-2xl leading-tight">
                                    Peter Kim
                                </p>
                                <p class="text-current mb-0 text-sm font-medium uppercase tracking-widest">
                                    CEO/Co-Founder
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="relative border-t border-light-gray" x-data="{ accordionItem: 0 }">
        <div class="fluid-container flex flex-col lg:flex-row lg:gap-16">
            <div class="basis-full lg:basis-1/3 py-8 lg:pb-0 lg:py-16 lg:pr-8 border-b lg:border-r lg:border-b-0 border-light-gray">
                <div x-ref="content" class="text-left">
                    <h2 x-data="animateText()" x-init="
                        opacity.active = false;
                        yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                        };
                        type = `words`;
                        wordsClass = avalanche.textClass.words.h1;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                        Frequently asked questions
                    </h2>
                    <p class="text-primary-800 mt-4 lg:max-w-sm text-base leading-relaxed font-normal">
                        Your Construction Questions Answered Simply
                    </p>
                </div>
            </div>
            <div class="basis-2/3 lg:pt-16">
                <div class="py-4 relative w-full border-neutral-300 border-b last:border-b-0">
                    <button @click="accordionItem !== 1 ? accordionItem = 1 : accordionItem = null" class="flex justify-between gap-6 items-center relative w-full">
                        <p class="text-primary-600 mb-0 text-left max-w-[85%] text-base leading-relaxed font-normal">
                            What types of construction services do you offer?
                        </p>
                        <div class="w-14 h-14 relative">
                            <div class="bg-primary p-5" x-show="accordionItem !== 1">
                                <div class="text-secondary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-secondary p-5 -rotate-180" x-show="accordionItem === 1 " style="display: none;">
                                <div class="text-primary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </button>
                    <div x-ref="container1" x-show="accordionItem == 1" x-collapse.duration.500ms="" class="accordion ease-in-out-circ" style="display: none; height: 0px; overflow: hidden;" hidden>
                        <div class="prose max-w-xl">
                            <p>We offer a wide range of construction services, including commercial and residential construction, roofing, glazing, general contracting, and specialized construction solutions. Our subsidiaries specialize in different aspects, ensuring comprehensive service across various projects.</p>
                        </div>
                    </div>
                </div>
                <div class="py-4 relative w-full border-neutral-300 border-b last:border-b-0">
                    <button @click="accordionItem !== 2 ? accordionItem = 2 : accordionItem = null" class="flex justify-between gap-6 items-center relative w-full">
                        <p class="text-primary-600 mb-0 text-left max-w-[85%] text-base leading-relaxed font-normal">
                            How do I get started with a construction project?
                        </p>
                        <div class="w-14 h-14 relative">
                            <div class="bg-primary p-5" x-show="accordionItem !== 2">
                                <div class="text-secondary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-secondary p-5 -rotate-180" x-show="accordionItem === 2 " style="display: none;">
                                <div class="text-primary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </button>
                    <div x-ref="container2" x-show="accordionItem == 2" x-collapse.duration.500ms="" class="accordion ease-in-out-circ" style="display: none; height: 0px; overflow: hidden;" hidden>
                        <div class="prose max-w-xl">
                            <p>Simply reach out to us via phone, email, or our contact form, and we'll schedule an initial consultation. From there, we'll discuss your project goals, timeline, and budget to determine the best approach.</p>
                        </div>
                    </div>
                </div>
                <div class="py-4 relative w-full border-neutral-300 border-b last:border-b-0">
                    <button @click="accordionItem !== 3 ? accordionItem = 3 : accordionItem = null" class="flex justify-between gap-6 items-center relative w-full">
                        <p class="text-primary-600 mb-0 text-left max-w-[85%] text-base leading-relaxed font-normal">
                            What is your typical project timeline?
                        </p>
                        <div class="w-14 h-14 relative">
                            <div class="bg-primary p-5" x-show="accordionItem !== 3">
                                <div class="text-secondary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-secondary p-5 -rotate-180" x-show="accordionItem === 3 " style="display: none;">
                                <div class="text-primary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </button>
                    <div x-ref="container3" x-show="accordionItem == 3" x-collapse.duration.500ms="" class="accordion ease-in-out-circ" style="display: none; height: 0px; overflow: hidden;" hidden>
                        <div class="prose max-w-xl">
                            <p>Timelines vary based on the size and complexity of the project. After our initial consultation, we'll provide you with an estimated timeline tailored to your specific needs, ensuring we meet your expectations every step of the way.</p>
                        </div>
                    </div>
                </div>
                <div class="py-4 relative w-full border-neutral-300 border-b last:border-b-0">
                    <button @click="accordionItem !== 4 ? accordionItem = 4 : accordionItem = null" class="flex justify-between gap-6 items-center relative w-full">
                        <p class="text-primary-600 mb-0 text-left max-w-[85%] text-base leading-relaxed font-normal">
                            Do you work with both small and large-scale projects?
                        </p>
                        <div class="w-14 h-14 relative">
                            <div class="bg-primary p-5" x-show="accordionItem !== 4">
                                <div class="text-secondary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-secondary p-5 -rotate-180" x-show="accordionItem === 4 " style="display: none;">
                                <div class="text-primary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </button>
                    <div x-ref="container4" x-show="accordionItem == 4" x-collapse.duration.500ms="" class="accordion ease-in-out-circ" style="display: none; height: 0px; overflow: hidden;" hidden>
                        <div class="prose max-w-xl">
                            <p>Yes, we have the expertise and resources to handle both small and large-scale projects. Whether it’s a single residential home or a multi-million-dollar commercial development, we have the experience to deliver high-quality results.</p>
                        </div>
                    </div>
                </div>
                <div class="py-4 relative w-full border-neutral-300 border-b last:border-b-0">
                    <button @click="accordionItem !== 5 ? accordionItem = 5 : accordionItem = null" class="flex justify-between gap-6 items-center relative w-full">
                        <p class="text-primary-600 mb-0 text-left max-w-[85%] text-base leading-relaxed font-normal">
                            Can you help with design and planning in addition to construction?
                        </p>
                        <div class="w-14 h-14 relative">
                            <div class="bg-primary p-5" x-show="accordionItem !== 5">
                                <div class="text-secondary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-secondary p-5 -rotate-180" x-show="accordionItem === 5 " style="display: none;">
                                <div class="text-primary w-4 py-[0.125rem]">
                                    <svg viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path id="Vector" d="M1.645 0.236328L7 5.80371L12.355 0.236328L14 1.9587L7 9.23633L0 1.9587L1.645 0.236328Z" fill="currentColor" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </button>
                    <div x-ref="container5" x-show="accordionItem == 5" x-collapse.duration.500ms="" class="accordion ease-in-out-circ" style="display: none; height: 0px; overflow: hidden;" hidden>
                        <div class="prose max-w-xl">
                            <p>Absolutely! Our team works closely with architects and designers to provide integrated design-build services. We ensure your vision is brought to life with thoughtful planning, efficient construction methods, and innovative solutions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
.image-caption {
  position: absolute;
  display: none;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 14px;
  pointer-events: none;
  z-index: 9999;
  transform: translate(-50%, -50%);
  white-space: nowrap;
}
</style>

<script>
$(document).ready(function() {
  // Create one floating caption element
  const $caption = $('<div class="image-caption"></div>').appendTo('body');

   $('.team-gallery img').hover(function(e) {
    const $img = $(this);
    const offset = $img.offset();
    const width = $img.width();
    const height = $img.height();
    const alt = $img.attr('alt');

    $caption.text(alt).css({
      top: offset.top + height - 10,
      left: offset.left + width / 2
    }).fadeIn(200);
  }, function() {
    $caption.fadeOut(150);
  });
});
</script>
