<section x-data="" class="relative overflow-hidden">
                    <div x-ref="container" class="relative hidden lg:flex flex-col justify-center min-h-[calc(100vh-62px)] xl:min-h-[calc(100vh-70px)] tall-and-wide:min-h-screen-75 overflow-hidden">



                        <div x-data="animate()" x-init="
        mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
            opacity.active = false;

            scale = {
                ...scale, active: true,
                start: 1.24,
                end: 1.24,
                ease: 'none',
            };

            yPercent = {
                ...yPercent, active: true,
                start: 12,
                end: -12,
                ease: 'none',
            };

            trigger = $refs.container;
            scrub = 1.2;
            start = 'top bottom';
            mounted();
        });
    " class="right-0 absolute top-0 w-3/5 2xl:w-[55%] h-full hidden lg:block">
                            <div x-ref="element" class="stats-mask origin-left relative w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%) scale(1.24, 1.24);">
                                <div x-data="animate()" x-init="
                mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                    element = [...$refs.container.querySelectorAll('[data-curtain]')];
                    opacity.active = false;
                    xPercent = {
                        ...xPercent, active: true,
                        start: 80,
                        end: 0,
                        duration: 1,
                    };

                    yPercent = {
                        ...yPercent, active: true,
                        start: 20,
                        end: 0,
                        duration: 1,
                    };

                    setTrigger($refs.container);
                    stagger = 0.2;
                    mounted();
                });
            " class="relative w-full h-full">
                                    <div x-data="animate()" x-init="
                    mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                        element = $refs.mediaWrapper;
                        opacity.active = false;
                        scale = {
                            ...scale, active: true,
                            start: 1.25,
                            end: 1,
                            duration: 0.6,
                            ease: 'circ.out',
                        };
                        setTrigger($refs.container);
                        delay = delay + 1;
                        mounted();
                    });
                " x-ref="mediaWrapper" class="absolute inset-0 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">

                                        <div x-data="animate()" x-init="
                        mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                            opacity.duration = 0.2;
                            element = $refs.media;
                            setTrigger($refs.container);
                            delay = delay + 0.7;
                            mounted();
                        });
                    " x-ref="media" class="absolute inset-0 overflow-hidden" style="opacity: 1;">
                                            <div x-data="animate()" x-init="
                            mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                                opacity.active = false;

                                scale = {
                                    ...scale, active: true,
                                    start: 1.16,
                                    end: 1.16,
                                    ease: 'none',
                                };

                                yPercent = {
                                    ...yPercent, active: true,
                                    start: -8,
                                    end: 8,
                                    ease: 'none',
                                };

                                trigger = $refs.container;
                                scrub = 1.2;
                                start = 'top bottom';
                                mounted();
                            });
                        " class="absolute inset-0 overflow-hidden">

                                                <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.16, 1.16);">



                                                    <picture>
                                                        <source srcset="
                                            /images/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 3200w,
                                            /images/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 2500w,
                                            /images/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 1920w,
                                            /images/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 1680w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1440x1079.webp 1440w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1280x959.webp 1280w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1024x767.webp 1024w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.webp 768w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-640x480.webp 640w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-480x360.webp 480w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-320x240.webp 320w
                                    " sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">

                                                        <source srcset="
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 3200w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 2500w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 1920w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 1680w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1440x1079.jpg 1440w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1280x959.jpg 1280w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1024x767.jpg 1024w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.jpg 768w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-640x480.jpg 640w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-480x360.jpg 480w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-320x240.jpg 320w
                                    " sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/jpeg">


                                                        <img src="./images/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 50% 50%" alt="Aerial view of a Brasfield & Gorrie construction site with a tall crane in a cityscape, showcasing urban buildings, roads, and vehicles under a cloudy sky.">
                                                    </picture>


                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div x-data="animate()" x-init="
                    mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                        opacity.start = 1;
                        opacity.end = 0;
                        setTrigger($refs.container);
                        delay = delay + 1;
                        mounted();
                    });
                " x-ref="element" class="absolute inset-0" style="opacity: 0;">
                                        <div data-curtain="" class="absolute -left-1/4 -top-1/4 h-[150%] aspect-square bg-secondary rounded-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>

                                        <div data-curtain="" class="absolute -left-1/4 -top-1/4 h-[150%] aspect-square bg-primary-600 rounded-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="fluid-container py-28">
                            <div class="max-w-[22rem]">
                                <h2 x-data="animateText()" x-init="
                            opacity.active = false;
                            yPercent = {
                                ...yPercent, active: true,
                                start: 102,
                                duration: 0.4,
                            };
                            type = `chars`;
                            wordsClass = avalanche.textClass.words.h1;
                            stagger = 0.03;
                            setTrigger($refs.content);
                            mounted();
                        " x-ref="element" class="text-primary-600 !leading-none font-sans font-semibold text-3xl md:text-4xl xl:text-5.5xl mb-4">
                                    <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                        <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">A</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">l</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">i</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">l</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">e</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">a</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">b</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">o</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">u</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">u</div>
                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">s</div>
                                        </div>
                                    </div>
                                </h2>

                                <div x-ref="stats">


                                    <div class="relative">
                                        <div class="py-2 flex justify-start items-center gap-3">
                                            <div class="relative w-auto">
                                                <div class="flex">

                                                    <span x-text="24000 > 0 ? formatNumber(4000) : 4000" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">3</span>

                                                    <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                        +
                                                    </span>
                                                </div>

                                                <div class="absolute inset-0 flex">

                                                    <div class="relative overflow-hidden -mr-8 pr-8">
                                                        <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                            <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">4,000</span>
                                                        </div>
                                                    </div>

                                                    <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0 + 0 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                        <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"> +</div>
                                                            </div>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>


                                            <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0 + 0 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                                Years of experience
                                            </p>
                                        </div>

                                        <div class="relative w-full overflow-hidden">
                                            <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                        </div>
                                    </div>


                                    <div class="relative">
                                        <div class="py-2 flex justify-start items-center gap-3">
                                            <div class="relative w-auto">
                                                <div class="flex">

                                                    <span x-text="28 > 0 ? formatNumber(28) : 28" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">28</span>

                                                    <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                        +-
                                                    </span>
                                                </div>

                                                <div class="absolute inset-0 flex">

                                                    <div class="relative overflow-hidden -mr-8 pr-8">
                                                        <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.1 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                            <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.1 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">28</span>
                                                        </div>
                                                    </div>

                                                    <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.1 + 0 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                        <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"> +</div>
                                                            </div>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>


                                            <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.1 + 0 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                                Licensed States
                                            </p>
                                        </div>

                                        <div class="relative w-full overflow-hidden">
                                            <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.1 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                        </div>
                                    </div>


                                    <div class="relative">
                                        <div class="py-2 flex justify-start items-center gap-3">
                                            <div class="relative w-auto">
                                                <div class="flex">

                                                    <span x-text="17 > 0 ? formatNumber(17) : 17" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">6</span>

                                                </div>

                                                <div class="absolute inset-0 flex">

                                                    <div class="relative overflow-hidden -mr-8 pr-8">
                                                        <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.2 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                            <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.2 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">6</span>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>


                                            <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.2 + 0 + 0;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        Office Locations
                                            </p>
                                        </div>

                                        <div class="relative w-full overflow-hidden">
                                            <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.2 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                        </div>
                                    </div>


                                    <div class="relative">
                                        <div class="py-2 flex justify-start items-center gap-3">
                                            <div class="relative w-auto">
                                                <div class="flex">

                                                    <span x-text="60 > 0 ? formatNumber(60) : 60" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">0.74</span>

                                                    <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                        +
                                                    </span>
                                                </div>

                                                <div class="absolute inset-0 flex">

                                                    <div class="relative overflow-hidden -mr-8 pr-8">
                                                        <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.3 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                        


                                                            <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.3 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">0.74</span>
                                                        </div>
                                                    </div>

                                                    <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.3 + 0 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                        <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"> +</div>
                                                            </div>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>


                                            <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.3 + 0 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                                EMR Rating
                                            </p>
                                        </div>

                                        <div class="relative w-full overflow-hidden">
                                            <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.3 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                        </div>
                                    </div>


                                    <div class="relative">
                                        <div class="py-2 flex justify-start items-center gap-3">
                                            <div class="relative w-auto">
                                                <div class="flex">
                                                    <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                        $
                                                    </span>

                                                    <span x-text="6 > 0 ? formatNumber(6) : 6" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">6</span>

                                                    <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                        .4
                                                    </span>
                                                </div>

                                                <div class="absolute inset-0 flex">
                                                    <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    type = 'chars';
                                    wordsClass = avalanche.textClass.words.h1;
                                    stagger = 0.03;
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4;
                                    mounted();
                                ">
                                                        <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                                <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">$</div>
                                                                </div>
                                                            </div>
                                                        </span>
                                                    </div>

                                                    <div class="relative overflow-hidden -mr-8 pr-8">
                                                        <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4 + 0.1;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                            <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.4 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">700</span>
                                                        </div>
                                                    </div>

                                                    <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4 + 0.1 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                        <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">M</div>
                                                            </div>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>


                                            <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.4 + 0.1 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                                Bonding Capacity
                                            </p>
                                        </div>

                                        <div class="relative w-full overflow-hidden">
                                            <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.4 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                        </div>
                                    </div>


                                    <div class="relative">
                                        <div class="py-2 flex justify-start items-center gap-3">
                                            <div class="relative w-auto">
                                                <div class="flex">
                                                    <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                        #
                                                    </span>

                                                    <span x-text="22 > 0 ? formatNumber(22) : 22" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">22</span>

                                                </div>

                                                <div class="absolute inset-0 flex">
                                                    <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    type = 'chars';
                                    wordsClass = avalanche.textClass.words.h1;
                                    stagger = 0.03;
                                    setTrigger($refs.stats);
                                    delay = delay + 0.5;
                                    mounted();
                                ">
                                                        <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                                <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">#</div>
                                                                </div>
                                                            </div>
                                                        </span>
                                                    </div>

                                                    <div class="relative overflow-hidden -mr-8 pr-8">
                                                        <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.5 + 0.1;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                            <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.5 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">22</span>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>


                                            <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.5 + 0.1 + 0;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                                top contractor nationally, according to Engineering News-Record
                                            </p>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div x-data="" x-ref="container" class="relative flex lg:hidden flex-col justify-between min-h-[calc(100vh-62px)] bg-primary-400">
                        <div class="fluid-container relative -mb-10 pt-12 z-10">

                            <h2 x-data="animateText()" x-init="
                    opacity.active = false;
                    yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                    };
                    type = `chars`;
                    wordsClass = avalanche.textClass.words.h1;
                    stagger = 0.03;
                    setTrigger($refs.container);
                    mounted();
                " x-ref="element" class="text-white font-sans leading-none font-semibold text-5xl md:text-6xl text-center mb-4 text-balance">
                                <div class="overflow-hidden" style="display: block; text-align: center; position: relative;">
                                    <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">A</div>
                                    </div>
                                    <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">l</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">i</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">l</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">e</div>
                                    </div>
                                    <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">a</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">b</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">o</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">u</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                    </div>
                                    <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">u</div>
                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">s</div>
                                    </div>
                                </div>
                            </h2>

                            <div class="relative mt-12 w-full overflow-hidden">

                                <div x-data="animate()" x-init="
                    opacity.active = false;
                    xPercent = {
                        ...xPercent, active: true,
                        start: -101,
                        end: 0,
                    };

                    setTrigger($refs.stats);
                    delay = delay + 0 + 0.6;
                    mounted();
                " x-ref="element" class="w-full h-px bg-white/20" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>

                            <div x-ref="stats">


                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">

                                                <span x-text="4000 > 0 ? formatNumber(4000) : 4000" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">4,000</span>

                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                    +
                                                </span>
                                            </div>

                                            <div class="absolute inset-0 flex">

                                                <div class="relative overflow-hidden -mr-8 pr-8">

                                                    <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                        <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">4,000</span>
                                                    </div>
                                                </div>

                                                <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0 + 0 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                    <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> +</div>
                                                        </div>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>


                                        <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0 + 0 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                            Years of experience
                                        </p>
                                    </div>

                                    <div class="relative w-full overflow-hidden">

                                        <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                    </div>
                                </div>


                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">

                                                <span x-text="220 > 0 ? formatNumber(220) : 220" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">220</span>

                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                    +
                                                </span>
                                            </div>

                                            <div class="absolute inset-0 flex">

                                                <div class="relative overflow-hidden -mr-8 pr-8">

                                                    <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.1 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                        <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.1 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">220</span>
                                                    </div>
                                                </div>

                                                <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.1 + 0 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                    <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> +</div>
                                                        </div>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>


                                        <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.1 + 0 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                            active projects
                                        </p>
                                    </div>

                                    <div class="relative w-full overflow-hidden">

                                        <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.1 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                    </div>
                                </div>


                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">

                                                <span x-text="17 > 0 ? formatNumber(17) : 17" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">17</span>

                                            </div>

                                            <div class="absolute inset-0 flex">

                                                <div class="relative overflow-hidden -mr-8 pr-8">

                                                    <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.2 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                        <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.2 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">17</span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>


                                        <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.2 + 0 + 0;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                            states with active projects
                                        </p>
                                    </div>

                                    <div class="relative w-full overflow-hidden">

                                        <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.2 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                    </div>
                                </div>


                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">

                                                <span x-text="60 > 0 ? formatNumber(60) : 60" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">60</span>

                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                    +
                                                </span>
                                            </div>

                                            <div class="absolute inset-0 flex">

                                                <div class="relative overflow-hidden -mr-8 pr-8">

                                                    <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.3 + 0;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                        <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.3 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">60</span>
                                                    </div>
                                                </div>

                                                <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.3 + 0 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                    <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> +</div>
                                                        </div>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>


                                        <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.3 + 0 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                            years in business
                                        </p>
                                    </div>

                                    <div class="relative w-full overflow-hidden">

                                        <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.3 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                    </div>
                                </div>


                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                    $
                                                </span>

                                                <span x-text="6 > 0 ? formatNumber(6) : 6" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">6</span>

                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                    .4
                                                </span>
                                            </div>

                                            <div class="absolute inset-0 flex">
                                                <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    type = 'chars';
                                    wordsClass = avalanche.textClass.words.h1;
                                    stagger = 0.03;
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4;
                                    mounted();
                                ">
                                                    <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                            <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">$</div>
                                                            </div>
                                                        </div>
                                                    </span>
                                                </div>

                                                <div class="relative overflow-hidden -mr-8 pr-8">

                                                    <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4 + 0.1;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                        <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.4 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">6</span>
                                                    </div>
                                                </div>

                                                <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4 + 0.1 + 1.2;
                                    mounted();
                                " class="relative ml-2">
                                                    <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> .4</div>
                                                        </div>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>


                                        <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.4 + 0.1 + 0.1;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                            billion in annual revenue
                                        </p>
                                    </div>

                                    <div class="relative w-full overflow-hidden">

                                        <div x-data="animate()" x-init="
                        opacity.active = false;
                        xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                        };

                        setTrigger($refs.stats);
                        delay = delay + 0.4 + 0.6;
                        mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                    </div>
                                </div>


                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                    #
                                                </span>

                                                <span x-text="22 > 0 ? formatNumber(22) : 22" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">22</span>

                                            </div>

                                            <div class="absolute inset-0 flex">
                                                <div x-data="animateText()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    type = 'chars';
                                    wordsClass = avalanche.textClass.words.h1;
                                    stagger = 0.03;
                                    setTrigger($refs.stats);
                                    delay = delay + 0.5;
                                    mounted();
                                ">
                                                    <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                            <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 0.0011%) translate3d(0px, 0px, 0px);">#</div>
                                                            </div>
                                                        </div>
                                                    </span>
                                                </div>

                                                <div class="relative overflow-hidden -mr-8 pr-8">

                                                    <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    yPercent = {
                                        ...yPercent, active: true,
                                        start: 102,
                                        duration: 0.3,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.5 + 0.1;
                                    mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">



                                                        <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                        if (avalanche.inView($refs.stats)) {
                                            delay = avalanche.delay.enter;
                                        } else {
                                            delay = avalanche.delay.default;
                                        }
                                        scrollSettings = {
                                            start: `top 85%`,
                                            end: `bottom top`,
                                            toggleActions: `play none play none`,
                                            trigger: $refs.stats,
                                        }
                                        gsap.from($refs.number, {
                                            textContent: 0,
                                            duration: 0.8,
                                            ease: `circ.out`,
                                            snap: { textContent: 1 },
                                            delay: delay + 0.5 + 0.5,
                                            scrollTrigger: scrollSettings,
                                            stagger: {
                                                each: 1.0,
                                                onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                },
                                            }
                                        });
                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">22</span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>


                                        <p x-data="fadeIn()" x-init="
                    delay = avalanche.delay.enter + 0.5 + 0.1 + 0;
                    type = `chars`;
                    stagger = 0.01;
                    mounted();
                " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                            top contractor nationally, according to Engineering News-Record
                                        </p>
                                    </div>

                                </div>
                            </div>
                        </div>



                        <div x-data="animate()" x-init="
        scale = {
            ...scale, active: true,
            start: 1.25,
            end: 1,
            ease: 'circ.out',
        };
        element = $refs.media;
        setTrigger($refs.media);
        delay = delay + 0.7;
        mounted();
    " x-ref="media" class="flex-grow w-full min-h-52 relative bg-primary-400" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">
                            <div x-data="animate()" x-init="
            opacity.active = false;

            scale = {
                ...scale, active: true,
                start: 1.24,
                end: 1.24,
                ease: 'none',
            };

            yPercent = {
                ...yPercent, active: true,
                start: -12,
                end: 12,
                ease: 'none',
            };

            setTrigger($refs.container);
            scrollTrigger = true;
            trigger = $refs.container;
            scrub = 0.4;
            start = 'top bottom';
            mounted();
        " class="absolute inset-0 !top-1 overflow-hidden">


                                <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 12%) scale(1.24, 1.24);">



                                    <picture>
                                        <source srcset="
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 3200w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 2500w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 1920w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.webp 1680w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1440x1079.webp 1440w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1280x959.webp 1280w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1024x767.webp 1024w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.webp 768w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-640x480.webp 640w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-480x360.webp 480w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-320x240.webp 320w
                                    " sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">

                                        <source srcset="
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 3200w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 2500w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 1920w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2.jpg 1680w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1440x1079.jpg 1440w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1280x959.jpg 1280w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-1024x767.jpg 1024w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.jpg 768w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-640x480.jpg 640w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-480x360.jpg 480w,
                                            https://www.brasfieldgorrie.com/wp-content/uploads/2025/04/Cooper-Green_-Crane-Placement-41_medium_rt2-320x240.jpg 320w
                                    " sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/jpeg">


                                        <img src="./images/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Aerial view of a Brasfield & Gorrie construction site with a tall crane in a cityscape, showcasing urban buildings, roads, and vehicles under a cloudy sky.">
                                    </picture>


                                </div>
                            </div>
                            <div class="stats-mobile-gradient absolute w-full h-full inset-0"></div>
                        </div>
                    </div>
                </section>