<?php

namespace App\Controllers;

class Home extends BaseController
{
    public function index($page = 'home', $id = 0, $id2 = 0, $id3 = 0)
    {
        $result = true;
        $type = 'page';
        $data['page'] = $page;
        $data['page_id'] = $id;
        $data['title'] = 'Quality Improvement Management for Healthcare Professionals';

        switch ($page) {
            case 'home':
                $data['page_seo'] = '';
                $data['page_description'] = 'The Quality Coaching Co. offers tailored quality improvement management for healthcare pros to enhance patient care and outcomes. Start improving today - contact us!';
                break;
            case 'about':
                $data['title'] = 'About Quality Improvement Management for Healthcare Professionals | The QCC';
                $data['page_seo'] = 'About Us: ';
                $data['page_description'] = 'We Empower Healthcare Leaders to Achieve Measurable, Lasting Results.';
                break;
            case 'services':
                $data['title'] = 'Healthcare Quality Improvement Services | The Quality Coaching Co.';
                $data['page_seo'] = 'Services: ';
                $data['page_description'] = 'Empower healthcare leaders with the skills to inspire teams, drive actionable strategies, and optimize operations through Lean Six Sigma excellence.';
                break;
            case 'podcast':
                $data['title'] = 'Excellence In Healthcare Podcast | The Quality Coaching Co.';
                $data['page_seo'] = 'Podcast: ';
                $data['page_description'] = 'Explore expert interviews & insights driving healthcare excellence. Tune in to inspire growth, innovation & leadership. Listen now to elevate your impact!';
                break;
            case 'public-sector':
                $data['title'] = 'Public & Government Healthcare Process Improvement | The QCC';
                $data['page_seo'] = 'Public Sector: ';
                $data['page_description'] = 'Boost efficiency in public healthcare with proven process improvement strategies. Discover insights tailored for government agencies. Learn more with The QCC today!';
                break;
            case 'small-business':
                $data['title'] = 'Healthcare Small Business Coaching & Consulting | The QCC';
                $data['page_seo'] = 'Small Business: ';
                $data['page_description'] = 'Empower your healthcare business with expert coaching and consulting. Gain clarity, grow strategically, and lead confidently. Start your success journey today!';
                break;
            case 'resources':
                $data['title'] = 'Resources | Quality Improvement Management for Healthcare Professionals';
                $data['page_seo'] = 'Resources: ';
                $data['page_description'] = 'Access top-notch education on todays essential business topics through e-learning webinars, public policy discussions, leadership development programs, and more.';
                break;
            case 'contact':
                $data['title'] = 'Contact Us  | The Quality Coaching Co.';
                $data['page_seo'] = 'Contact Us: ';
                $data['page_description'] = '2310 Parklake Dr. NE, #387, Atlanta, GA 30345 (888) 880 - 8127 <EMAIL>';
                break;
            case 'blog':
                $data['title'] = 'Quality Improvement Management for Healthcare Professionals Blog';
                $data['page_seo'] = 'Blog: ';
                $data['page_description'] = 'To empower healthcare leaders with the skillset, toolset, and mindset needed to inspire their teams and drive organizational success.';
                $blog_model = model('App\Models\BlogModel');

                $data['blogs'] = $blog_model->blogs();

                if (!empty($id)) {
                    if (!empty($id2)) {
                        $data['blog'] = $blog_model->blog_by_id($id);
                        $page = 'blog_details';

                        if (empty($data['blog'])) {
                            $page = 'blog_details_not_found';
                            $data['blog'] = new \stdClass();
                            $data['blog']->title = 'Blog Not Found';
                            $data['blog']->intro = 'Sorry! Cannot seem to find the blog you were looking for.';
                        } else {
                            $data['page'] = 'blog/' . $id . '/' . $id2;
                            $data['page_seo'] = $data['blog']->title . ': ';
                            $data['page_description'] = $data['blog']->intro;
                        }
                        $data['page_seo'] = $data['blog']->title . ': ';
                        $data['page_description'] = $data['blog']->intro;
                    } else {
                        $redirect = true;
                        $data['redirect'] = '/blog';
                    }
                }
                break;
            case 'send_email':
                $type = 'json';
                $form_values = $this->request->getPost();
                $data['return'] = $this->send_mail($form_values);
                break;
            default:
                $data['page_seo'] = '';
                $data['page_description'] = '';
        }

        if (!empty($redirect)) {
            echo view('templates/redirect', $data);
        } else {
            if ($type == 'json') {
                $this->response->setContentType('application/json');
                echo view('templates/json', $data);
            } else {
                $pagePath = APPPATH . 'Views/pages/' . $page . '.php';
                if (file_exists($pagePath)) {
                    echo view('templates/header', $data);
                    if ($result) {
                        echo view('pages/' . $page, $data);
                    }
                    echo view('templates/footer', $data);
                } else {
                    $this->response->setStatusCode(404);
                    $data['page_seo'] = '404 Not Found: ';
                    $data['page_description'] = 'Sorry! Cannot seem to find the page you were looking for.';
                    echo view('templates/header', $data);
                    echo view('errors/html/error_404', $data);
                    echo view('templates/footer', $data);
                }
            }
        }
    }

    public function send_mail($data)
    {
        $fname = '';
        if (!empty($data['name'])) {
            $fname = $data['name'];
        }

        $femail = '';
        if (!empty($data['email'])) {
            $femail = $data['email'];
        }

        $fphone = '';
        if (!empty($data['phone'])) {
            $fphone = $data['phone'];
        }

        $fsubject = '';
        if (!empty($data['subject'])) {
            $fsubject = $data['subject'];
        }

        $fmessage = '';
        if (!empty($data['message'])) {
            $fmessage = $data['message'];
        }

        $email = \Config\Services::email();
        $email->setFrom('<EMAIL>', 'Contact Form');
        $email->setTo('<EMAIL>');
        $email->setCC('<EMAIL>');

        //$email->setTo('<EMAIL>');
        $email->setSubject('iugis Website: Contact Form');

        $message  = '';
        $message .= "Name: $fname \n";
        $message .= "Email: $femail \n";
        $message .= "Phone: $fphone \n";
        $message .= "------------------------- \n";
        $message .= "Subject: $fsubject \n";
        $message .= "Message: $fmessage \n";

        $email->setMessage($message);

        $email_sent = $email->send();

        return $email_sent;
    }
}
