
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"8",
  
  "macros":[{"function":"__e"},{"function":"__v","vtp_name":"gtm.elementUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.triggers","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":""},{"function":"__c","vtp_value":"G-2CR695G58K"},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__vis","vtp_elementSelector":"body \u003E div.dialog.fixed.z-50.inset-0.flex.w-full.h-screen.overflow-hidden.py-12","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"100"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__v","vtp_name":"gtm.elementClasses","vtp_dataLayerVersion":1},{"function":"__aev","vtp_varType":"TEXT"},{"function":"__vis","vtp_elementSelector":"#smooth-content \u003E div \u003E section \u003E div \u003E div:nth-child(9) \u003E div \u003E div \u003E div \u003E div","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"50"},{"function":"__vis","vtp_elementSelector":"#smooth-content \u003E div \u003E section \u003E div \u003E div:nth-child(10) \u003E div \u003E div \u003E div \u003E div","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"50"},{"function":"__vis","vtp_elementSelector":"#smooth-content \u003E div \u003E section \u003E div \u003E div:nth-child(11) \u003E div \u003E div \u003E div \u003E div","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"50"},{"function":"__vis","vtp_elementSelector":"#smooth-content \u003E div \u003E section \u003E div \u003E div:nth-child(12) \u003E div \u003E div \u003E div \u003E div","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"50"},{"function":"__vis","vtp_elementSelector":"#smooth-content \u003E div \u003E section \u003E div \u003E div:nth-child(13) \u003E div \u003E div \u003E div \u003E div","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"50"},{"function":"__vis","vtp_elementSelector":"#smooth-content \u003E div \u003E section \u003E div \u003E div:nth-child(14) \u003E div \u003E div \u003E div \u003E div","vtp_outputMethod":"BOOLEAN","vtp_selectorType":"CSS","vtp_onScreenRatio":"50"},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"},{"function":"__v","vtp_name":"gtm.element","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementId","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementTarget","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.newUrlFragment","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.oldUrlFragment","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.newHistoryState","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.oldHistoryState","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.historyChangeSource","vtp_dataLayerVersion":1},{"function":"__ctv"},{"function":"__r"},{"function":"__cid"},{"function":"__hid"},{"function":"__v","vtp_name":"gtm.videoProvider","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoTitle","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoDuration","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoPercent","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoVisible","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoStatus","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoCurrentTime","vtp_dataLayerVersion":1}],
  "tags":[{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":"G-2CR695G58K","tag_id":4},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"career_click","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":7},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"find_jobs_click","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":9},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"career_video","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":11},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"nav_commercial","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":17},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"nav_government","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":19},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"nav_healthcare","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":24},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"nav_infrastructure","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":25},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"nav_industrial","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":26},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"nav_mission_critical","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":27},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"home_commercial","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":29},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"home_government","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":37},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"home_healthcare","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":42},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"home_infrastructure","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":43},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"home_industrial","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":44},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"home_mission_critical","vtp_measurementIdOverride":["macro",3],"vtp_enableUserProperties":true,"vtp_enableMoreSettingsOption":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":45},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"184799763_5","tag_id":54},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_waitForTagsTimeout":"2000","vtp_uniqueTriggerId":"184799763_8","tag_id":55},{"function":"__cl","tag_id":56},{"function":"__hl","tag_id":57},{"function":"__cl","tag_id":58},{"function":"__cl","tag_id":59},{"function":"__cl","tag_id":60},{"function":"__cl","tag_id":61},{"function":"__cl","tag_id":62},{"function":"__cl","tag_id":63},{"function":"__cl","tag_id":64},{"function":"__cl","tag_id":65},{"function":"__cl","tag_id":66},{"function":"__cl","tag_id":67},{"function":"__cl","tag_id":68},{"function":"__cl","tag_id":69},{"function":"__html","metadata":["map"],"once_per_event":true,"vtp_html":"\u003Cscript type=\"text\/gtmscript\"\u003E(function(a,c,e,f,d,b){a.hj=a.hj||function(){(a.hj.q=a.hj.q||[]).push(arguments)};a._hjSettings={hjid:4981665,hjsv:6};d=c.getElementsByTagName(\"head\")[0];b=c.createElement(\"script\");b.async=1;b.src=e+a._hjSettings.hjid+f+a._hjSettings.hjsv;d.appendChild(b)})(window,document,\"https:\/\/static.hotjar.com\/c\/hotjar-\",\".js?sv\\x3d\");\u003C\/script\u003E","vtp_supportDocumentWrite":false,"vtp_enableIframeMode":false,"vtp_enableEditJsMacroBehavior":false,"tag_id":53}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_cn","arg0":["macro",1],"arg1":"\/careers"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.linkClick"},{"function":"_re","arg0":["macro",2],"arg1":"(^$|((^|,)184799763_5($|,)))"},{"function":"_cn","arg0":["macro",4],"arg1":"\/careers"},{"function":"_cn","arg0":["macro",1],"arg1":"\/jobs"},{"function":"_re","arg0":["macro",2],"arg1":"(^$|((^|,)184799763_8($|,)))"},{"function":"_eq","arg0":["macro",5],"arg1":"true"},{"function":"_cn","arg0":["macro",6],"arg1":"\/careers"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.click"},{"function":"_cn","arg0":["macro",7],"arg1":"ration-200 ease-in-out lg:hover:text-primary"},{"function":"_cn","arg0":["macro",8],"arg1":"Commercial"},{"function":"_cn","arg0":["macro",8],"arg1":"Government"},{"function":"_cn","arg0":["macro",8],"arg1":"Healthcare"},{"function":"_cn","arg0":["macro",8],"arg1":"Infrastructure"},{"function":"_cn","arg0":["macro",8],"arg1":"Industrial"},{"function":"_cn","arg0":["macro",8],"arg1":"Mission"},{"function":"_cn","arg0":["macro",1],"arg1":"\/commercial"},{"function":"_cn","arg0":["macro",7],"arg1":"hover:bg-secondary"},{"function":"_cn","arg0":["macro",9],"arg1":"true"},{"function":"_cn","arg0":["macro",1],"arg1":"\/government"},{"function":"_cn","arg0":["macro",10],"arg1":"true"},{"function":"_cn","arg0":["macro",1],"arg1":"\/healthcare"},{"function":"_eq","arg0":["macro",11],"arg1":"true"},{"function":"_cn","arg0":["macro",1],"arg1":"\/infrastructure"},{"function":"_eq","arg0":["macro",12],"arg1":"true"},{"function":"_cn","arg0":["macro",1],"arg1":"\/industrial"},{"function":"_eq","arg0":["macro",13],"arg1":"true"},{"function":"_cn","arg0":["macro",1],"arg1":"\/mission-critical"},{"function":"_eq","arg0":["macro",14],"arg1":"true"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",0,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31]],[["if",1,2,3],["add",1]],[["if",2,4,5,6],["add",2]],[["if",7,8,9],["add",3]],[["if",9,10,11],["add",4]],[["if",9,10,12],["add",5]],[["if",9,10,13],["add",6]],[["if",9,10,14],["add",7]],[["if",9,10,15],["add",8]],[["if",9,10,16],["add",9]],[["if",9,17,18,19],["add",10]],[["if",9,18,20,21],["add",11]],[["if",9,18,22,23],["add",12]],[["if",9,18,24,25],["add",13]],[["if",9,18,26,27],["add",14]],[["if",9,18,28,29],["add",15]],[["if",30],["add",32]]]
},
"runtime":[ [50,"__aev",[46,"a"],[50,"bc",[46,"bj"],[22,[2,[15,"v"],"hasOwnProperty",[7,[15,"bj"]]],[46,[53,[36,[16,[15,"v"],[15,"bj"]]]]]],[52,"bk",[16,[15,"z"],"element"]],[22,[28,[15,"bk"]],[46,[36,[44]]]],[52,"bl",["g",[15,"bk"]]],["bd",[15,"bj"],[15,"bl"]],[36,[15,"bl"]]],[50,"bd",[46,"bj","bk"],[43,[15,"v"],[15,"bj"],[15,"bk"]],[2,[15,"w"],"push",[7,[15,"bj"]]],[22,[18,[17,[15,"w"],"length"],[15,"s"]],[46,[53,[52,"bl",[2,[15,"w"],"shift",[7]]],[2,[15,"b"],"delete",[7,[15,"v"],[15,"bl"]]]]]]],[50,"be",[46,"bj","bk"],[52,"bl",["n",[30,[30,[16,[15,"z"],"elementUrl"],[15,"bj"]],""]]],[52,"bm",["n",[30,[17,[15,"bk"],"component"],"URL"]]],[38,[15,"bm"],[46,"URL","IS_OUTBOUND","PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT"],[46,[5,[46,[36,[15,"bl"]]]],[5,[46,[36,["bg",[15,"bl"],[17,[15,"bk"],"affiliatedDomains"]]]]],[5,[46,[36,[2,[15,"l"],"getProtocol",[7,[15,"bl"]]]]]],[5,[46,[36,[2,[15,"l"],"getHost",[7,[15,"bl"],[17,[15,"bk"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"l"],"getPort",[7,[15,"bl"]]]]]],[5,[46,[36,[2,[15,"l"],"getPath",[7,[15,"bl"],[17,[15,"bk"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"l"],"getExtension",[7,[15,"bl"]]]]]],[5,[46,[22,[17,[15,"bk"],"queryKey"],[46,[53,[36,[2,[15,"l"],"getFirstQueryParam",[7,[15,"bl"],[17,[15,"bk"],"queryKey"]]]]]],[46,[53,[36,[2,[17,["m",[15,"bl"]],"search"],"replace",[7,"?",""]]]]]]]],[5,[46,[36,[2,[15,"l"],"getFragment",[7,[15,"bl"]]]]]],[9,[46,[36,[17,["m",[15,"bl"]],"href"]]]]]]],[50,"bf",[46,"bj","bk"],[52,"bl",[8,"ATTRIBUTE","elementAttribute","CLASSES","elementClasses","ELEMENT","element","ID","elementId","HISTORY_CHANGE_SOURCE","historyChangeSource","HISTORY_NEW_STATE","newHistoryState","HISTORY_NEW_URL_FRAGMENT","newUrlFragment","HISTORY_OLD_STATE","oldHistoryState","HISTORY_OLD_URL_FRAGMENT","oldUrlFragment","TARGET","elementTarget"]],[52,"bm",[16,[15,"z"],[16,[15,"bl"],[15,"bj"]]]],[36,[39,[21,[15,"bm"],[44]],[15,"bm"],[15,"bk"]]]],[50,"bg",[46,"bj","bk"],[22,[28,[15,"bj"]],[46,[53,[36,false]]]],[52,"bl",["bi",[15,"bj"]]],[22,["bh",[15,"bl"],["k"]],[46,[53,[36,false]]]],[22,[28,["q",[15,"bk"]]],[46,[53,[3,"bk",[2,[2,["n",[30,[15,"bk"],""]],"replace",[7,["c","\\s+","g"],""]],"split",[7,","]]]]]],[65,"bm",[15,"bk"],[46,[53,[22,[20,["j",[15,"bm"]],"object"],[46,[53,[22,[16,[15,"bm"],"is_regex"],[46,[53,[52,"bn",["c",[16,[15,"bm"],"domain"]]],[22,[20,[15,"bn"],[45]],[46,[6]]],[22,["p",[15,"bn"],[15,"bl"]],[46,[53,[36,false]]]]]],[46,[53,[22,["bh",[15,"bl"],[16,[15,"bm"],"domain"]],[46,[53,[36,false]]]]]]]]],[46,[22,[20,["j",[15,"bm"]],"RegExp"],[46,[53,[22,["p",[15,"bm"],[15,"bl"]],[46,[53,[36,false]]]]]],[46,[53,[22,["bh",[15,"bl"],[15,"bm"]],[46,[53,[36,false]]]]]]]]]]]],[36,true]],[50,"bh",[46,"bj","bk"],[22,[28,[15,"bk"]],[46,[36,false]]],[22,[19,[2,[15,"bj"],"indexOf",[7,[15,"bk"]]],0],[46,[36,true]]],[3,"bk",["bi",[15,"bk"]]],[22,[28,[15,"bk"]],[46,[36,false]]],[3,"bk",[2,[15,"bk"],"toLowerCase",[7]]],[41,"bl"],[3,"bl",[37,[17,[15,"bj"],"length"],[17,[15,"bk"],"length"]]],[22,[1,[18,[15,"bl"],0],[29,[2,[15,"bk"],"charAt",[7,0]],"."]],[46,[53,[34,[3,"bl",[37,[15,"bl"],1]]],[3,"bk",[0,".",[15,"bk"]]]]]],[36,[1,[19,[15,"bl"],0],[12,[2,[15,"bj"],"indexOf",[7,[15,"bk"],[15,"bl"]]],[15,"bl"]]]]],[50,"bi",[46,"bj"],[22,[28,["p",[15,"r"],[15,"bj"]]],[46,[53,[3,"bj",[0,"http://",[15,"bj"]]]]]],[36,[2,[15,"l"],"getHost",[7,[15,"bj"],true]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","internal.getElementAttribute"]],[52,"e",["require","internal.getElementValue"]],[52,"f",["require","internal.getEventData"]],[52,"g",["require","internal.getElementInnerText"]],[52,"h",["require","internal.getElementProperty"]],[52,"i",["require","internal.copyFromDataLayerCache"]],[52,"j",["require","getType"]],[52,"k",["require","getUrl"]],[52,"l",[15,"__module_legacyUrls"]],[52,"m",["require","internal.legacyParseUrl"]],[52,"n",["require","makeString"]],[52,"o",["require","templateStorage"]],[52,"p",["require","internal.testRegex"]],[52,"q",[51,"",[7,"bj"],[36,[20,["j",[15,"bj"]],"array"]]]],[52,"r",["c","^https?:\\/\\/","i"]],[52,"s",35],[52,"t","eq"],[52,"u","evc"],[52,"v",[30,[2,[15,"o"],"getItem",[7,[15,"u"]]],[8]]],[2,[15,"o"],"setItem",[7,[15,"u"],[15,"v"]]],[52,"w",[30,[2,[15,"o"],"getItem",[7,[15,"t"]]],[7]]],[2,[15,"o"],"setItem",[7,[15,"t"],[15,"w"]]],[52,"x",[17,[15,"a"],"defaultValue"]],[52,"y",[17,[15,"a"],"varType"]],[52,"z",["i","gtm"]],[38,[15,"y"],[46,"TAG_NAME","TEXT","URL","ATTRIBUTE"],[46,[5,[46,[52,"ba",[16,[15,"z"],"element"]],[52,"bb",[1,[15,"ba"],["h",[15,"ba"],"tagName"]]],[36,[30,[15,"bb"],[15,"x"]]]]],[5,[46,[36,[30,["bc",["f","gtm\\.uniqueEventId"]],[15,"x"]]]]],[5,[46,[36,["be",[15,"x"],[15,"a"]]]]],[5,[46,[22,[20,[17,[15,"a"],"attribute"],[44]],[46,[53,[36,["bf",[15,"y"],[15,"x"]]]]],[46,[53,[52,"bj",[16,[15,"z"],"element"]],[52,"bk",[1,[15,"bj"],[39,[20,[17,[15,"a"],"attribute"],"value"],["e",[15,"bj"]],["d",[15,"bj"],[17,[15,"a"],"attribute"]]]]],[36,[30,[30,[15,"bk"],[15,"x"]],""]]]]]]],[9,[46,[36,["bf",[15,"y"],[15,"x"]]]]]]]]
 ,[50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__cid",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"containerId"]]]
 ,[50,"__cl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnClick"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ctv",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"version"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"getProtocol",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"getHost",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"getPort",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"getPath",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"getFirstQueryParam",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"getFragment",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"removeFragment",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__googtag",[46,"a"],[50,"l",[46,"u","v"],[66,"w",[2,[15,"b"],"keys",[7,[15,"v"]]],[46,[53,[43,[15,"u"],[15,"w"],[16,[15,"v"],[15,"w"]]]]]]],[50,"m",[46],[36,[7,[17,[17,[15,"d"],"SCHEMA"],"EP_SERVER_CONTAINER_URL"],[17,[17,[15,"d"],"SCHEMA"],"EP_TRANSPORT_URL"]]]],[50,"n",[46,"u"],[52,"v",["m"]],[65,"w",[15,"v"],[46,[53,[52,"x",[16,[15,"u"],[15,"w"]]],[22,[15,"x"],[46,[36,[15,"x"]]]]]]],[36,[44]]],[52,"b",["require","Object"]],[52,"c",["require","createArgumentsQueue"]],[52,"d",[15,"__module_gtag"]],[52,"e",["require","internal.gtagConfig"]],[52,"f",["require","getType"]],[52,"g",["require","internal.loadGoogleTag"]],[52,"h",["require","logToConsole"]],[52,"i",["require","makeNumber"]],[52,"j",["require","makeString"]],[52,"k",["require","makeTableMap"]],[52,"o",[30,[17,[15,"a"],"tagId"],""]],[22,[30,[21,["f",[15,"o"]],"string"],[24,[2,[15,"o"],"indexOf",[7,"-"]],0]],[46,[53,["h",[0,"Invalid Measurement ID for the GA4 Configuration tag: ",[15,"o"]]],[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[52,"p",[30,[17,[15,"a"],"configSettingsVariable"],[8]]],[52,"q",[30,["k",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["l",[15,"p"],[15,"q"]],[52,"r",[30,[17,[15,"a"],"eventSettingsVariable"],[8]]],[52,"s",[30,["k",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["l",[15,"r"],[15,"s"]],[52,"t",[15,"p"]],["l",[15,"t"],[15,"r"]],[22,[30,[2,[15,"t"],"hasOwnProperty",[7,[17,[17,[15,"d"],"SCHEMA"],"EP_USER_PROPERTIES"]]],[17,[15,"a"],"userProperties"]],[46,[53,[52,"u",[30,[16,[15,"t"],[17,[17,[15,"d"],"SCHEMA"],"EP_USER_PROPERTIES"]],[8]]],["l",[15,"u"],[30,["k",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"t"],[17,[17,[15,"d"],"SCHEMA"],"EP_USER_PROPERTIES"],[15,"u"]]]]],[2,[15,"d"],"convertParameters",[7,[15,"t"],[17,[15,"d"],"GOLD_BOOLEAN_FIELDS"],[51,"",[7,"u"],[36,[39,[20,"false",[2,["j",[15,"u"]],"toLowerCase",[7]]],false,[28,[28,[15,"u"]]]]]]]],[2,[15,"d"],"convertParameters",[7,[15,"t"],[17,[15,"d"],"GOLD_NUMERIC_FIELDS"],[51,"",[7,"u"],[36,["i",[15,"u"]]]]]],["g",[15,"o"],[8,"firstPartyUrl",["n",[15,"t"]]]],["e",[15,"o"],[15,"t"],[8,"noTargetGroup",true]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__hid",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getHtmlId"]],["$0"]]]]
 ,[50,"__hl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnHistoryChange"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__html",[46,"a"],[52,"b",["require","internal.injectHtml"]],["b",[17,[15,"a"],"html"],[17,[15,"a"],"gtmOnSuccess"],[17,[15,"a"],"gtmOnFailure"],[17,[15,"a"],"useIframe"],[17,[15,"a"],"supportDocumentWrite"]]]
 ,[50,"__lcl",[46,"a"],[52,"b",["require","makeInteger"]],[52,"c",["require","makeString"]],[52,"d",["require","internal.enableAutoEventOnLinkClick"]],[52,"e",[8]],[22,[17,[15,"a"],"waitForTags"],[46,[53,[43,[15,"e"],"waitForTags",true],[43,[15,"e"],"waitForTagsTimeout",["b",[17,[15,"a"],"waitForTagsTimeout"]]]]]],[22,[17,[15,"a"],"checkValidation"],[46,[53,[43,[15,"e"],"checkValidation",true]]]],[52,"f",[30,[17,[15,"a"],"uniqueTriggerId"],"0"]],["d",[15,"e"],[15,"f"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__r",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","generateRandom"]],["$0",[30,[17,[15,"a"],"min"],0],[30,[17,[15,"a"],"max"],2.147483647E9]]]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"getFirstQueryParam",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"getProtocol",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"getHost",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"getPort",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"getPath",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"getExtension",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"getFragment",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"removeFragment",[7,["f",[15,"j"]]]]]]]]]]
 ,[50,"__v",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getType"]],[52,"e",[17,[15,"a"],"name"]],[22,[30,[28,[15,"e"]],[21,["d",[15,"e"]],"string"]],[46,[36,false]]],[52,"f",[2,[15,"e"],"replace",[7,["c","\\\\.","g"],"."]]],[52,"g",["b",[15,"f"],[30,[17,[15,"a"],"dataLayerVersion"],1]]],[36,[39,[21,[15,"g"],[44]],[15,"g"],[17,[15,"a"],"defaultValue"]]]]
 ,[50,"__vis",[46,"a"],[50,"j",[46,"p"],[52,"q",[7]],[2,[15,"q"],"push",[7,[39,[20,[17,[15,"p"],"selectorType"],"CSS"],[17,[15,"p"],"elementSelector"],[0,"#",[17,[15,"p"],"elementId"]]]]],[2,[15,"q"],"push",[7,[17,[15,"p"],"outputMethod"]]],[22,[12,[17,[15,"p"],"outputMethod"],"BOOLEAN"],[46,[53,[2,[15,"q"],"push",[7,[17,[15,"p"],"onScreenRatio"]]]]]],[36,[2,[15,"q"],"join",[7,"&"]]]],[52,"b",["require","Math"]],[52,"c",["require","internal.getElementVisibilityRatio"]],[52,"d",["require","templateStorage"]],[52,"e",["require","getTimestampMillis"]],[52,"f",["require","internal.getElementById"]],[52,"g",["require","internal.getElementsByCssSelector"]],[52,"h",["require","makeNumber"]],[52,"i",250],[52,"k",["j",[15,"a"]]],[41,"l"],[3,"l",[2,[15,"d"],"getItem",[7,[15,"k"]]]],[52,"m",["e"]],[22,[1,[15,"l"],[23,[37,[15,"m"],[17,[15,"l"],"time"]],[15,"i"]]],[46,[53,[36,[17,[15,"l"],"value"]]]],[46,[53,[3,"l",[8,"time",[15,"m"],"value",[45]]]]]],[52,"n",[17,[15,"a"],"outputMethod"]],[41,"o"],[3,"o",[45]],[22,[12,[17,[15,"a"],"selectorType"],"CSS"],[46,[53,[52,"p",["g",[17,[15,"a"],"elementSelector"]]],[22,[1,[15,"p"],[18,[17,[15,"p"],"length"],0]],[46,[53,[3,"o",[16,[15,"p"],0]]]]]]],[46,[53,[3,"o",["f",[17,[15,"a"],"elementId"]]]]]],[22,[15,"o"],[46,[53,[52,"p",["c",[15,"o"]]],[38,[15,"n"],[46,"BOOLEAN","PERCENT"],[46,[5,[46,[52,"q",[10,[30,["h",[17,[15,"a"],"onScreenRatio"]],50],100]],[43,[15,"l"],"value",[1,[21,[15,"p"],0],[19,[15,"p"],[15,"q"]]]],[4]]],[5,[46,[43,[15,"l"],"value",0],[22,[15,"p"],[46,[53,[43,[15,"l"],"value",[10,[2,[15,"b"],"round",[7,[26,[15,"p"],1000]]],10]]]]],[4]]]]]]]],[2,[15,"d"],"setItem",[7,[15,"k"],[15,"l"]]],[36,[17,[15,"l"],"value"]]]
 ,[52,"__module_gtag",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46,"g","h","i"],[65,"j",[15,"h"],[46,[53,[22,[2,[15,"g"],"hasOwnProperty",[7,[15,"j"]]],[46,[53,[43,[15,"g"],[15,"j"],["i",[16,[15,"g"],[15,"j"]]]]]]]]]]],[52,"b",["require","Object"]],[52,"c",[2,[15,"b"],"freeze",[7,[8,"EP_FIRST_PARTY_COLLECTION","first_party_collection","EP_SERVER_CONTAINER_URL","server_container_url","EP_TRANSPORT_URL","transport_url","EP_USER_PROPERTIES","user_properties"]]]],[52,"d",[2,[15,"b"],"freeze",[7,[7,"allow_ad_personalization_signals","allow_direct_google_requests","allow_google_signals","cookie_update","ignore_referrer","update","first_party_collection","send_page_view"]]]],[52,"e",[2,[15,"b"],"freeze",[7,[7,"cookie_expires","event_timeout","session_duration","session_engaged_time","engagement_time_msec"]]]],[36,[8,"SCHEMA",[15,"c"],"GOLD_BOOLEAN_FIELDS",[15,"d"],"GOLD_NUMERIC_FIELDS",[15,"e"],"convertParameters",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"removeFragment",[15,"h"],"getProtocol",[15,"i"],"getHost",[15,"j"],"getPort",[15,"k"],"getPath",[15,"l"],"getExtension",[15,"m"],"getFragment",[15,"n"],"getFirstQueryParam",[15,"o"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__aev":{"2":true}
,
"__c":{"2":true,"4":true}
,
"__cid":{"2":true,"4":true,"3":true}
,
"__ctv":{"2":true,"3":true}
,
"__e":{"2":true,"4":true}
,
"__f":{"2":true}
,
"__googtag":{"1":10}
,
"__r":{"2":true}
,
"__u":{"2":true}
,
"__v":{"2":true}
,
"__vis":{"2":true}


}
,"blob":{"1":"8"}
,"permissions":{
"__aev":{"read_data_layer":{"allowedKeys":"specific","keyPatterns":["gtm"]},"read_event_data":{"eventDataAccess":"any"},"read_dom_element_text":{},"get_element_attributes":{"allowedAttributes":"any"},"get_url":{"urlParts":"any"},"access_dom_element_properties":{"properties":[{"property":"tagName","read":true}]},"access_template_storage":{},"access_element_values":{"allowRead":[true],"allowWrite":[false]}}
,
"__c":{}
,
"__cid":{"read_container_data":{}}
,
"__cl":{"detect_click_events":{}}
,
"__ctv":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__googtag":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"gtag","read":true,"write":true,"execute":true},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"configure_google_tags":{"allowedTagIds":"any"},"load_google_tags":{"allowedTagIds":"any","allowFirstPartyUrls":true,"allowedFirstPartyUrls":"any"}}
,
"__hid":{}
,
"__hl":{"detect_history_change_events":{}}
,
"__html":{"unsafe_inject_arbitrary_html":{}}
,
"__lcl":{"detect_link_click_events":{"allowWaitForTags":true}}
,
"__r":{}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}
,
"__v":{"read_data_layer":{"allowedKeys":"any"}}
,
"__vis":{"read_dom_elements":{"allowedElementIds":"any","allowedCssSelectors":"any"},"access_template_storage":{},"read_document_dimensions":{},"read_document_visibility_state":{},"read_element_style":{},"read_element_dimensions":{}}


}



,"security_groups":{
"customScripts":[
"__html"

]
,
"google":[
"__aev"
,
"__c"
,
"__cid"
,
"__cl"
,
"__ctv"
,
"__e"
,
"__f"
,
"__googtag"
,
"__hid"
,
"__hl"
,
"__r"
,
"__u"
,
"__v"
,
"__vis"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},da=ca(this),ea=function(a,b){if(b)a:{for(var c=da,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
ea("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var fa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ka;
if(typeof Object.setPrototypeOf=="function")ka=Object.setPrototypeOf;else{var ma;a:{var na={a:!0},oa={};try{oa.__proto__=na;ma=oa.a;break a}catch(a){}ma=!1}ka=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var pa=ka,qa=function(a,b){a.prototype=fa(b.prototype);a.prototype.constructor=a;if(pa)pa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Zo=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ra=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},sa=function(a){return a instanceof Array?a:ra(l(a))},ua=function(a){return ta(a,a)},ta=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},va=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ea("Object.assign",function(a){return a||va});
var wa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var xa=this||self;var ya=function(a,b){this.type=a;this.data=b};var za=function(){this.map={};this.C={}};za.prototype.get=function(a){return this.map["dust."+a]};za.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};za.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};za.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ba=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};za.prototype.ra=function(){return Ba(this,1)};za.prototype.fc=function(){return Ba(this,2)};za.prototype.Hb=function(){return Ba(this,3)};var Ca=function(){};Ca.prototype.reset=function(){};var Da=function(a,b){this.O=a;this.parent=b;this.C=this.H=void 0;this.Bc=!1;this.N=function(c,d,e){return c.apply(d,e)};this.values=new za};Da.prototype.add=function(a,b){Ea(this,a,b,!1)};var Ea=function(a,b,c,d){if(!a.Bc)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};Da.prototype.set=function(a,b){this.Bc||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Da.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Da.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Fa=function(a){var b=new Da(a.O,a);a.H&&(b.H=a.H);b.N=a.N;b.C=a.C;return b};Da.prototype.Jd=function(){return this.O};Da.prototype.Qa=function(){this.Bc=!0};var Ga=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.zk=a;this.lk=c===void 0?!1:c;this.debugInfo=[];this.C=b};qa(Ga,Error);var Ha=function(a){return a instanceof Ga?a:new Ga(a,void 0,!0)};function Ja(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ka(a,e.value),c instanceof ya);e=d.next());return c}function Ka(a,b){try{var c=l(b),d=c.next().value,e=ra(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ha(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(sa(e)))}catch(h){var g=a.H;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var La=function(){this.H=new Ca;this.C=new Da(this.H)};k=La.prototype;k.Jd=function(){return this.H};k.execute=function(a){return this.Bi([a].concat(sa(wa.apply(1,arguments))))};k.Bi=function(){for(var a,b=l(wa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ka(this.C,c.value);return a};k.am=function(a){var b=wa.apply(1,arguments),c=Fa(this.C);c.C=a;for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ka(c,f.value);return d};k.Qa=function(){this.C.Qa()};var Ma=function(){this.wa=!1;this.W=new za};k=Ma.prototype;k.get=function(a){return this.W.get(a)};k.set=function(a,b){this.wa||this.W.set(a,b)};k.has=function(a){return this.W.has(a)};k.remove=function(a){this.wa||this.W.remove(a)};k.ra=function(){return this.W.ra()};k.fc=function(){return this.W.fc()};k.Hb=function(){return this.W.Hb()};k.Qa=function(){this.wa=!0};k.Bc=function(){return this.wa};function Na(){for(var a=Oa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Pa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Oa,Ra;function Sa(a){Oa=Oa||Pa();Ra=Ra||Na();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Oa[m],Oa[n],Oa[p],Oa[q])}return b.join("")}
function Ta(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Ra[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Oa=Oa||Pa();Ra=Ra||Na();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Ua={};function Wa(a,b){Ua[a]=Ua[a]||[];Ua[a][b]=!0}function Xa(a){var b=Ua[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Sa(c.join("")).replace(/\.+$/,"")}function Ya(){for(var a=[],b=Ua.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function Za(){}function $a(a){return typeof a==="function"}function ab(a){return typeof a==="string"}function bb(a){return typeof a==="number"&&!isNaN(a)}function cb(a){return Array.isArray(a)?a:[a]}function eb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function fb(a,b){if(!bb(a)||!bb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function gb(a,b){for(var c=new hb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function ib(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function jb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function kb(a){return Math.round(Number(a))||0}function lb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function mb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function nb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function ob(){return new Date(Date.now())}function pb(){return ob().getTime()}var hb=function(){this.prefix="gtm.";this.values={}};hb.prototype.set=function(a,b){this.values[this.prefix+a]=b};hb.prototype.get=function(a){return this.values[this.prefix+a]};hb.prototype.contains=function(a){return this.get(a)!==void 0};
function qb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function rb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function sb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function tb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function ub(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function vb(a,b){var c=z;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function wb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var xb=/^\w{1,9}$/;function yb(a,b){a=a||{};b=b||",";var c=[];ib(a,function(d,e){xb.test(d)&&e&&c.push(d)});return c.join(b)}function zb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Ab(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Bb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Cb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Db=globalThis.trustedTypes,Fb;function Gb(){var a=null;if(!Db)return a;try{var b=function(c){return c};a=Db.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Hb(){Fb===void 0&&(Fb=Gb());return Fb};var Ib=function(a){this.C=a};Ib.prototype.toString=function(){return this.C+""};function Jb(a){var b=a,c=Hb(),d=c?c.createScriptURL(b):b;return new Ib(d)}function Kb(a){if(a instanceof Ib)return a.C;throw Error("");};var Lb=ua([""]),Mb=ta(["\x00"],["\\0"]),Nb=ta(["\n"],["\\n"]),Ob=ta(["\x00"],["\\u0000"]);function Pb(a){return a.toString().indexOf("`")===-1}Pb(function(a){return a(Lb)})||Pb(function(a){return a(Mb)})||Pb(function(a){return a(Nb)})||Pb(function(a){return a(Ob)});var Qb=function(a){this.C=a};Qb.prototype.toString=function(){return this.C};var Rb=function(a){this.Dn=a};function Sb(a){return new Rb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Tb=[Sb("data"),Sb("http"),Sb("https"),Sb("mailto"),Sb("ftp"),new Rb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Ub(a){var b;b=b===void 0?Tb:b;if(a instanceof Qb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Rb&&d.Dn(a))return new Qb(a)}}var Vb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function Wb(a){var b;if(a instanceof Qb)if(a instanceof Qb)b=a.C;else throw Error("");else b=Vb.test(a)?a:void 0;return b};function Xb(a,b){var c=Wb(b);c!==void 0&&(a.action=c)};function Yb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var Zb=function(a){this.C=a};Zb.prototype.toString=function(){return this.C+""};var ac=function(){this.C=$b[0].toLowerCase()};ac.prototype.toString=function(){return this.C};function bc(a,b){var c=[new ac];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof ac)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var cc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function dc(a){return a===null?"null":a===void 0?"undefined":a};var z=window,ec=window.history,A=document,fc=navigator;function gc(){var a;try{a=fc.serviceWorker}catch(b){return}return a}var hc=A.currentScript,ic=hc&&hc.src;function jc(a,b){var c=z[a];z[a]=c===void 0?b:c;return z[a]}function kc(a){return(fc.userAgent||"").indexOf(a)!==-1}function lc(){return kc("Firefox")||kc("FxiOS")}function mc(){return(kc("GSA")||kc("GoogleApp"))&&(kc("iPhone")||kc("iPad"))}function nc(){return kc("Edg/")||kc("EdgA/")||kc("EdgiOS/")}
var oc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},pc={onload:1,src:1,width:1,height:1,style:1};function qc(a,b,c){b&&ib(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function rc(a,b,c,d,e){var f=A.createElement("script");qc(f,d,oc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Jb(dc(a));f.src=Kb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function sc(){if(ic){var a=ic.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function tc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);qc(g,c,pc);d&&ib(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function uc(a,b,c,d){return vc(a,b,c,d)}function wc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function xc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function D(a){z.setTimeout(a,0)}function yc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function zc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Ac(a){var b=A.createElement("div"),c=b,d,e=dc("A<div>"+a+"</div>"),f=Hb(),g=f?f.createHTML(e):e;d=new Zb(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof Zb)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Bc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Cc(a,b,c){var d;try{d=fc.sendBeacon&&fc.sendBeacon(a)}catch(e){Wa("TAGGING",15)}d?b==null||b():vc(a,b,c)}function Dc(a,b){try{return fc.sendBeacon(a,b)}catch(c){Wa("TAGGING",15)}return!1}var Ec={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Fc(a,b,c,d,e){if(Gc()){var f=Object.assign({},Ec);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=z.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.ki)return e==null||e(),!1;if(b){var h=
Dc(a,b);h?d==null||d():e==null||e();return h}Hc(a,d,e);return!0}function Gc(){return typeof z.fetch==="function"}function Ic(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Jc(){var a=z.performance;if(a&&$a(a.now))return a.now()}
function Kc(){var a,b=z.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Lc(){return z.performance||void 0}function Mc(){var a=z.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var vc=function(a,b,c,d){var e=new Image(1,1);qc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Hc=Cc;function Nc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Oc(a,b){return this.evaluate(a)===this.evaluate(b)}function Pc(a,b){return this.evaluate(a)||this.evaluate(b)}function Qc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Rc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Sc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=z.location.href;d instanceof Ma&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Tc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Uc=function(a){if(a==null)return String(a);var b=Tc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Vc=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},Wc=function(a){if(!a||Uc(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Vc(a,"constructor")&&!Vc(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
Vc(a,b)},Xc=function(a,b){var c=b||(Uc(a)=="array"?[]:{}),d;for(d in a)if(Vc(a,d)){var e=a[d];Uc(e)=="array"?(Uc(c[d])!="array"&&(c[d]=[]),c[d]=Xc(e,c[d])):Wc(e)?(Wc(c[d])||(c[d]={}),c[d]=Xc(e,c[d])):c[d]=e}return c};function Yc(a){if(a==void 0||Array.isArray(a)||Wc(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function Zc(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var ad=function(a){a=a===void 0?[]:a;this.W=new za;this.values=[];this.wa=!1;for(var b in a)a.hasOwnProperty(b)&&(Zc(b)?this.values[Number(b)]=a[Number(b)]:this.W.set(b,a[b]))};k=ad.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof ad?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.wa)if(a==="length"){if(!Zc(b))throw Ha(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else Zc(a)?this.values[Number(a)]=b:this.W.set(a,b)};k.get=function(a){return a==="length"?this.length():Zc(a)?this.values[Number(a)]:this.W.get(a)};k.length=function(){return this.values.length};k.ra=function(){for(var a=this.W.ra(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.fc=function(){for(var a=this.W.fc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Hb=function(){for(var a=this.W.Hb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){Zc(a)?delete this.values[Number(a)]:this.wa||this.W.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,sa(wa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=wa.apply(2,arguments);return b===void 0&&c.length===0?new ad(this.values.splice(a)):new ad(this.values.splice.apply(this.values,[a,b||0].concat(sa(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,sa(wa.apply(0,arguments)))};k.has=function(a){return Zc(a)&&this.values.hasOwnProperty(a)||this.W.has(a)};k.Qa=function(){this.wa=!0;Object.freeze(this.values)};k.Bc=function(){return this.wa};
function bd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var cd=function(a,b){this.functionName=a;this.Id=b;this.W=new za;this.wa=!1};k=cd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new ad(this.ra())};k.invoke=function(a){return this.Id.call.apply(this.Id,[new dd(this,a)].concat(sa(wa.apply(1,arguments))))};k.kb=function(a){var b=wa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(sa(b)))}catch(c){}};k.get=function(a){return this.W.get(a)};
k.set=function(a,b){this.wa||this.W.set(a,b)};k.has=function(a){return this.W.has(a)};k.remove=function(a){this.wa||this.W.remove(a)};k.ra=function(){return this.W.ra()};k.fc=function(){return this.W.fc()};k.Hb=function(){return this.W.Hb()};k.Qa=function(){this.wa=!0};k.Bc=function(){return this.wa};var ed=function(a,b){cd.call(this,a,b)};qa(ed,cd);var fd=function(a,b){cd.call(this,a,b)};qa(fd,cd);var dd=function(a,b){this.Id=a;this.J=b};
dd.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?Ka(b,a):a};dd.prototype.getName=function(){return this.Id.getName()};dd.prototype.Jd=function(){return this.J.Jd()};var gd=function(){this.map=new Map};gd.prototype.set=function(a,b){this.map.set(a,b)};gd.prototype.get=function(a){return this.map.get(a)};var hd=function(){this.keys=[];this.values=[]};hd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};hd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function id(){try{return Map?new gd:new hd}catch(a){return new hd}};var jd=function(a){if(a instanceof jd)return a;if(Yc(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};jd.prototype.getValue=function(){return this.value};jd.prototype.toString=function(){return String(this.value)};var ld=function(a){this.promise=a;this.wa=!1;this.W=new za;this.W.set("then",kd(this));this.W.set("catch",kd(this,!0));this.W.set("finally",kd(this,!1,!0))};k=ld.prototype;k.get=function(a){return this.W.get(a)};k.set=function(a,b){this.wa||this.W.set(a,b)};k.has=function(a){return this.W.has(a)};k.remove=function(a){this.wa||this.W.remove(a)};k.ra=function(){return this.W.ra()};k.fc=function(){return this.W.fc()};k.Hb=function(){return this.W.Hb()};
var kd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new ed("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof ed||(d=void 0);e instanceof ed||(e=void 0);var f=Fa(this.J),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new jd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new ld(h)})};ld.prototype.Qa=function(){this.wa=!0};ld.prototype.Bc=function(){return this.wa};function md(a,b,c){var d=id(),e=function(g,h){for(var m=g.ra(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof ad){var m=[];d.set(g,m);for(var n=g.ra(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof ld)return g.promise.then(function(u){return md(u,b,1)},function(u){return Promise.reject(md(u,b,1))});if(g instanceof Ma){var q={};d.set(g,q);e(g,q);return q}if(g instanceof ed){var r=function(){for(var u=
wa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=nd(u[w],b,c);var x=new Da(b?b.Jd():new Ca);b&&(x.C=b.C);return f(g.invoke.apply(g,[x].concat(sa(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof jd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function nd(a,b,c){var d=id(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||jb(g)){var m=new ad;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(Wc(g)){var p=new Ma;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new ed("",function(){for(var u=wa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=md(this.evaluate(u[w]),b,c);return f((0,this.J.N)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new jd(g)};return f(a)};var od={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof ad)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new ad(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new ad(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new ad(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
sa(wa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ha(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ha(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ha(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ha(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=bd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new ad(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=bd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(sa(wa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,sa(wa.apply(1,arguments)))}};var pd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},qd=new ya("break"),rd=new ya("continue");function sd(a,b){return this.evaluate(a)+this.evaluate(b)}function td(a,b){return this.evaluate(a)&&this.evaluate(b)}
function ud(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof ad))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ha(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=md(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ha(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(pd.hasOwnProperty(e)){var m=2;m=1;var n=md(f,void 0,m);return nd(d[e].apply(d,n),this.J)}throw Ha(Error("TypeError: "+e+" is not a function"));}if(d instanceof ad){if(d.has(e)){var p=d.get(String(e));if(p instanceof ed){var q=bd(f);return p.invoke.apply(p,[this.J].concat(sa(q)))}throw Ha(Error("TypeError: "+e+" is not a function"));}if(od.supportedMethods.indexOf(e)>=
0){var r=bd(f);return od[e].call.apply(od[e],[d,this.J].concat(sa(r)))}}if(d instanceof ed||d instanceof Ma||d instanceof ld){if(d.has(e)){var t=d.get(e);if(t instanceof ed){var u=bd(f);return t.invoke.apply(t,[this.J].concat(sa(u)))}throw Ha(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof ed?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof jd&&e==="toString")return d.toString();throw Ha(Error("TypeError: Object has no '"+
e+"' property."));}function vd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function wd(){var a=wa.apply(0,arguments),b=Fa(this.J),c=Ja(b,a);if(c instanceof ya)return c}function xd(){return qd}function yd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof ya)return d}}
function zd(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ea(a,c,d,!0)}}}function Ad(){return rd}function Bd(a,b){return new ya(a,this.evaluate(b))}function Cd(a,b){for(var c=wa.apply(2,arguments),d=new ad,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(sa(c));this.J.add(a,this.evaluate(g))}function Dd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Ed(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof jd,f=d instanceof jd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Fd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Gd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ja(f,d);if(g instanceof ya){if(g.type==="break")break;if(g.type==="return")return g}}}
function Hd(a,b,c){if(typeof b==="string")return Gd(a,function(){return b.length},function(f){return f},c);if(b instanceof Ma||b instanceof ld||b instanceof ad||b instanceof ed){var d=b.ra(),e=d.length;return Gd(a,function(){return e},function(f){return d[f]},c)}}function Id(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Hd(function(h){g.set(d,h);return g},e,f)}
function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Hd(function(h){var m=Fa(g);Ea(m,d,h,!0);return m},e,f)}function Kd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Hd(function(h){var m=Fa(g);m.add(d,h);return m},e,f)}function Ld(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Md(function(h){g.set(d,h);return g},e,f)}
function Nd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Md(function(h){var m=Fa(g);Ea(m,d,h,!0);return m},e,f)}function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Md(function(h){var m=Fa(g);m.add(d,h);return m},e,f)}
function Md(a,b,c){if(typeof b==="string")return Gd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof ad)return Gd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ha(Error("The value is not iterable."));}
function Pd(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof ad))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),m=Fa(g);for(e(g,m);Ka(m,b);){var n=Ja(m,h);if(n instanceof ya){if(n.type==="break")break;if(n.type==="return")return n}var p=Fa(g);e(m,p);Ka(p,c);m=p}}
function Qd(a,b){var c=wa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof ad))throw Error("Error: non-List value given for Fn argument names.");return new ed(a,function(){return function(){var f=wa.apply(0,arguments),g=Fa(d);g.C===void 0&&(g.C=this.J.C);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new ad(h));var r=Ja(g,c);if(r instanceof ya)return r.type===
"return"?r.data:r}}())}function Rd(a){var b=this.evaluate(a),c=this.J;if(Sd&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Td(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ha(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ma||d instanceof ld||d instanceof ad||d instanceof ed)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:Zc(e)&&(c=d[e]);else if(d instanceof jd)return;return c}function Ud(a,b){return this.evaluate(a)>this.evaluate(b)}function Vd(a,b){return this.evaluate(a)>=this.evaluate(b)}
function Wd(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof jd&&(c=c.getValue());d instanceof jd&&(d=d.getValue());return c===d}function Xd(a,b){return!Wd.call(this,a,b)}function Yd(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ja(this.J,d);if(e instanceof ya)return e}var Sd=!1;
function Zd(a,b){return this.evaluate(a)<this.evaluate(b)}function $d(a,b){return this.evaluate(a)<=this.evaluate(b)}function ae(){for(var a=new ad,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function be(){for(var a=new Ma,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ce(a,b){return this.evaluate(a)%this.evaluate(b)}
function de(a,b){return this.evaluate(a)*this.evaluate(b)}function ee(a){return-this.evaluate(a)}function fe(a){return!this.evaluate(a)}function ge(a,b){return!Ed.call(this,a,b)}function he(){return null}function ie(a,b){return this.evaluate(a)||this.evaluate(b)}function je(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ke(a){return this.evaluate(a)}function le(){return wa.apply(0,arguments)}function me(a){return new ya("return",this.evaluate(a))}
function ne(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ha(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof ed||d instanceof ad||d instanceof Ma)&&d.set(String(e),f);return f}function oe(a,b){return this.evaluate(a)-this.evaluate(b)}
function pe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof ya){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof ya&&(g.type==="return"||g.type==="continue")))return g}
function qe(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function re(a){var b=this.evaluate(a);return b instanceof ed?"function":typeof b}function se(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function te(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ja(this.J,e);if(f instanceof ya){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ja(this.J,e);if(g instanceof ya){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function ue(a){return~Number(this.evaluate(a))}function ve(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function we(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function xe(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function ye(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function ze(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ae(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Be(){}
function Ce(a,b,c){try{var d=this.evaluate(b);if(d instanceof ya)return d}catch(h){if(!(h instanceof Ga&&h.lk))throw h;var e=Fa(this.J);a!==""&&(h instanceof Ga&&(h=h.zk),e.add(a,new jd(h)));var f=this.evaluate(c),g=Ja(e,f);if(g instanceof ya)return g}}function De(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ga&&f.lk))throw f;c=f}var e=this.evaluate(b);if(e instanceof ya)return e;if(c)throw c;if(d instanceof ya)return d};var Fe=function(){this.C=new La;Ee(this)};Fe.prototype.execute=function(a){return this.C.Bi(a)};var Ee=function(a){var b=function(c,d){var e=new fd(String(c),d);e.Qa();a.C.C.set(String(c),e)};b("map",be);b("and",Nc);b("contains",Qc);b("equals",Oc);b("or",Pc);b("startsWith",Rc);b("variable",Sc)};var He=function(){this.H=!1;this.C=new La;Ge(this);this.H=!0};He.prototype.execute=function(a){return Ie(this.C.Bi(a))};var Je=function(a,b,c){return Ie(a.C.am(b,c))};He.prototype.Qa=function(){this.C.Qa()};
var Ge=function(a){var b=function(c,d){var e=String(c),f=new fd(e,d);f.Qa();a.C.C.set(e,f)};b(0,sd);b(1,td);b(2,ud);b(3,vd);b(56,ye);b(57,ve);b(58,ue);b(59,Ae);b(60,we);b(61,xe);b(62,ze);b(53,wd);b(4,xd);b(5,yd);b(68,Ce);b(52,zd);b(6,Ad);b(49,Bd);b(7,ae);b(8,be);b(9,yd);b(50,Cd);b(10,Dd);b(12,Ed);b(13,Fd);b(67,De);b(51,Qd);b(47,Id);b(54,Jd);b(55,Kd);b(63,Pd);b(64,Ld);b(65,Nd);b(66,Od);b(15,Rd);b(16,Td);b(17,Td);b(18,Ud);b(19,Vd);b(20,Wd);b(21,Xd);b(22,Yd);b(23,Zd);b(24,$d);b(25,ce);b(26,de);b(27,
ee);b(28,fe);b(29,ge);b(45,he);b(30,ie);b(32,je);b(33,je);b(34,ke);b(35,ke);b(46,le);b(36,me);b(43,ne);b(37,oe);b(38,pe);b(39,qe);b(40,re);b(44,Be);b(41,se);b(42,te)};He.prototype.Jd=function(){return this.C.Jd()};function Ie(a){if(a instanceof ya||a instanceof ed||a instanceof ad||a instanceof Ma||a instanceof ld||a instanceof jd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ke=function(a){this.message=a};function Le(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ke("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Me(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Ne=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Oe(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Le(e)+c}a<<=2;d||(a|=32);return c=""+Le(a|b)+c};var Pe=function(){function a(b){return{toString:function(){return b}}}return{Wk:a("consent"),Li:a("convert_case_to"),Mi:a("convert_false_to"),Ni:a("convert_null_to"),Oi:a("convert_true_to"),Pi:a("convert_undefined_to"),wo:a("debug_mode_metadata"),Aa:a("function"),sh:a("instance_name"),fm:a("live_only"),gm:a("malware_disabled"),METADATA:a("metadata"),jm:a("original_activity_id"),Io:a("original_vendor_template_id"),Ho:a("once_on_load"),im:a("once_per_event"),Tj:a("once_per_load"),Jo:a("priority_override"),
Ko:a("respected_consent_types"),bk:a("setup_tags"),og:a("tag_id"),ek:a("teardown_tags")}}();var of;var pf=[],qf=[],rf=[],sf=[],tf=[],uf,vf,wf;function xf(a){wf=wf||a}
function yf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)pf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)sf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)rf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||zf(p[r])}qf.push(p)}}
function zf(a){}var Af,Bf=[],Cf=[];function Df(a,b){var c={};c[Pe.Aa]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Ef(a,b,c){try{return vf(Ff(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Gf(a){var b=a[Pe.Aa];if(!b)throw Error("Error: No function name given for function call.");return!!uf[b]}
var Ff=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Hf(a[e],b,c));return d},Hf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Hf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=pf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[Pe.sh]);try{var m=Ff(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=If(m,{event:b,index:f,type:2,
name:h});Af&&(d=Af.Em(d,m))}catch(y){b.logMacroError&&b.logMacroError(y,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Hf(a[n],b,c)]=Hf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Hf(a[q],b,c);wf&&(p=p||wf.An(r));d.push(r)}return wf&&p?wf.Jm(d):d.join("");case "escape":d=Hf(a[1],b,c);if(wf&&Array.isArray(a[1])&&a[1][0]==="macro"&&wf.Bn(a))return wf.Tn(d);d=String(d);for(var t=2;t<a.length;t++)We[a[t]]&&(d=We[a[t]](d));return d;
case "tag":var u=a[1];if(!sf[u])throw Error("Unable to resolve tag reference "+u+".");return{qk:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[Pe.Aa]=a[1];var w=Ef(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},If=function(a,b){var c=a[Pe.Aa],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=uf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Bf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&ub(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=pf[q];break;case 1:r=sf[q];break;default:n="";break a}var t=r&&r[Pe.sh];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Cf.indexOf(c)===-1){Cf.push(c);
var x=pb();u=e(g);var y=pb()-x,B=pb();v=of(c,h,b);w=y-(pb()-B)}else if(e&&(u=e(g)),!e||f)v=of(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),Yc(u)?(Array.isArray(u)?Array.isArray(v):Wc(u)?Wc(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Jf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};qa(Jf,Error);Jf.prototype.getMessage=function(){return this.message};function Kf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Kf(a[c],b[c])}};function Lf(){return function(a,b){var c;var d=Mf;a instanceof Ga?(a.C=d,c=a):c=new Ga(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Mf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)bb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Nf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Of(a),f=0;f<qf.length;f++){var g=qf[f],h=Pf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<sf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Pf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Of(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Ef(rf[c],a));return b[c]}};function Qf(a,b){b[Pe.Li]&&typeof a==="string"&&(a=b[Pe.Li]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Pe.Ni)&&a===null&&(a=b[Pe.Ni]);b.hasOwnProperty(Pe.Pi)&&a===void 0&&(a=b[Pe.Pi]);b.hasOwnProperty(Pe.Oi)&&a===!0&&(a=b[Pe.Oi]);b.hasOwnProperty(Pe.Mi)&&a===!1&&(a=b[Pe.Mi]);return a};var Rf=function(){this.C={}},Tf=function(a,b){var c=Sf.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,sa(wa.apply(0,arguments)))})};function Uf(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Jf(c,d,g);}}
function Vf(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(sa(wa.apply(1,arguments))));Uf(e,b,d,g);Uf(f,b,d,g)}}}};var Zf=function(){var a=data.permissions||{},b=Wf.ctid,c=this;this.H={};this.C=new Rf;var d={},e={},f=Vf(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(sa(wa.apply(1,arguments)))):{}});ib(a,function(g,h){function m(p){var q=wa.apply(1,arguments);if(!n[p])throw Xf(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(sa(q)))}var n={};ib(h,function(p,q){var r=Yf(p,q);n[p]=r.assert;d[p]||(d[p]=r.P);r.ik&&!e[p]&&(e[p]=r.ik)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw Xf(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(sa(t.slice(1))))}})},$f=function(a){return Sf.H[a]||function(){}};
function Yf(a,b){var c=Df(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=Xf;try{return If(c)}catch(d){return{assert:function(e){throw new Jf(e,{},"Permission "+e+" is unknown.");},P:function(){throw new Jf(a,{},"Permission "+a+" is unknown.");}}}}function Xf(a,b,c){return new Jf(a,b,c)};var ag=!1;var bg={};bg.Ok=lb('');bg.Pm=lb('');function gg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var hg=[],ig={};function jg(a){return hg[a]===void 0?!1:hg[a]};var kg=[];function lg(a){switch(a){case 0:return 0;case 37:return 13;case 49:return 10;case 50:return 11;case 51:return 1;case 52:return 2;case 53:return 7;case 73:return 3;case 102:return 14;case 113:return 12;case 114:return 4;case 116:return 5;case 134:return 9;case 135:return 6}}function mg(a,b){kg[a]=b;var c=lg(a);c!==void 0&&(hg[c]=b)}function G(a){mg(a,!0)}G(38);G(33);G(34);G(35);
G(55);G(144);G(17);
G(152);G(143);G(74);G(119);
G(57);G(4);G(110);
G(138);G(100);G(89);G(115);
G(158);G(131);G(19);
G(71);G(112);G(153);
G(116);mg(22,!1),G(23);
ig[1]=gg('1',6E4);ig[3]=gg('10',1);ig[2]=gg('',50);G(28);
G(8);G(88);G(139);G(122);
G(156);G(135);G(126);G(26);G(68);G(69);
G(134);G(50);G(49);G(92);
G(99);
G(111);G(62);
G(151);G(98);G(133);G(114);
G(93);G(30);G(21);
G(54);G(13);G(149);
G(150);G(56);G(94);
G(10);G(14);G(85);G(95);G(105);
G(75);G(76);
G(78);G(27);
G(79);G(87);G(117);

function H(a){return!!kg[a]}function ng(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?G(b):G(a)};var pg={},qg=(pg.uaa=!0,pg.uab=!0,pg.uafvl=!0,pg.uamb=!0,pg.uam=!0,pg.uap=!0,pg.uapv=!0,pg.uaw=!0,pg);
var yg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!wg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!xg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?ub(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},xg=/^[a-z$_][\w-$]*$/i,wg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var zg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Ag(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Bg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Cg=new hb;function Dg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Cg.get(e);f||(f=new RegExp(b,d),Cg.set(e,f));return f.test(a)}catch(g){return!1}}function Eg(a,b){return String(a).indexOf(String(b))>=0}
function Fg(a,b){return String(a)===String(b)}function Gg(a,b){return Number(a)>=Number(b)}function Hg(a,b){return Number(a)<=Number(b)}function Ig(a,b){return Number(a)>Number(b)}function Jg(a,b){return Number(a)<Number(b)}function Kg(a,b){return ub(String(a),String(b))};var Rg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Sg={Fn:"function",PixieMap:"Object",List:"Array"};
function Tg(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Rg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof ed?n="Fn":m instanceof ad?n="List":m instanceof Ma?n="PixieMap":m instanceof ld?n="PixiePromise":m instanceof jd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Sg[n]||n)+", which does not match required type ")+
((Sg[h]||h)+"."));}}}function J(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof ed?d.push("function"):g instanceof ad?d.push("Array"):g instanceof Ma?d.push("Object"):g instanceof ld?d.push("Promise"):g instanceof jd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function Ug(a){return a instanceof Ma}function Vg(a){return Ug(a)||a===null||Wg(a)}
function Xg(a){return a instanceof ed}function Yg(a){return Xg(a)||a===null||Wg(a)}function Zg(a){return a instanceof ad}function $g(a){return a instanceof jd}function ah(a){return typeof a==="string"}function bh(a){return ah(a)||a===null||Wg(a)}function ch(a){return typeof a==="boolean"}function dh(a){return ch(a)||Wg(a)}function eh(a){return ch(a)||a===null||Wg(a)}function fh(a){return typeof a==="number"}function Wg(a){return a===void 0};function gh(a){return""+a}
function hh(a,b){var c=[];return c};function ih(a,b){var c=new ed(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ha(g);}});c.Qa();return c}
function jh(a,b){var c=new Ma,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];$a(e)?c.set(d,ih(a+"_"+d,e)):Wc(e)?c.set(d,jh(a+"_"+d,e)):(bb(e)||ab(e)||typeof e==="boolean")&&c.set(d,e)}c.Qa();return c};function kh(a,b){if(!ah(a))throw J(this.getName(),["string"],arguments);if(!bh(b))throw J(this.getName(),["string","undefined"],arguments);var c={},d=new Ma;return d=jh("AssertApiSubject",
c)};function lh(a,b){if(!bh(b))throw J(this.getName(),["string","undefined"],arguments);if(a instanceof ld)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ma;return d=jh("AssertThatSubject",c)};function mh(a){return function(){for(var b=wa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(md(b[e],d));return nd(a.apply(null,c))}}function nh(){for(var a=Math,b=oh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=mh(a[e].bind(a)))}return c};function ph(a){return a!=null&&ub(a,"__cvt_")};function qh(a){var b;return b};function rh(a){var b;if(!ah(a))throw J(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function sh(a){try{return encodeURI(a)}catch(b){}};function th(a){try{return encodeURIComponent(String(a))}catch(b){}};function yh(a){if(!bh(a))throw J(this.getName(),["string|undefined"],arguments);};function zh(a,b){if(!fh(a)||!fh(b))throw J(this.getName(),["number","number"],arguments);return fb(a,b)};function Ah(){return(new Date).getTime()};function Bh(a){if(a===null)return"null";if(a instanceof ad)return"array";if(a instanceof ed)return"function";if(a instanceof jd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Ch(a){function b(c){return function(d){try{return c(d)}catch(e){(ag||bg.Ok)&&a.call(this,e.message)}}}return{parse:b(function(c){return nd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(md(c))}),publicName:"JSON"}};function Dh(a){return kb(md(a,this.J))};function Eh(a){return Number(md(a,this.J))};function Fh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Gh(a,b,c){var d=null,e=!1;if(!Zg(a)||!ah(b)||!ah(c))throw J(this.getName(),["Array","string","string"],arguments);d=new Ma;for(var f=0;f<a.length();f++){var g=a.get(f);g instanceof Ma&&g.has(b)&&g.has(c)&&(d.set(g.get(b),g.get(c)),e=!0)}return e?d:null};var oh="floor ceil round max min abs pow sqrt".split(" ");function Hh(){var a={};return{dn:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Lk:function(b,c){a[b]=c},reset:function(){a={}}}}function Ih(a,b){return function(){return ed.prototype.invoke.apply(a,[b].concat(sa(wa.apply(0,arguments))))}}
function Jh(a,b){if(!ah(a))throw J(this.getName(),["string","any"],arguments);}
function Kh(a,b){if(!ah(a)||!Ug(b))throw J(this.getName(),["string","PixieMap"],arguments);};var Lh={};var Mh=function(a){var b=new Ma;if(a instanceof ad)for(var c=a.ra(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof ed)for(var f=a.ra(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Lh.keys=function(a){Tg(this.getName(),arguments);if(a instanceof ad||a instanceof ed||typeof a==="string")a=Mh(a);if(a instanceof Ma||a instanceof ld)return new ad(a.ra());return new ad};
Lh.values=function(a){Tg(this.getName(),arguments);if(a instanceof ad||a instanceof ed||typeof a==="string")a=Mh(a);if(a instanceof Ma||a instanceof ld)return new ad(a.fc());return new ad};
Lh.entries=function(a){Tg(this.getName(),arguments);if(a instanceof ad||a instanceof ed||typeof a==="string")a=Mh(a);if(a instanceof Ma||a instanceof ld)return new ad(a.Hb().map(function(b){return new ad(b)}));return new ad};
Lh.freeze=function(a){(a instanceof Ma||a instanceof ld||a instanceof ad||a instanceof ed)&&a.Qa();return a};Lh.delete=function(a,b){if(a instanceof Ma&&!a.Bc())return a.remove(b),!0;return!1};function M(a,b){var c=wa.apply(2,arguments),d=a.J.C;if(!d)throw Error("Missing program state.");if(d.Zn){try{d.jk.apply(null,[b].concat(sa(c)))}catch(e){throw Wa("TAGGING",21),e;}return}d.jk.apply(null,[b].concat(sa(c)))};var Nh=function(){this.H={};this.C={};this.N=!0;};Nh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Nh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Nh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:$a(b)?ih(a,b):jh(a,b)};function Oh(a,b){var c=void 0;return c};function Ph(a,b){}Ph.K="internal.safeInvoke";function Qh(){var a={};
return a};var N={m:{Da:"ad_personalization",T:"ad_storage",U:"ad_user_data",Z:"analytics_storage",Nb:"region",mc:"consent_updated",Cf:"wait_for_update",bl:"app_remove",fl:"app_store_refund",il:"app_store_subscription_cancel",jl:"app_store_subscription_convert",kl:"app_store_subscription_renew",ml:"consent_update",Si:"add_payment_info",Ti:"add_shipping_info",jd:"add_to_cart",kd:"remove_from_cart",Ui:"view_cart",Cc:"begin_checkout",ld:"select_item",Pb:"view_item_list",nc:"select_promotion",Qb:"view_promotion",
Va:"purchase",md:"refund",hb:"view_item",Vi:"add_to_wishlist",nl:"exception",ol:"first_open",pl:"first_visit",la:"gtag.config",lb:"gtag.get",ql:"in_app_purchase",Dc:"page_view",rl:"screen_view",sl:"session_start",tl:"source_update",vl:"timing_complete",wl:"track_social",nd:"user_engagement",xl:"user_id_update",Ud:"gclid_link_decoration_source",Vd:"gclid_storage_source",Rb:"gclgb",Wa:"gclid",Wi:"gclid_len",od:"gclgs",pd:"gcllp",rd:"gclst",oa:"ads_data_redaction",Wd:"gad_source",Xd:"gad_source_src",
Ec:"gclid_url",Xi:"gclsrc",Yd:"gbraid",sd:"wbraid",ya:"allow_ad_personalization_signals",Gf:"allow_custom_scripts",Zd:"allow_direct_google_requests",Hf:"allow_display_features",If:"allow_enhanced_conversions",mb:"allow_google_signals",Ja:"allow_interest_groups",yl:"app_id",zl:"app_installer_id",Al:"app_name",Bl:"app_version",Sb:"auid",Cl:"auto_detection_enabled",Fc:"aw_remarketing",Ng:"aw_remarketing_only",Jf:"discount",Kf:"aw_feed_country",Lf:"aw_feed_language",ma:"items",Mf:"aw_merchant_id",Yi:"aw_basket_type",
ae:"campaign_content",be:"campaign_id",ce:"campaign_medium",de:"campaign_name",ee:"campaign",fe:"campaign_source",he:"campaign_term",wb:"client_id",Zi:"rnd",Og:"consent_update_type",Dl:"content_group",El:"content_type",xb:"conversion_cookie_prefix",ie:"conversion_id",Ga:"conversion_linker",Pg:"conversion_linker_disabled",Gc:"conversion_api",Nf:"cookie_deprecation",Xa:"cookie_domain",Ya:"cookie_expires",ib:"cookie_flags",Hc:"cookie_name",yb:"cookie_path",Ta:"cookie_prefix",oc:"cookie_update",ud:"country",
Ka:"currency",Qg:"customer_buyer_stage",je:"customer_lifetime_value",Rg:"customer_loyalty",Sg:"customer_ltv_bucket",ke:"custom_map",Tg:"gcldc",Ic:"dclid",aj:"debug_mode",qa:"developer_id",Fl:"disable_merchant_reported_purchases",Jc:"dc_custom_params",Gl:"dc_natural_search",bj:"dynamic_event_settings",cj:"affiliation",Of:"checkout_option",Ug:"checkout_step",dj:"coupon",me:"item_list_name",Vg:"list_name",Hl:"promotions",ne:"shipping",Wg:"tax",Pf:"engagement_time_msec",Qf:"enhanced_client_id",Rf:"enhanced_conversions",
ej:"enhanced_conversions_automatic_settings",Sf:"estimated_delivery_date",Xg:"euid_logged_in_state",oe:"event_callback",Il:"event_category",zb:"event_developer_id_string",Jl:"event_label",Kc:"event",Tf:"event_settings",Uf:"event_timeout",Kl:"description",Ll:"fatal",Ml:"experiments",Yg:"firebase_id",vd:"first_party_collection",Vf:"_x_20",Ub:"_x_19",fj:"fledge_drop_reason",gj:"fledge",ij:"flight_error_code",jj:"flight_error_message",kj:"fl_activity_category",lj:"fl_activity_group",Zg:"fl_advertiser_id",
mj:"fl_ar_dedupe",pe:"match_id",nj:"fl_random_number",oj:"tran",pj:"u",Wf:"gac_gclid",wd:"gac_wbraid",qj:"gac_wbraid_multiple_conversions",rj:"ga_restrict_domain",sj:"ga_temp_client_id",Nl:"ga_temp_ecid",Lc:"gdpr_applies",tj:"geo_granularity",qc:"value_callback",Vb:"value_key",Mc:"google_analysis_params",xd:"_google_ng",yd:"google_signals",uj:"google_tld",qe:"gpp_sid",se:"gpp_string",Xf:"groups",vj:"gsa_experiment_id",te:"gtag_event_feature_usage",wj:"gtm_up",rc:"iframe_state",ue:"ignore_referrer",
ah:"internal_traffic_results",sc:"is_legacy_converted",uc:"is_legacy_loaded",Yf:"is_passthrough",Nc:"_lps",jb:"language",Zf:"legacy_developer_id_string",Ha:"linker",zd:"accept_incoming",Wb:"decorate_forms",da:"domains",vc:"url_position",cg:"merchant_feed_label",dg:"merchant_feed_language",eg:"merchant_id",xj:"method",Ol:"name",yj:"navigation_type",ve:"new_customer",fg:"non_interaction",Pl:"optimize_id",zj:"page_hostname",we:"page_path",La:"page_referrer",nb:"page_title",Aj:"passengers",Bj:"phone_conversion_callback",
Ql:"phone_conversion_country_code",Cj:"phone_conversion_css_class",Rl:"phone_conversion_ids",Dj:"phone_conversion_number",Ej:"phone_conversion_options",Sl:"_platinum_request_status",bh:"_protected_audience_enabled",xe:"quantity",gg:"redact_device_info",eh:"referral_exclusion_definition",yo:"_request_start_time",Bb:"restricted_data_processing",Tl:"retoken",Ul:"sample_rate",fh:"screen_name",wc:"screen_resolution",Fj:"_script_source",Vl:"search_term",Za:"send_page_view",Oc:"send_to",Pc:"server_container_url",
ye:"session_duration",hg:"session_engaged",gh:"session_engaged_time",Xb:"session_id",ig:"session_number",ze:"_shared_user_id",Ae:"delivery_postal_code",zo:"_tag_firing_delay",Ao:"_tag_firing_time",Bo:"temporary_client_id",hh:"_timezone",ih:"topmost_url",Wl:"tracking_id",jh:"traffic_type",Ma:"transaction_id",Yb:"transport_url",Gj:"trip_type",Rc:"update",ob:"url_passthrough",Hj:"uptgs",Be:"_user_agent_architecture",Ce:"_user_agent_bitness",De:"_user_agent_full_version_list",Ee:"_user_agent_mobile",
Fe:"_user_agent_model",Ge:"_user_agent_platform",He:"_user_agent_platform_version",Ie:"_user_agent_wow64",Na:"user_data",kh:"user_data_auto_latency",lh:"user_data_auto_meta",mh:"user_data_auto_multi",nh:"user_data_auto_selectors",oh:"user_data_auto_status",Cb:"user_data_mode",jg:"user_data_settings",Ia:"user_id",Db:"user_properties",Ij:"_user_region",Je:"us_privacy_string",za:"value",Jj:"wbraid_multiple_conversions",Bd:"_fpm_parameters",qh:"_host_name",Qj:"_in_page_command",Rj:"_ip_override",Sj:"_is_passthrough_cid",
Zb:"non_personalized_ads",Qe:"_sst_parameters",Tb:"conversion_label",sa:"page_location",Ab:"global_developer_id_string",Qc:"tc_privacy_string"}};var Rh={},Sh=Object.freeze((Rh[N.m.ya]=1,Rh[N.m.Hf]=1,Rh[N.m.If]=1,Rh[N.m.mb]=1,Rh[N.m.ma]=1,Rh[N.m.Xa]=1,Rh[N.m.Ya]=1,Rh[N.m.ib]=1,Rh[N.m.Hc]=1,Rh[N.m.yb]=1,Rh[N.m.Ta]=1,Rh[N.m.oc]=1,Rh[N.m.ke]=1,Rh[N.m.qa]=1,Rh[N.m.bj]=1,Rh[N.m.oe]=1,Rh[N.m.Tf]=1,Rh[N.m.Uf]=1,Rh[N.m.vd]=1,Rh[N.m.rj]=1,Rh[N.m.Mc]=1,Rh[N.m.yd]=1,Rh[N.m.uj]=1,Rh[N.m.Xf]=1,Rh[N.m.ah]=1,Rh[N.m.sc]=1,Rh[N.m.uc]=1,Rh[N.m.Ha]=1,Rh[N.m.eh]=1,Rh[N.m.Bb]=1,Rh[N.m.Za]=1,Rh[N.m.Oc]=1,Rh[N.m.Pc]=1,Rh[N.m.ye]=1,Rh[N.m.gh]=1,Rh[N.m.Ae]=1,Rh[N.m.Yb]=
1,Rh[N.m.Rc]=1,Rh[N.m.jg]=1,Rh[N.m.Db]=1,Rh[N.m.Qe]=1,Rh));Object.freeze([N.m.sa,N.m.La,N.m.nb,N.m.jb,N.m.fh,N.m.Ia,N.m.Yg,N.m.Dl]);
var Th={},Uh=Object.freeze((Th[N.m.bl]=1,Th[N.m.fl]=1,Th[N.m.il]=1,Th[N.m.jl]=1,Th[N.m.kl]=1,Th[N.m.ol]=1,Th[N.m.pl]=1,Th[N.m.ql]=1,Th[N.m.sl]=1,Th[N.m.nd]=1,Th)),Vh={},Wh=Object.freeze((Vh[N.m.Si]=1,Vh[N.m.Ti]=1,Vh[N.m.jd]=1,Vh[N.m.kd]=1,Vh[N.m.Ui]=1,Vh[N.m.Cc]=1,Vh[N.m.ld]=1,Vh[N.m.Pb]=1,Vh[N.m.nc]=1,Vh[N.m.Qb]=1,Vh[N.m.Va]=1,Vh[N.m.md]=1,Vh[N.m.hb]=1,Vh[N.m.Vi]=1,Vh)),Xh=Object.freeze([N.m.ya,N.m.Zd,N.m.mb,N.m.oc,N.m.vd,N.m.ue,N.m.Za,N.m.Rc]),Yh=Object.freeze([].concat(sa(Xh))),Zh=Object.freeze([N.m.Ya,
N.m.Uf,N.m.ye,N.m.gh,N.m.Pf]),$h=Object.freeze([].concat(sa(Zh))),ai={},bi=(ai[N.m.T]="1",ai[N.m.Z]="2",ai[N.m.U]="3",ai[N.m.Da]="4",ai),ci={},di=Object.freeze((ci.search="s",ci.youtube="y",ci.playstore="p",ci.shopping="h",ci.ads="a",ci.maps="m",ci));Object.freeze(N.m);var ei={},fi=(ei[N.m.mc]="gcu",ei[N.m.Rb]="gclgb",ei[N.m.Wa]="gclaw",ei[N.m.Wi]="gclid_len",ei[N.m.od]="gclgs",ei[N.m.pd]="gcllp",ei[N.m.rd]="gclst",ei[N.m.Sb]="auid",ei[N.m.Jf]="dscnt",ei[N.m.Kf]="fcntr",ei[N.m.Lf]="flng",ei[N.m.Mf]="mid",ei[N.m.Yi]="bttype",ei[N.m.wb]="gacid",ei[N.m.Tb]="label",ei[N.m.Gc]="capi",ei[N.m.Nf]="pscdl",ei[N.m.Ka]="currency_code",ei[N.m.Qg]="clobs",ei[N.m.je]="vdltv",ei[N.m.Rg]="clolo",ei[N.m.Sg]="clolb",ei[N.m.aj]="_dbg",ei[N.m.Sf]="oedeld",ei[N.m.zb]="edid",ei[N.m.fj]=
"fdr",ei[N.m.gj]="fledge",ei[N.m.Wf]="gac",ei[N.m.wd]="gacgb",ei[N.m.qj]="gacmcov",ei[N.m.Lc]="gdpr",ei[N.m.Ab]="gdid",ei[N.m.xd]="_ng",ei[N.m.qe]="gpp_sid",ei[N.m.se]="gpp",ei[N.m.vj]="gsaexp",ei[N.m.te]="_tu",ei[N.m.rc]="frm",ei[N.m.Yf]="gtm_up",ei[N.m.Nc]="lps",ei[N.m.Zf]="did",ei[N.m.cg]="fcntr",ei[N.m.dg]="flng",ei[N.m.eg]="mid",ei[N.m.ve]=void 0,ei[N.m.nb]="tiba",ei[N.m.Bb]="rdp",ei[N.m.Xb]="ecsid",ei[N.m.ze]="ga_uid",ei[N.m.Ae]="delopc",ei[N.m.Qc]="gdpr_consent",ei[N.m.Ma]="oid",ei[N.m.Hj]=
"uptgs",ei[N.m.Be]="uaa",ei[N.m.Ce]="uab",ei[N.m.De]="uafvl",ei[N.m.Ee]="uamb",ei[N.m.Fe]="uam",ei[N.m.Ge]="uap",ei[N.m.He]="uapv",ei[N.m.Ie]="uaw",ei[N.m.kh]="ec_lat",ei[N.m.lh]="ec_meta",ei[N.m.mh]="ec_m",ei[N.m.nh]="ec_sel",ei[N.m.oh]="ec_s",ei[N.m.Cb]="ec_mode",ei[N.m.Ia]="userId",ei[N.m.Je]="us_privacy",ei[N.m.za]="value",ei[N.m.Jj]="mcov",ei[N.m.qh]="hn",ei[N.m.Qj]="gtm_ee",ei[N.m.Zb]="npa",ei[N.m.ie]=null,ei[N.m.wc]=null,ei[N.m.jb]=null,ei[N.m.ma]=null,ei[N.m.sa]=null,ei[N.m.La]=null,ei[N.m.ih]=
null,ei[N.m.Bd]=null,ei[N.m.Ud]=null,ei[N.m.Vd]=null,ei[N.m.Mc]=null,ei);function gi(a,b){if(a){var c=a.split("x");c.length===2&&(hi(b,"u_w",c[0]),hi(b,"u_h",c[1]))}}
function ii(a){var b=ji;b=b===void 0?ki:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(li(q.value)),r.push(li(q.quantity)),r.push(li(q.item_id)),r.push(li(q.start_date)),r.push(li(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ki(a){return mi(a.item_id,a.id,a.item_name)}function mi(){for(var a=l(wa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ni(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function hi(a,b,c){c===void 0||c===null||c===""&&!qg[b]||(a[b]=c)}function li(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};function qi(a){return ri?A.querySelectorAll(a):null}
function si(a,b){if(!ri)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ti=!1;
if(A.querySelectorAll)try{var ui=A.querySelectorAll(":root");ui&&ui.length==1&&ui[0]==A.documentElement&&(ti=!0)}catch(a){}var ri=ti;function vi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var wi=/^[0-9A-Fa-f]{64}$/;function yi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function zi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=z.crypto)==null?0:b.subtle){if(wi.test(a))return Promise.resolve(a);try{var c=yi(a);return z.crypto.subtle.digest("SHA-256",c).then(function(d){var e=Array.from(new Uint8Array(d)).map(function(f){return String.fromCharCode(f)}).join("");return z.btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")};var Ai={Yk:'100',Zk:'100',al:'1000',rm:'101509156~103116025~103200001~103233424~103251618~103251620'},Bi={Rh:Number(Ai.Yk)||0,Ye:Number(Ai.Zk)||0,Om:Number(Ai.al)||0,so:Ai.rm};function O(a){Wa("GTM",a)};var hj={},ij=(hj[N.m.Ja]=1,hj[N.m.Pc]=2,hj[N.m.Yb]=2,hj[N.m.oa]=3,hj[N.m.je]=4,hj[N.m.Gf]=5,hj[N.m.oc]=6,hj[N.m.Ta]=6,hj[N.m.Xa]=6,hj[N.m.Hc]=6,hj[N.m.yb]=6,hj[N.m.ib]=6,hj[N.m.Ya]=7,hj[N.m.Bb]=9,hj[N.m.Hf]=10,hj[N.m.mb]=11,hj),jj={},kj=(jj.unknown=13,jj.standard=14,jj.unique=15,jj.per_session=16,jj.transactions=17,jj.items_sold=18,jj);var lj=[];function mj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(ij)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=ij[f],h=b;h=h===void 0?!1:h;Wa("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(lj[g]=!0)}}};var nj=function(){this.C=new Set},pj=function(a){var b=oj.Ea;a=a===void 0?[]:a;return Array.from(b.C).concat(a)},qj=function(){var a=oj.Ea,b=Bi.so;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var rj={xh:"54u1"};rj.wh=Number("0")||0;rj.vb="dataLayer";rj.vo="ChAI8NrRwAYQ4rXJnfiI1Y0XEiUAa4w+iR1IFSwJXkKVyCh+tRLuFqeEaYS61UTHzy9hJZwc856wGgLSxw\x3d\x3d";var sj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},tj={__paused:1,__tg:1},uj;for(uj in sj)sj.hasOwnProperty(uj)&&(tj[uj]=1);var vj=lb(""),wj=!1,xj,yj=!1;xj=yj;var zj,Aj=!1;zj=Aj;var Bj,Cj=!1;Bj=Cj;rj.Ff="www.googletagmanager.com";var Dj=""+rj.Ff+(xj?"/gtag/js":"/gtm.js"),Ej=null,Fj=null,Gj={},Hj={};rj.Xk="";var Ij="";rj.yh=Ij;
var oj=new function(){this.Ea=new nj;this.C=!1;this.H=0;this.fa=this.ia=this.ab=this.O="";this.R=this.N=!1};function Jj(){var a;a=a===void 0?[]:a;return pj(a).join("~")}function Kj(){var a=oj.O.length;return oj.O[a-1]==="/"?oj.O.substring(0,a-1):oj.O}function Lj(){return oj.C?H(83)?oj.H===0:oj.H!==1:!1}function Mj(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var Nj=new hb,Oj={},Pj={},Sj={name:rj.vb,set:function(a,b){Xc(wb(a,b),Oj);Qj()},get:function(a){return Rj(a,2)},reset:function(){Nj=new hb;Oj={};Qj()}};function Rj(a,b){return b!=2?Nj.get(a):Tj(a)}function Tj(a,b){var c=a.split(".");b=b||[];for(var d=Oj,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Uj(a,b){Pj.hasOwnProperty(a)||(Nj.set(a,b),Xc(wb(a,b),Oj),Qj())}
function Vj(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Rj(c,1);if(Array.isArray(d)||Wc(d))d=Xc(d,null);Pj[c]=d}}function Qj(a){ib(Pj,function(b,c){Nj.set(b,c);Xc(wb(b),Oj);Xc(wb(b,c),Oj);a&&delete Pj[b]})}function Wj(a,b){var c,d=(b===void 0?2:b)!==1?Tj(a):Nj.get(a);Uc(d)==="array"||Uc(d)==="object"?c=Xc(d,null):c=d;return c};var bk=/:[0-9]+$/,ck=/^\d+\.fls\.doubleclick\.net$/;function dk(a,b,c,d){for(var e=[],f=l(a.split("&")),g=f.next();!g.done;g=f.next()){var h=l(g.value.split("=")),m=h.next().value,n=ra(h);if(decodeURIComponent(m.replace(/\+/g," "))===b){var p=n.join("=");if(!c)return d?p:decodeURIComponent(p.replace(/\+/g," "));e.push(d?p:decodeURIComponent(p.replace(/\+/g," ")))}}return c?e:void 0}function ek(a){try{return decodeURIComponent(a)}catch(b){}}
function fk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=gk(a.protocol)||gk(z.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:z.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||z.location.hostname).replace(bk,"").toLowerCase());return hk(a,b,c,d,e)}
function hk(a,b,c,d,e){var f,g=gk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=ik(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(bk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Wa("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=dk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function gk(a){return a?a.replace(":","").toLowerCase():""}function ik(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var jk={},kk=0;
function lk(a){var b=jk[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Wa("TAGGING",1),d="/"+d);var e=c.hostname.replace(bk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};kk<5&&(jk[a]=b,kk++)}return b}function mk(a,b,c){var d=lk(a);return Bb(b,d,c)}
function nk(a){var b=lk(z.location.href),c=fk(b,"host",!1);if(c&&c.match(ck)){var d=fk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var ok={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},pk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function qk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return lk(""+c+b).href}}function rk(a,b){if(Lj()||zj)return qk(a,b)}
function sk(){return!!rj.yh&&rj.yh.split("@@").join("")!=="SGTM_TOKEN"}function tk(a){for(var b=l([N.m.Pc,N.m.Yb]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function uk(a,b,c){c=c===void 0?"":c;if(!Lj())return a;var d=b?ok[a]||"":"";d==="/gs"&&(c="");return""+Kj()+d+c}function vk(a){if(!Lj())return a;for(var b=l(pk),c=b.next();!c.done;c=b.next())if(ub(a,""+Kj()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function wk(a){var b=String(a[Pe.Aa]||"").replace(/_/g,"");return ub(b,"cvt")?"cvt":b}var xk=z.location.search.indexOf("?gtm_latency=")>=0||z.location.search.indexOf("&gtm_latency=")>=0;var yk={sampleRate:"0.005000",Tk:"",ro:"0.01"};function zk(){var a=yk.sampleRate;return Number(a)}var Ak=Math.random(),Bk=xk||Ak<zk(),Ck=zk()===1||(ic==null?void 0:ic.includes("gtm_debug=d"))||xk||Ak>=1-Number(yk.ro);var Dk,Ek;a:{for(var Fk=["CLOSURE_FLAGS"],Gk=xa,Hk=0;Hk<Fk.length;Hk++)if(Gk=Gk[Fk[Hk]],Gk==null){Ek=null;break a}Ek=Gk}var Ik=Ek&&Ek[610401301];Dk=Ik!=null?Ik:!1;function Jk(){var a=xa.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var Kk,Lk=xa.navigator;Kk=Lk?Lk.userAgentData||null:null;function Mk(a){if(!Dk||!Kk)return!1;for(var b=0;b<Kk.brands.length;b++){var c=Kk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Nk(a){return Jk().indexOf(a)!=-1};function Ok(){return Dk?!!Kk&&Kk.brands.length>0:!1}function Pk(){return Ok()?!1:Nk("Opera")}function Qk(){return Nk("Firefox")||Nk("FxiOS")}function Rk(){return Ok()?Mk("Chromium"):(Nk("Chrome")||Nk("CriOS"))&&!(Ok()?0:Nk("Edge"))||Nk("Silk")};function Sk(){return Dk?!!Kk&&!!Kk.platform:!1}function Tk(){return Nk("iPhone")&&!Nk("iPod")&&!Nk("iPad")}function Uk(){Tk()||Nk("iPad")||Nk("iPod")};var Vk=function(a){Vk[" "](a);return a};Vk[" "]=function(){};Pk();Ok()||Nk("Trident")||Nk("MSIE");Nk("Edge");!Nk("Gecko")||Jk().toLowerCase().indexOf("webkit")!=-1&&!Nk("Edge")||Nk("Trident")||Nk("MSIE")||Nk("Edge");Jk().toLowerCase().indexOf("webkit")!=-1&&!Nk("Edge")&&Nk("Mobile");Sk()||Nk("Macintosh");Sk()||Nk("Windows");(Sk()?Kk.platform==="Linux":Nk("Linux"))||Sk()||Nk("CrOS");Sk()||Nk("Android");Tk();Nk("iPad");Nk("iPod");Uk();Jk().toLowerCase().indexOf("kaios");var Wk=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Xk=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var Yk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var Zk=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},$k=/#|$/,al=function(a,b){var c=a.search($k),d=Zk(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Yk(a.slice(d,e!==-1?e:0))},bl=/[?&]($|#)/,cl=function(a,b,c){for(var d,e=a.search($k),f=0,g,h=[];(g=Zk(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(bl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};var dl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Vk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},el=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},fl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},gl=function(a){if(z.top==z)return 0;if(a===void 0?0:a){var b=z.location.ancestorOrigins;
if(b)return b[b.length-1]==z.location.origin?1:2}return dl(z.top)?1:2},hl=function(a){a=a===void 0?document:a;return a.createElement("img")},il=function(){for(var a=z,b=a;a&&a!=a.parent;)a=a.parent,dl(a)&&(b=a);return b};function jl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function kl(){return jl("join-ad-interest-group")&&$a(fc.joinAdInterestGroup)}
function ll(a,b,c){var d=ig[3]===void 0?1:ig[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(ig[2]===void 0?50:ig[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&pb()-q<(ig[1]===void 0?6E4:ig[1])?(Wa("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)ml(f[0]);else{if(n)return Wa("TAGGING",10),!1}else f.length>=d?ml(f[0]):n&&ml(m[0]);tc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:pb()});return!0}function ml(a){try{a.parentNode.removeChild(a)}catch(b){}}function nl(){return"https://td.doubleclick.net"};function ol(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var pl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Qk();Tk()||Nk("iPod");Nk("iPad");!Nk("Android")||Rk()||Qk()||Pk()||Nk("Silk");Rk();!Nk("Safari")||Rk()||(Ok()?0:Nk("Coast"))||Pk()||(Ok()?0:Nk("Edge"))||(Ok()?Mk("Microsoft Edge"):Nk("Edg/"))||(Ok()?Mk("Opera"):Nk("OPR"))||Qk()||Nk("Silk")||Nk("Android")||Uk();var ql={},rl=null,sl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!rl){rl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));ql[m]=n;for(var p=0;p<n.length;p++){var q=n[p];rl[q]===void 0&&(rl[q]=p)}}}for(var r=ql[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
y=b[v+1],B=b[v+2],C=r[x>>2],E=r[(x&3)<<4|y>>4],F=r[(y&15)<<2|B>>6],I=r[B&63];t[w++]=""+C+E+F+I}var L=0,U=u;switch(b.length-v){case 2:L=b[v+1],U=r[(L&15)<<2]||u;case 1:var K=b[v];t[w]=""+r[K>>2]+r[(K&3)<<4|L>>4]+U+u}return t.join("")};function tl(a,b,c,d,e,f){var g=al(c,"fmt");if(d){var h=al(c,"random"),m=al(c,"label")||"";if(!h)return!1;var n=sl(Yk(m)+":"+Yk(h));if(!ol(a,n,d))return!1}g&&Number(g)!==4&&(c=cl(c,"rfmt",g));var p=cl(c,"fmt",4);rc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var ul={},vl=(ul[1]={},ul[2]={},ul[3]={},ul[4]={},ul);function wl(a,b,c){var d=xl(b,c);if(d){var e=vl[b][d];e||(e=vl[b][d]=[]);e.push(Object.assign({},a))}}function yl(a,b){var c=xl(a,b);if(c){var d=vl[a][c];d&&(vl[a][c]=d.filter(function(e){return!e.Gk}))}}function zl(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function xl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=z.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Al(a){var b=wa.apply(1,arguments);H(54)&&Ck&&(wl(a,2,b[0]),wl(a,3,b[0]));Cc.apply(null,sa(b))}function Bl(a){var b=wa.apply(1,arguments);H(54)&&Ck&&wl(a,2,b[0]);return Dc.apply(null,sa(b))}function Cl(a){var b=wa.apply(1,arguments);H(54)&&Ck&&wl(a,3,b[0]);uc.apply(null,sa(b))}
function Dl(a){var b=wa.apply(1,arguments),c=b[0];H(54)&&Ck&&(wl(a,2,c),wl(a,3,c));return Fc.apply(null,sa(b))}function El(a){var b=wa.apply(1,arguments);H(54)&&Ck&&wl(a,1,b[0]);rc.apply(null,sa(b))}function Fl(a){var b=wa.apply(1,arguments);b[0]&&H(54)&&Ck&&wl(a,4,b[0]);tc.apply(null,sa(b))}function Gl(a){var b=wa.apply(1,arguments);H(54)&&Ck&&wl(a,1,b[2]);return tl.apply(null,sa(b))}function Hl(a){var b=wa.apply(1,arguments);H(54)&&Ck&&wl(a,4,b[0]);ll.apply(null,sa(b))};var Il=/gtag[.\/]js/,Jl=/gtm[.\/]js/,Kl=!1;function Ll(a){if(Kl)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(Il.test(c))return"3";if(Jl.test(c))return"2"}return"0"};function Ml(a,b){var c=Nl();c.pending||(c.pending=[]);eb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Ol(){var a=z.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Pl=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Ol()};
function Nl(){var a=jc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Pl,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Ol());return c};var Ql={},Rl=!1,Sl=void 0,Wf={ctid:"GTM-N2764VHX",canonicalContainerId:"184799763",Ak:"GTM-N2764VHX",Bk:"GTM-N2764VHX"};Ql.Le=lb("");function Tl(){return Ql.Le&&Ul().some(function(a){return a===Wf.ctid})}function Vl(){var a=Wl();return Rl?a.map(Xl):a}function Yl(){var a=Ul();return Rl?a.map(Xl):a}
function Zl(){var a=Yl();if(!Rl)for(var b=l([].concat(sa(a))),c=b.next();!c.done;c=b.next()){var d=Xl(c.value),e=Nl().destination[d];e&&e.state!==0||a.push(d)}return a}function $l(){return am(Wf.ctid)}function bm(){return am(Wf.canonicalContainerId||"_"+Wf.ctid)}function Wl(){return Wf.Ak?Wf.Ak.split("|"):[Wf.ctid]}function Ul(){return Wf.Bk?Wf.Bk.split("|").filter(function(a){return H(107)?a.indexOf("GTM-")!==0:!0}):[]}function cm(){var a=dm(em()),b=a&&a.parent;if(b)return dm(b)}
function dm(a){var b=Nl();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function am(a){return Rl?Xl(a):a}function Xl(a){return"siloed_"+a}function fm(a){a=String(a);return ub(a,"siloed_")?a.substring(7):a}function gm(){if(oj.N){var a=Nl();if(a.siloed){for(var b=[],c=Wl().map(Xl),d=Ul().map(Xl),e={},f=0;f<a.siloed.length;e={sg:void 0},f++)e.sg=a.siloed[f],!Rl&&eb(e.sg.isDestination?d:c,function(g){return function(h){return h===g.sg.ctid}}(e))?Rl=!0:b.push(e.sg);a.siloed=b}}}
function hm(){var a=Nl();if(a.pending){for(var b,c=[],d=!1,e=Vl(),f=Sl?Sl:Zl(),g={},h=0;h<a.pending.length;g={uf:void 0},h++)g.uf=a.pending[h],eb(g.uf.target.isDestination?f:e,function(m){return function(n){return n===m.uf.target.ctid}}(g))?d||(b=g.uf.onLoad,d=!0):c.push(g.uf);a.pending=c;if(b)try{b(bm())}catch(m){}}}
function im(){var a=Wf.ctid,b=Vl(),c=Zl();Sl=c;for(var d=function(n,p){var q={canonicalContainerId:Wf.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};hc&&(q.scriptElement=hc);ic&&(q.scriptSource=ic);if(cm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=oj.C,x=lk(v),y=w?x.pathname:""+x.hostname+x.pathname,B=A.scripts,C="",E=0;E<B.length;++E){var F=B[E];if(!(F.innerHTML.length===
0||!w&&F.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||F.innerHTML.indexOf(y)<0)){if(F.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(E);break b}C=String(E)}}if(C){t=C;break b}}t=void 0}var I=t;if(I){Kl=!0;r=I;break a}}var L=[].slice.call(A.scripts);r=q.scriptElement?String(L.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Ll(q)}var U=p?e.destination:e.container,K=U[n];K?(p&&K.state===0&&O(93),Object.assign(K,q)):U[n]=q},e=Nl(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[bm()]={};hm()}function jm(){var a=bm();return!!Nl().canonical[a]}function km(a){return!!Nl().container[a]}function lm(a){var b=Nl().destination[a];return!!b&&!!b.state}function em(){return{ctid:$l(),isDestination:Ql.Le}}function mm(a,b,c){b.siloed&&nm({ctid:a,isDestination:!1});var d=em();Nl().container[a]={state:1,context:b,parent:d};Ml({ctid:a,isDestination:!1},c)}
function nm(a){var b=Nl();(b.siloed=b.siloed||[]).push(a)}function om(){var a=Nl().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function pm(){var a={};ib(Nl().destination,function(b,c){c.state===0&&(a[fm(b)]=c)});return a}function qm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function rm(){for(var a=Nl(),b=l(Vl()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function sm(a){var b=Nl();return b.destination[a]?1:b.destination[Xl(a)]?2:0};var tm={Ba:{Cd:0,Dd:1,uh:2}};tm.Ba[tm.Ba.Cd]="FULL_TRANSMISSION";tm.Ba[tm.Ba.Dd]="LIMITED_TRANSMISSION";tm.Ba[tm.Ba.uh]="NO_TRANSMISSION";var um={V:{pb:0,xa:1,kc:2,xc:3}};um.V[um.V.pb]="NO_QUEUE";um.V[um.V.xa]="ADS";um.V[um.V.kc]="ANALYTICS";um.V[um.V.xc]="MONITORING";function vm(){var a=jc("google_tag_data",{});return a.ics=a.ics||new wm}var wm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
wm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Wa("TAGGING",19);b==null?Wa("TAGGING",18):xm(this,a,b==="granted",c,d,e,f,g)};wm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)xm(this,a[d],void 0,void 0,"","",b,c)};
var xm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&ab(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&z.setTimeout(function(){m[b]===t&&t.quiet&&(Wa("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=wm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())ym(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())ym(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&ab(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Id:b})};var ym=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Ck=!0)}};wm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Ck){d.Ck=!1;try{d.Id({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var zm=!1,Am=!1,Bm={},Cm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Bm.ad_storage=1,Bm.analytics_storage=1,Bm.ad_user_data=1,Bm.ad_personalization=1,Bm),usedContainerScopedDefaults:!1};function Dm(a){var b=vm();b.accessedAny=!0;return(ab(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Cm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Em(a){var b=vm();b.accessedAny=!0;return b.getConsentState(a,Cm)}function Fm(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Cm.corePlatformServices[e]!==!1}return b}function Gm(a){var b=vm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}
function Hm(){if(!jg(8))return!1;var a=vm();a.accessedAny=!0;if(a.active)return!0;if(!Cm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Cm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Cm.containerScopedDefaults[c.value]!==1)return!0;return!1}function Im(a,b){vm().addListener(a,b)}function Jm(a,b){vm().notifyListeners(a,b)}
function Km(a,b){function c(){for(var e=0;e<b.length;e++)if(!Gm(b[e]))return!0;return!1}if(c()){var d=!1;Im(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function Lm(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Dm(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=ab(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Im(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):z.setTimeout(function(){m(c())},500)}}))};var Mm={},Nm=(Mm[um.V.pb]=tm.Ba.Cd,Mm[um.V.xa]=tm.Ba.Cd,Mm[um.V.kc]=tm.Ba.Cd,Mm[um.V.xc]=tm.Ba.Cd,Mm),Om=function(a,b){this.C=a;this.consentTypes=b};Om.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Dm(a)});case 1:return this.consentTypes.some(function(a){return Dm(a)});default:Yb(this.C,"consentsRequired had an unknown type")}};
var Pm={},Qm=(Pm[um.V.pb]=new Om(0,[]),Pm[um.V.xa]=new Om(0,["ad_storage"]),Pm[um.V.kc]=new Om(0,["analytics_storage"]),Pm[um.V.xc]=new Om(1,["ad_storage","analytics_storage"]),Pm);var Sm=function(a){var b=this;this.type=a;this.C=[];Im(Qm[a].consentTypes,function(){Rm(b)||b.flush()})};Sm.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Rm=function(a){return Nm[a.type]===tm.Ba.uh&&!Qm[a.type].isConsentGranted()},Tm=function(a,b){Rm(a)?a.C.push(b):b()},Um=new Map;function Vm(a){Um.has(a)||Um.set(a,new Sm(a));return Um.get(a)};var Wm="/td?id="+Wf.ctid,Xm="v t pid dl tdp exp".split(" "),Ym=["mcc"],Zm={},$m={},an=!1;function bn(a,b,c){$m[a]=b;(c===void 0||c)&&cn(a)}function cn(a,b){if(Zm[a]===void 0||(b===void 0?0:b))Zm[a]=!0}function dn(a){a=a===void 0?!1:a;var b=Object.keys(Zm).filter(function(c){return Zm[c]===!0&&$m[c]!==void 0&&(a||!Ym.includes(c))}).map(function(c){var d=$m[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+uk("https://www.googletagmanager.com")+Wm+(""+b+"&z=0")}
function en(){Object.keys(Zm).forEach(function(a){Xm.indexOf(a)<0&&(Zm[a]=!1)})}function fn(a){a=a===void 0?!1:a;if(oj.R&&Ck&&Wf.ctid){var b=Vm(um.V.xc);if(Rm(b))an||(an=!0,Tm(b,fn));else{var c=dn(a),d={destinationId:Wf.ctid,endpoint:56};a?Dl(d,c):Cl(d,c);en();an=!1}}}var gn={};function hn(){Object.keys(Zm).filter(function(a){return Zm[a]&&!Xm.includes(a)}).length>0&&fn(!0)}var jn=fb();function kn(){jn=fb()}
function ln(){bn("v","3");bn("t","t");bn("pid",function(){return String(jn)});bn("exp",Jj());wc(z,"pagehide",hn);z.setInterval(kn,864E5)};var mn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],nn=[N.m.Pc,N.m.Yb,N.m.vd,N.m.wb,N.m.Xb,N.m.Ia,N.m.Ha,N.m.Ta,N.m.Xa,N.m.yb],on=!1,pn=!1,qn={},rn={};function sn(){!pn&&on&&(mn.some(function(a){return Cm.containerScopedDefaults[a]!==1})||tn("mbc"));pn=!0}function tn(a){Ck&&(bn(a,"1"),fn())}function un(a,b){if(!qn[b]&&(qn[b]=!0,rn[b]))for(var c=l(nn),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){tn("erc");break}};function vn(a){Wa("HEALTH",a)};var wn={Zj:"service_worker_endpoint",zh:"shared_user_id",Ah:"shared_user_id_requested",Re:"shared_user_id_source",Ef:"cookie_deprecation_label",Uk:"aw_user_data_cache",Yl:"ga4_user_data_cache",Xl:"fl_user_data_cache",Uj:"pt_listener_set",Pe:"pt_data",th:"ip_geo_fetch_in_progress",Ke:"ip_geo_data_cache"},xn;function yn(a){if(!xn){xn={};for(var b=l(Object.keys(wn)),c=b.next();!c.done;c=b.next())xn[wn[c.value]]=!0}return!!xn[a]}
function zn(a,b){b=b===void 0?!1:b;if(yn(a)){var c,d,e=(d=(c=jc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function An(a,b){var c=zn(a,!0);c&&c.set(b)}function Bn(a){var b;return(b=zn(a))==null?void 0:b.get()}function Cn(a,b){if(typeof b==="function"){var c;return(c=zn(a,!0))==null?void 0:c.subscribe(b)}}function Dn(a,b){var c=zn(a);return c?c.unsubscribe(b):!1};var En={bn:"eyIwIjoiVVMiLCIxIjoiVVMtR0EiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9"},Fn={},Gn=!1;function Hn(){function a(){c!==void 0&&Dn(wn.Ke,c);try{var e=Bn(wn.Ke);Fn=JSON.parse(e)}catch(f){O(123),vn(2),Fn={}}Gn=!0;b()}var b=In,c=void 0,d=Bn(wn.Ke);d?a(d):(c=Cn(wn.Ke,a),Jn())}
function Jn(){function a(c){An(wn.Ke,c||"{}");An(wn.th,!1)}if(!Bn(wn.th)){An(wn.th,!0);var b="";try{z.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function Kn(){var a=En.bn;try{return JSON.parse(Ta(a))}catch(b){return O(123),vn(2),{}}}function Ln(){return Fn["0"]||""}function Mn(){return Fn["1"]||""}function Nn(){var a=!1;return a}function On(){return Fn["6"]!==!1}function Pn(){var a="";return a}
function Qn(){var a=!1;return a}function Rn(){var a="";return a};function Sn(a){return typeof a!=="object"||a===null?{}:a}function Tn(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Un(a){if(a!==void 0&&a!==null)return Tn(a)}function Vn(a){return typeof a==="number"?a:Un(a)};function Wn(a){return a&&a.indexOf("pending:")===0?Xn(a.substr(8)):!1}function Xn(a){if(a==null||a.length===0)return!1;var b=Number(a),c=pb();return b<c+3E5&&b>c-9E5};var Yn=!1,Zn=!1,$n=!1,ao=0,bo=!1,co=[];function eo(a){if(ao===0)bo&&co&&(co.length>=100&&co.shift(),co.push(a));else if(fo()){var b=jc('google.tagmanager.ta.prodqueue',[]);b.length>=50&&b.shift();b.push(a)}}function go(){ho();xc(A,"TAProdDebugSignal",go)}function ho(){if(!Zn){Zn=!0;io();var a=co;co=void 0;a==null||a.forEach(function(b){eo(b)})}}
function io(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Xn(a)?ao=1:!Wn(a)||Yn||$n?ao=2:($n=!0,wc(A,"TAProdDebugSignal",go,!1),z.setTimeout(function(){ho();Yn=!0},200))}function fo(){if(!bo)return!1;switch(ao){case 1:case 0:return!0;case 2:return!1;default:return!1}};var jo=!1;function ko(a,b){var c=Wl(),d=Ul();if(fo()){var e=lo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;eo(e)}}function mo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Oa;e=a.isBatched;if(fo()){var f=lo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});f.target=b;f.url=c.url;c.postBody&&(f.postBody=c.postBody);f.parameterEncoding=c.parameterEncoding;f.endpoint=c.endpoint;e!==void 0&&(f.isBatched=e);eo(f)}}
function no(a){fo()&&mo(a())}function lo(a,b){b=b===void 0?{}:b;b.groupId=oo;var c,d=b,e={publicId:po};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'8',messageType:a};c.containerProduct=jo?"OGT":"GTM";c.key.targetRef=qo;return c}var po="",qo={ctid:"",isDestination:!1},oo;
function ro(a){var b=Wf.ctid,c=Tl();ao=0;bo=!0;io();oo=a;po=b;jo=xj;qo={ctid:b,isDestination:c}};var so=[N.m.T,N.m.Z,N.m.U,N.m.Da],to,uo;function vo(a){var b=a[N.m.Nb];b||(b=[""]);for(var c={ff:0};c.ff<b.length;c={ff:c.ff},++c.ff)ib(a,function(d){return function(e,f){if(e!==N.m.Nb){var g=Tn(f),h=b[d.ff],m=Ln(),n=Mn();Am=!0;zm&&Wa("TAGGING",20);vm().declare(e,g,h,m,n)}}}(c))}
function wo(a){sn();!uo&&to&&tn("crc");uo=!0;var b=a[N.m.Cf];b&&O(41);var c=a[N.m.Nb];c?O(40):c=[""];for(var d={hf:0};d.hf<c.length;d={hf:d.hf},++d.hf)ib(a,function(e){return function(f,g){if(f!==N.m.Nb&&f!==N.m.Cf){var h=Un(g),m=c[e.hf],n=Number(b),p=Ln(),q=Mn();n=n===void 0?0:n;zm=!0;Am&&Wa("TAGGING",20);vm().default(f,h,m,p,q,n,Cm)}}}(d))}
function xo(a){Cm.usedContainerScopedDefaults=!0;var b=a[N.m.Nb];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(Mn())&&!c.includes(Ln()))return}ib(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Cm.usedContainerScopedDefaults=!0;Cm.containerScopedDefaults[d]=e==="granted"?3:2})}
function yo(a,b){sn();to=!0;ib(a,function(c,d){var e=Tn(d);zm=!0;Am&&Wa("TAGGING",20);vm().update(c,e,Cm)});Jm(b.eventId,b.priorityId)}function zo(a){a.hasOwnProperty("all")&&(Cm.selectedAllCorePlatformServices=!0,ib(di,function(b){Cm.corePlatformServices[b]=a.all==="granted";Cm.usedCorePlatformServices=!0}));ib(a,function(b,c){b!=="all"&&(Cm.corePlatformServices[b]=c==="granted",Cm.usedCorePlatformServices=!0)})}function Ao(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Dm(b)})}
function Bo(a,b){Im(a,b)}function Co(a,b){Lm(a,b)}function Do(a,b){Km(a,b)}function Eo(){var a=[N.m.T,N.m.Da,N.m.U];vm().waitForUpdate(a,500,Cm)}function Fo(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;vm().clearTimeout(d,void 0,Cm)}Jm()}function Go(){if(!Bj)for(var a=On()?Mj(oj.ia):Mj(oj.ab),b=0;b<so.length;b++){var c=so[b],d=c,e=a[c]?"granted":"denied";vm().implicit(d,e)}};var Ho=!1,Io=[];function Jo(){if(!Ho){Ho=!0;for(var a=Io.length-1;a>=0;a--)Io[a]();Io=[]}};var Ko=z.google_tag_manager=z.google_tag_manager||{};function Lo(a,b){return Ko[a]=Ko[a]||b()}function Mo(){var a=$l(),b=No;Ko[a]=Ko[a]||b}function Oo(){var a=rj.vb;return Ko[a]=Ko[a]||{}}function Po(){var a=Ko.sequence||1;Ko.sequence=a+1;return a};function Qo(){if(Ko.pscdl!==void 0)Bn(wn.Ef)===void 0&&An(wn.Ef,Ko.pscdl);else{var a=function(c){Ko.pscdl=c;An(wn.Ef,c)},b=function(){a("error")};try{fc.cookieDeprecationLabel?(a("pending"),fc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};function Ro(a,b){b&&ib(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var So=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,To=/\s/;
function Uo(a,b){if(ab(a)){a=nb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(So.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||To.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Vo(a,b){for(var c={},d=0;d<a.length;++d){var e=Uo(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Wo[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Xo={},Wo=(Xo[0]=0,Xo[1]=1,Xo[2]=2,Xo[3]=0,Xo[4]=1,Xo[5]=0,Xo[6]=0,Xo[7]=0,Xo);var Yo=Number('')||500,Zo={},$o={},ap={initialized:11,complete:12,interactive:13},ep={},fp=Object.freeze((ep[N.m.Za]=!0,ep)),gp=void 0;function hp(a,b){if(b.length&&Ck){var c;(c=Zo)[a]!=null||(c[a]=[]);$o[a]!=null||($o[a]=[]);var d=b.filter(function(e){return!$o[a].includes(e)});Zo[a].push.apply(Zo[a],sa(d));$o[a].push.apply($o[a],sa(d));!gp&&d.length>0&&(cn("tdc",!0),gp=z.setTimeout(function(){fn();Zo={};gp=void 0},Yo))}}
function ip(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function jp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;Uc(t)==="object"?u=t[r]:Uc(t)==="array"&&(u=t[r]);return u===void 0?fp[r]:u},f=ip(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=Uc(m)==="object"||Uc(m)==="array",q=Uc(n)==="object"||Uc(n)==="array";if(p&&q)jp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function kp(){bn("tdc",function(){gp&&(z.clearTimeout(gp),gp=void 0);var a=[],b;for(b in Zo)Zo.hasOwnProperty(b)&&a.push(b+"*"+Zo[b].join("."));return a.length?a.join("!"):void 0},!1)};var lp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.O=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},mp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.O);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.O);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.O)}return c},P=function(a,b,c,d){for(var e=l(mp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},np=function(a){for(var b={},c=mp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)},op=function(a,b,c){function d(n){Wc(n)&&ib(n,function(p,q){f=!0;e[p]=q})}var e={},f=!1,g=mp(a,c===void 0?3:c);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[b]);return f?e:void 0},pp=function(a){for(var b=[N.m.ee,N.m.ae,
N.m.be,N.m.ce,N.m.de,N.m.fe,N.m.he],c=mp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},qp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.fa={};this.O={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},rp=function(a,b){a.H=b;return a},sp=function(a,b){a.R=b;
return a},tp=function(a,b){a.C=b;return a},up=function(a,b){a.N=b;return a},vp=function(a,b){a.fa=b;return a},wp=function(a,b){a.O=b;return a},xp=function(a,b){a.eventMetadata=b||{};return a},yp=function(a,b){a.onSuccess=b;return a},zp=function(a,b){a.onFailure=b;return a},Ap=function(a,b){a.isGtmEvent=b;return a},Bp=function(a){return new lp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.O,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Cp={Sk:Number("5"),ap:Number("")},Dp=[],Ep=!1;function Fp(a){Dp.push(a)}var Gp="?id="+Wf.ctid,Hp=void 0,Ip={},Jp=void 0,Kp=new function(){var a=5;Cp.Sk>0&&(a=Cp.Sk);this.H=a;this.C=0;this.N=[]},Lp=1E3;
function Mp(a,b){var c=Hp;if(c===void 0)if(b)c=Po();else return"";for(var d=[uk("https://www.googletagmanager.com"),"/a",Gp],e=l(Dp),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,hd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Np(){if(oj.R&&(Jp&&(z.clearTimeout(Jp),Jp=void 0),Hp!==void 0&&Op)){var a=Vm(um.V.xc);if(Rm(a))Ep||(Ep=!0,Tm(a,Np));else{var b;if(!(b=Ip[Hp])){var c=Kp;b=c.C<c.H?!1:pb()-c.N[c.C%c.H]<1E3}if(b||Lp--<=0)O(1),Ip[Hp]=!0;else{var d=Kp,e=d.C++%d.H;d.N[e]=pb();var f=Mp(!0);Cl({destinationId:Wf.ctid,endpoint:56,eventId:Hp},f);Ep=Op=!1}}}}function Pp(){if(Bk&&oj.R){var a=Mp(!0,!0);Cl({destinationId:Wf.ctid,endpoint:56,eventId:Hp},a)}}var Op=!1;
function Qp(a){Ip[a]||(a!==Hp&&(Np(),Hp=a),Op=!0,Jp||(Jp=z.setTimeout(Np,500)),Mp().length>=2022&&Np())}var Rp=fb();function Sp(){Rp=fb()}function Tp(){return[["v","3"],["t","t"],["pid",String(Rp)]]};var Up={};function Vp(a,b,c){Bk&&a!==void 0&&(Up[a]=Up[a]||[],Up[a].push(c+b),Qp(a))}function Wp(a){var b=a.eventId,c=a.hd,d=[],e=Up[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Up[b];return d};function Xp(a,b,c){var d=Uo(am(a),!0);d&&Yp.register(d,b,c)}function Zp(a,b,c,d){var e=Uo(c,d.isGtmEvent);e&&(wj&&(d.deferrable=!0),Yp.push("event",[b,a],e,d))}function $p(a,b,c,d){var e=Uo(c,d.isGtmEvent);e&&Yp.push("get",[a,b],e,d)}function aq(a){var b=Uo(am(a),!0),c;b?c=bq(Yp,b).C:c={};return c}function cq(a,b){var c=Uo(am(a),!0);if(c){var d=Yp,e=Xc(b,null);Xc(bq(d,c).C,e);bq(d,c).C=e}}
var dq=function(){this.R={};this.C={};this.H={};this.fa=null;this.O={};this.N=!1;this.status=1},eq=function(a,b,c,d){this.H=pb();this.C=b;this.args=c;this.messageContext=d;this.type=a},fq=function(){this.destinations={};this.C={};this.commands=[]},bq=function(a,b){var c=b.destinationId;Rl||(c=fm(c));return a.destinations[c]=a.destinations[c]||new dq},gq=function(a,b,c,d){if(d.C){var e=bq(a,d.C),f=e.fa;if(f){var g=d.C.id;Rl||(g=fm(g));var h=Xc(c,null),m=Xc(e.R[g],null),n=Xc(e.O,null),p=Xc(e.C,null),
q=Xc(a.C,null),r={};if(Bk)try{r=Xc(Oj,null)}catch(x){O(72)}var t=d.C.prefix,u=function(x){Vp(d.messageContext.eventId,t,x)},v=Bp(Ap(zp(yp(xp(vp(up(wp(tp(sp(rp(new qp(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Vp(d.messageContext.eventId,t,"1");var x=d.type,y=d.C.id;if(Ck&&x==="config"){var B,C=(B=Uo(y))==null?void 0:B.ids;if(!(C&&C.length>1)){var E,F=jc("google_tag_data",{});F.td||(F.td={});E=F.td;var I=Xc(v.O);Xc(v.C,I);var L=[],U;for(U in E)E.hasOwnProperty(U)&&jp(E[U],I).length&&L.push(U);L.length&&(hp(y,L),Wa("TAGGING",ap[A.readyState]||14));E[y]=I}}f(d.C.id,b,d.H,v)}catch(K){Vp(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():Tm(e.ia,w)}}};
fq.prototype.register=function(a,b,c){var d=bq(this,a);d.status!==3&&(d.fa=b,d.status=3,d.ia=Vm(c),this.flush())};
fq.prototype.push=function(a,b,c,d){c!==void 0&&(bq(this,c).status===1&&(bq(this,c).status=2,this.push("require",[{}],c,{})),bq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata.send_to_destinations||(d.eventMetadata.send_to_destinations=[c.destinationId]),d.eventMetadata.send_to_targets||(d.eventMetadata.send_to_targets=[c.id]));this.commands.push(new eq(a,c,b,d));d.deferrable||this.flush()};
fq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={ac:void 0,ug:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||bq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(bq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];ib(h,function(u,v){Xc(wb(u,v),b.C)});mj(h,!0);break;case "config":var m=bq(this,g);
e.ac={};ib(f.args[0],function(u){return function(v,w){Xc(wb(v,w),u.ac)}}(e));var n=!!e.ac[N.m.Rc];delete e.ac[N.m.Rc];var p=g.destinationId===g.id;mj(e.ac,!0);n||(p?m.O={}:m.R[g.id]={});m.N&&n||gq(this,N.m.la,e.ac,f);m.N=!0;p?Xc(e.ac,m.O):(Xc(e.ac,m.R[g.id]),O(70));d=!0;un(e.ac,g.id);on=!0;break;case "event":e.ug={};ib(f.args[0],function(u){return function(v,w){Xc(wb(v,w),u.ug)}}(e));mj(e.ug);gq(this,f.args[1],e.ug,f);var q=void 0;!f.C||((q=f.messageContext.eventMetadata)==null?0:q.em_event)||(rn[f.C.id]=
!0);on=!0;break;case "get":var r={},t=(r[N.m.Vb]=f.args[0],r[N.m.qc]=f.args[1],r);gq(this,N.m.lb,t,f);on=!0}this.commands.shift();hq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var hq=function(a,b){if(b.type!=="require")if(b.C)for(var c=bq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Yp=new fq;function iq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function jq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function kq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=hl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=cc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}jq(e,"load",f);jq(e,"error",f)};iq(e,"load",f);iq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function lq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";el(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});mq(c,b)}
function mq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else kq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var nq=function(){this.fa=this.fa;this.O=this.O};nq.prototype.fa=!1;nq.prototype.dispose=function(){this.fa||(this.fa=!0,this.N())};nq.prototype[Symbol.dispose]=function(){this.dispose()};nq.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.O||(this.O=[]),b&&(a=a.bind(b)),this.O.push(a))};nq.prototype.N=function(){if(this.O)for(;this.O.length;)this.O.shift()()};function oq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var pq=function(a,b){b=b===void 0?{}:b;nq.call(this);this.C=null;this.ia={};this.yc=0;this.R=null;this.H=a;var c;this.ab=(c=b.timeoutMs)!=null?c:500;var d;this.Ea=(d=b.Qo)!=null?d:!1};qa(pq,nq);pq.prototype.N=function(){this.ia={};this.R&&(jq(this.H,"message",this.R),delete this.R);delete this.ia;delete this.H;delete this.C;nq.prototype.N.call(this)};var rq=function(a){return typeof a.H.__tcfapi==="function"||qq(a)!=null};
pq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ea},d=Xk(function(){return a(c)}),e=0;this.ab!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.ab));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=oq(c),c.internalBlockOnErrors=b.Ea,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{sq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};pq.prototype.removeEventListener=function(a){a&&a.listenerId&&sq(this,"removeEventListener",null,a.listenerId)};
var uq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=tq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&tq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?tq(a.purpose.legitimateInterests,
b)&&tq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},tq=function(a,b){return!(!a||!a[b])},sq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(qq(a)){vq(a);var g=++a.yc;a.ia[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},qq=function(a){if(a.C)return a.C;a.C=fl(a.H,"__tcfapiLocator");return a.C},vq=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ia[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;iq(a.H,"message",b)}},wq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=oq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(lq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var xq={1:0,3:0,4:0,7:3,9:3,10:3};function yq(){return Lo("tcf",function(){return{}})}var zq=function(){return new pq(z,{timeoutMs:-1})};
function Aq(){var a=yq(),b=zq();rq(b)&&!Bq()&&!Cq()&&O(124);if(!a.active&&rq(b)){Bq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,vm().active=!0,a.tcString="tcunavailable");Eo();try{b.addEventListener(function(c){if(c.internalErrorState!==0)Dq(a),Fo([N.m.T,N.m.Da,N.m.U]),vm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,Cq()&&(a.active=!0),!Eq(c)||Bq()||Cq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in xq)xq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(Eq(c)){var g={},h;for(h in xq)if(xq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={Zm:!0};p=p===void 0?{}:p;m=wq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.Zm)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?uq(n,"1",0):!0:!1;g["1"]=m}else g[h]=uq(c,h,xq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[N.m.T]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Fo([N.m.T,N.m.Da,N.m.U]),vm().active=!0):(r[N.m.Da]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[N.m.U]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Fo([N.m.U]),yo(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:Fq()||""}))}}else Fo([N.m.T,N.m.Da,N.m.U])})}catch(c){Dq(a),Fo([N.m.T,N.m.Da,N.m.U]),vm().active=!0}}}
function Dq(a){a.type="e";a.tcString="tcunavailable"}function Eq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function Bq(){return z.gtag_enable_tcf_support===!0}function Cq(){return yq().enableAdvertiserConsentMode===!0}function Fq(){var a=yq();if(a.active)return a.tcString}function Gq(){var a=yq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Hq(a){if(!xq.hasOwnProperty(String(a)))return!0;var b=yq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Iq=[N.m.T,N.m.Z,N.m.U,N.m.Da],Jq={},Kq=(Jq[N.m.T]=1,Jq[N.m.Z]=2,Jq);function Lq(a){if(a===void 0)return 0;switch(P(a,N.m.ya)){case void 0:return 1;case !1:return 3;default:return 2}}function Mq(a){if(Mn()==="US-CO"&&fc.globalPrivacyControl===!0)return!1;var b=Lq(a);if(b===3)return!1;switch(Em(N.m.Da)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Nq(){return Hm()||!Dm(N.m.T)||!Dm(N.m.Z)}
function Oq(){var a={},b;for(b in Kq)Kq.hasOwnProperty(b)&&(a[Kq[b]]=Em(b));return"G1"+Me(a[1]||0)+Me(a[2]||0)}var Pq={},Qq=(Pq[N.m.T]=0,Pq[N.m.Z]=1,Pq[N.m.U]=2,Pq[N.m.Da]=3,Pq);function Rq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Sq(a){for(var b="1",c=0;c<Iq.length;c++){var d=b,e,f=Iq[c],g=Cm.delegatedConsentTypes[f];e=g===void 0?0:Qq.hasOwnProperty(g)?12|Qq[g]:8;var h=vm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Rq(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Rq(m.declare)<<4|Rq(m.default)<<2|Rq(m.update)])}var n=b,p=(Mn()==="US-CO"&&fc.globalPrivacyControl===!0?1:0)<<3,q=(Hm()?1:0)<<2,r=Lq(a);b=
n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Cm.containerScopedDefaults.ad_storage<<4|Cm.containerScopedDefaults.analytics_storage<<2|Cm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Cm.usedContainerScopedDefaults?1:0)<<2|Cm.containerScopedDefaults.ad_personalization]}
function Tq(){if(!Dm(N.m.U))return"-";for(var a=Object.keys(di),b=Fm(a),c="",d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;b[f]&&(c+=di[f])}(Cm.usedCorePlatformServices?Cm.selectedAllCorePlatformServices:1)&&(c+="o");return c||"-"}function Uq(){return On()||(Bq()||Cq())&&Gq()==="1"?"1":"0"}function Vq(){return(On()?!0:!(!Bq()&&!Cq())&&Gq()==="1")||!Dm(N.m.U)}
function Wq(){var a="0",b="0",c;var d=yq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=yq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;On()&&(h|=1);Gq()==="1"&&(h|=2);Bq()&&(h|=4);var m;var n=yq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);vm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Xq(){return Mn()==="US-CO"};function Yq(){var a=!1;return a};var Zq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function $q(a){a=a===void 0?{}:a;var b=Wf.ctid.split("-")[0].toUpperCase(),c={ctid:Wf.ctid,Yn:rj.wh,ao:rj.xh,En:Ql.Le?2:1,io:a.Kk,Ve:Wf.canonicalContainerId};c.Ve!==a.Fa&&(c.Fa=a.Fa);var d=cm();c.Mn=d?d.canonicalContainerId:void 0;xj?(c.Fg=Zq[b],c.Fg||(c.Fg=0)):c.Fg=Bj?13:10;oj.C?(c.Cg=0,c.Am=2):zj?c.Cg=1:Yq()?c.Cg=2:c.Cg=3;var e={};e[6]=Rl;oj.H===2?e[7]=!0:oj.H===1&&(e[2]=!0);if(ic){var f=fk(lk(ic),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Dm=e;var g=a.pg,h;var m=c.Fg,
n=c.Cg;m===void 0?h="":(n||(n=0),h=""+Oe(1,1)+Le(m<<2|n));var p=c.Am,q="4"+h+(p?""+Oe(2,1)+Le(p):""),r,t=c.ao;r=t&&Ne.test(t)?""+Oe(3,2)+t:"";var u,v=c.Yn;u=v?""+Oe(4,1)+Le(v):"";var w;var x=c.ctid;if(x&&g){var y=x.split("-"),B=y[0].toUpperCase();if(B!=="GTM"&&B!=="OPT")w="";else{var C=y[1];w=""+Oe(5,3)+Le(1+C.length)+(c.En||0)+C}}else w="";var E=c.io,F=c.Ve,I=c.Fa,L=c.Xo,U=q+r+u+w+(E?""+Oe(6,1)+Le(E):"")+(F?""+Oe(7,3)+Le(F.length)+F:"")+(I?""+Oe(8,3)+Le(I.length)+I:"")+(L?""+Oe(9,3)+Le(L.length)+
L:""),K;var Z=c.Dm;Z=Z===void 0?{}:Z;for(var Y=[],ha=l(Object.keys(Z)),S=ha.next();!S.done;S=ha.next()){var R=S.value;Y[Number(R)]=Z[R]}if(Y.length){var ja=Oe(10,3),ia;if(Y.length===0)ia=Le(0);else{for(var la=[],Ia=0,Qa=!1,Aa=0;Aa<Y.length;Aa++){Qa=!0;var Va=Aa%6;Y[Aa]&&(Ia|=1<<Va);Va===5&&(la.push(Le(Ia)),Ia=0,Qa=!1)}Qa&&la.push(Le(Ia));ia=la.join("")}var db=ia;K=""+ja+Le(db.length)+db}else K="";var Eb=c.Mn;return U+K+(Eb?""+Oe(11,3)+Le(Eb.length)+Eb:"")};function ar(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var br={M:{lm:0,Gi:1,Df:2,Ji:3,Jg:4,Hi:5,Ii:6,Ki:7,Kg:8,Lj:9,Kj:10,ph:11,Mj:12,kg:13,Oj:14,Ne:15,km:16,Ed:17,Dh:18,Eh:19,Fh:20,dk:21,Gh:22,Lg:23,Ri:24}};br.M[br.M.lm]="RESERVED_ZERO";br.M[br.M.Gi]="ADS_CONVERSION_HIT";br.M[br.M.Df]="CONTAINER_EXECUTE_START";br.M[br.M.Ji]="CONTAINER_SETUP_END";br.M[br.M.Jg]="CONTAINER_SETUP_START";br.M[br.M.Hi]="CONTAINER_BLOCKING_END";br.M[br.M.Ii]="CONTAINER_EXECUTE_END";br.M[br.M.Ki]="CONTAINER_YIELD_END";br.M[br.M.Kg]="CONTAINER_YIELD_START";br.M[br.M.Lj]="EVENT_EXECUTE_END";
br.M[br.M.Kj]="EVENT_EVALUATION_END";br.M[br.M.ph]="EVENT_EVALUATION_START";br.M[br.M.Mj]="EVENT_SETUP_END";br.M[br.M.kg]="EVENT_SETUP_START";br.M[br.M.Oj]="GA4_CONVERSION_HIT";br.M[br.M.Ne]="PAGE_LOAD";br.M[br.M.km]="PAGEVIEW";br.M[br.M.Ed]="SNIPPET_LOAD";br.M[br.M.Dh]="TAG_CALLBACK_ERROR";br.M[br.M.Eh]="TAG_CALLBACK_FAILURE";br.M[br.M.Fh]="TAG_CALLBACK_SUCCESS";br.M[br.M.dk]="TAG_EXECUTE_END";br.M[br.M.Gh]="TAG_EXECUTE_START";br.M[br.M.Lg]="CUSTOM_PERFORMANCE_START";br.M[br.M.Ri]="CUSTOM_PERFORMANCE_END";var cr=[],dr={},er={};var fr=["1"];function gr(a){return a.origin!=="null"};function hr(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return jg(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function ir(a,b,c,d){if(!jr(d))return[];if(cr.includes("1")){var e;(e=Lc())==null||e.mark("1-"+br.M.Lg+"-"+(er["1"]||0))}var f=hr(a,String(b||kr()),c);if(cr.includes("1")){var g="1-"+br.M.Ri+"-"+(er["1"]||0),h={start:"1-"+br.M.Lg+"-"+(er["1"]||0),end:g},m;(m=Lc())==null||m.mark(g);var n,p,q=(p=(n=Lc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(er["1"]=(er["1"]||0)+1,dr["1"]=q+(dr["1"]||0))}return f}
function lr(a,b,c,d,e){if(jr(e)){var f=mr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=nr(f,function(g){return g.Mm},b);if(f.length===1)return f[0];f=nr(f,function(g){return g.On},c);return f[0]}}}function or(a,b,c,d){var e=kr(),f=window;gr(f)&&(f.document.cookie=a);var g=kr();return e!==g||c!==void 0&&ir(b,g,!1,d).indexOf(c)>=0}
function pr(a,b,c,d){function e(w,x,y){if(y==null)return delete h[x],w;h[x]=y;return w+"; "+x+"="+y}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!jr(c.Lb))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=qr(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.In);g=e(g,"samesite",c.bo);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=rr(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!sr(u,c.path)&&or(v,a,b,c.Lb))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return sr(n,c.path)?1:or(g,a,b,c.Lb)?0:1}function tr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return pr(a,b,c)}
function nr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function mr(a,b,c){for(var d=[],e=ir(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Fm:e[f],Gm:g.join("."),Mm:Number(n[0])||1,On:Number(n[1])||1})}}}return d}function qr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ur=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,vr=/(^|\.)doubleclick\.net$/i;function sr(a,b){return a!==void 0&&(vr.test(window.document.location.hostname)||b==="/"&&ur.test(a))}function wr(a){if(!a)return 1;var b=a;jg(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function xr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function yr(a,b){var c=""+wr(a),d=xr(b);d>1&&(c+="-"+d);return c}
var kr=function(){return gr(window)?window.document.cookie:""},jr=function(a){return a&&jg(8)?(Array.isArray(a)?a:[a]).every(function(b){return Gm(b)&&Dm(b)}):!0},rr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;vr.test(e)||ur.test(e)||a.push("none");return a};function zr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^ar(a)&2147483647):String(b)}function Ar(a){return[zr(a),Math.round(pb()/1E3)].join(".")}function Br(a,b,c,d,e){var f=wr(b),g;return(g=lr(a,f,xr(c),d,e))==null?void 0:g.Gm}function Cr(a,b,c,d){return[b,yr(c,d),a].join(".")};function Dr(a,b,c,d){var e,f=Number(a.Kb!=null?a.Kb:void 0);f!==0&&(e=new Date((b||pb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Lb:d}};var Er=["ad_storage","ad_user_data"];function Fr(a,b){if(!a)return 10;if(b===null||b===void 0||b==="")return 11;var c=Gr(!1);if(c.error!==0)return c.error;if(!c.value)return 2;c.value[a]=b;return Hr(c)}function Ir(a){if(!a)return{error:10};var b=Gr();if(b.error!==0)return b;if(!b.value)return{error:2};if(!(a in b.value))return{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?{value:void 0,error:11}:{value:c,error:0}}
function Gr(a){a=a===void 0?!0:a;if(!Dm(Er))return{error:3};try{if(!z.localStorage)return{error:1}}catch(f){return{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=z.localStorage.getItem("_gcl_ls")}catch(f){return{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return{error:12}}}catch(f){return{error:8}}if(b.schema!=="gcl")return{error:4};if(b.version!==1)return{error:5};try{var e=Jr(b);a&&e&&Hr({value:b,error:0})}catch(f){return{error:8}}return{value:b,error:0}}
function Jr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Jr(a[e.value])||c;return c}return!1}
function Hr(a){if(a.error)return a.error;if(!a.value)return 2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return 6}try{z.localStorage.setItem("_gcl_ls",c)}catch(d){return 7}return 0};function Kr(){if(!Lr())return-1;var a=Mr();return a!==-1&&Nr(a+1)?a+1:-1}function Mr(){if(!Lr())return-1;var a=Ir("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Lr(){return Dm(["ad_storage","ad_user_data"])?jg(11):!1}
function Nr(a,b){b=b||{};var c=pb();return Fr("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(Dr(b,c,!0).expires)})===0?!0:!1};var Or;function Pr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Qr,d=Rr,e=Sr();if(!e.init){wc(A,"mousedown",a);wc(A,"keyup",a);wc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Tr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Sr().decorators.push(f)}
function Ur(a,b,c){for(var d=Sr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&sb(e,g.callback())}}return e}
function Sr(){var a=jc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Vr=/(.*?)\*(.*?)\*(.*)/,Wr=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Xr=/^(?:www\.|m\.|amp\.)+/,Yr=/([^?#]+)(\?[^#]*)?(#.*)?/;function Zr(a){var b=Yr.exec(a);if(b)return{ri:b[1],query:b[2],fragment:b[3]}}function $r(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function as(a,b){var c=[fc.userAgent,(new Date).getTimezoneOffset(),fc.userLanguage||fc.language,Math.floor(pb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Or)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Or=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Or[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function bs(a){return function(b){var c=lk(z.location.href),d=c.search.replace("?",""),e=dk(d,"_gl",!1,!0)||"";b.query=cs(e)||{};var f=fk(c,"fragment"),g;var h=-1;if(ub(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=cs(g||"")||{};a&&ds(c,d,f)}}function es(a,b){var c=$r(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function ds(a,b,c){function d(g,h){var m=es("_gl",g);m.length&&(m=h+m);return m}if(ec&&ec.replaceState){var e=$r("_gl");if(e.test(b)||e.test(c)){var f=fk(a,"path");b=d(b,"?");c=d(c,"#");ec.replaceState({},"",""+f+b+c)}}}function fs(a,b){var c=bs(!!b),d=Sr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(sb(e,f.query),a&&sb(e,f.fragment));return e}
var cs=function(a){try{var b=gs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Ta(d[e+1]);c[f]=g}Wa("TAGGING",6);return c}}catch(h){Wa("TAGGING",8)}};function gs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Vr.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===as(h,p)){m=!0;break a}m=!1}if(m)return h;Wa("TAGGING",7)}}}
function hs(a,b,c,d,e){function f(p){p=es(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Zr(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.ri+h+m}
function is(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Sa(String(x))))}var y=v.join("*");u=["1",as(y),y].join("*");d?(jg(3)||jg(1)||!p)&&js("_gl",u,a,p,q):ks("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ur(b,1,d),f=Ur(b,2,d),g=Ur(b,4,d),h=Ur(b,3,d);c(e,!1,!1);c(f,!0,!1);jg(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ls(m,h[m],a)}function ls(a,b,c){c.tagName.toLowerCase()==="a"?ks(a,b,c):c.tagName.toLowerCase()==="form"&&js(a,b,c)}function ks(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!jg(5)||d)){var h=z.location.href,m=Zr(c.href),n=Zr(h);g=!(m&&n&&m.ri===n.ri&&m.query===n.query&&m.fragment)}f=g}if(f){var p=hs(a,b,c.href,d,e);Vb.test(p)&&(c.href=p)}}
function js(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=hs(a,b,f,d,e);Vb.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Qr(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||is(e,e.hostname)}}catch(g){}}function Rr(a){try{var b=a.getAttribute("action");if(b){var c=fk(lk(b),"host");is(a,c)}}catch(d){}}function ms(a,b,c,d){Pr();var e=c==="fragment"?2:1;d=!!d;Tr(a,b,e,d,!1);e===2&&Wa("TAGGING",23);d&&Wa("TAGGING",24)}
function ns(a,b){Pr();Tr(a,[hk(z.location,"host",!0)],b,!0,!0)}function os(){var a=A.location.hostname,b=Wr.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Xr,""),m=e.replace(Xr,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ps(a,b){return a===!1?!1:a||b||os()};var qs=["1"],rs={},ss={};function ts(a,b){b=b===void 0?!0:b;var c=us(a.prefix);if(rs[c])vs(a);else if(ws(c,a.path,a.domain)){var d=ss[us(a.prefix)]||{id:void 0,Bg:void 0};b&&xs(a,d.id,d.Bg);vs(a)}else{var e=nk("auiddc");if(e)Wa("TAGGING",17),rs[c]=e;else if(b){var f=us(a.prefix),g=Ar();ys(f,g,a);ws(c,a.path,a.domain);vs(a,!0)}}}
function vs(a,b){if((b===void 0?0:b)&&Lr()){var c=Gr(!1);c.error===0&&c.value&&"gcl_ctr"in c.value&&(delete c.value.gcl_ctr,Hr(c))}Dm(["ad_storage","ad_user_data"])&&jg(10)&&Mr()===-1&&Nr(0,a)}function xs(a,b,c){var d=us(a.prefix),e=rs[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(pb()/1E3)));ys(d,h,a,g*1E3)}}}}function ys(a,b,c,d){var e=Cr(b,"1",c.domain,c.path),f=Dr(c,d);f.Lb=zs();tr(a,e,f)}
function ws(a,b,c){var d=Br(a,b,c,qs,zs());if(!d)return!1;As(a,d);return!0}function As(a,b){var c=b.split(".");c.length===5?(rs[a]=c.slice(0,2).join("."),ss[a]={id:c.slice(2,4).join("."),Bg:Number(c[4])||0}):c.length===3?ss[a]={id:c.slice(0,2).join("."),Bg:Number(c[2])||0}:rs[a]=b}function us(a){return(a||"_gcl")+"_au"}function Bs(a){function b(){Dm(c)&&a()}var c=zs();Km(function(){b();Dm(c)||Lm(b,c)},c)}
function Cs(a){var b=fs(!0),c=us(a.prefix);Bs(function(){var d=b[c];if(d){As(c,d);var e=Number(rs[c].split(".")[1])*1E3;if(e){Wa("TAGGING",16);var f=Dr(a,e);f.Lb=zs();var g=Cr(d,"1",a.domain,a.path);tr(c,g,f)}}})}function Ds(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Br(a,e.path,e.domain,qs,zs());h&&(g[a]=h);return g};Bs(function(){ms(f,b,c,d)})}function zs(){return["ad_storage","ad_user_data"]};function Es(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Ei:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Fs(a,b){var c=Es(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Ei]||(d[c[e].Ei]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,aa:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Ei].push(g)}}return d};var Gs={},Hs=(Gs.k={X:/^[\w-]+$/},Gs.b={X:/^[\w-]+$/,zi:!0},Gs.i={X:/^[1-9]\d*$/},Gs.h={X:/^\d+$/},Gs.t={X:/^[1-9]\d*$/},Gs.d={X:/^[A-Za-z0-9_-]+$/},Gs.j={X:/^\d+$/},Gs.u={X:/^[1-9]\d*$/},Gs.l={X:/^[01]$/},Gs.o={X:/^[1-9]\d*$/},Gs.g={X:/^[01]$/},Gs.s={X:/^.+$/},Gs);var Is={},Ms=(Is[5]={Hg:{2:Js},hi:"2",qg:["k","i","b","u"]},Is[4]={Hg:{2:Js,GCL:Ks},hi:"2",qg:["k","i","b"]},Is[2]={Hg:{GS2:Js,GS1:Ls},hi:"GS2",qg:"sogtjlhd".split("")},Is);function Ns(a,b,c){var d=Ms[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Hg[e];if(f)return f(a,b)}}}
function Js(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ms[b];if(f){for(var g=f.qg,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Hs[p];r&&(r.zi?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Os(a,b,c){var d=Ms[b];if(d)return[d.hi,c||"1",Ps(a,b)].join(".")}
function Ps(a,b){var c=Ms[b];if(c){for(var d=[],e=l(c.qg),f=e.next();!f.done;f=e.next()){var g=f.value,h=Hs[g];if(h){var m=a[g];if(m!==void 0)if(h.zi&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Ks(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Ls(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Qs=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Rs(a,b,c){if(Ms[b]){for(var d=[],e=ir(a,void 0,void 0,Qs.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Ns(g.value,b,c);h&&d.push(Ss(h))}return d}}function Ts(a,b,c,d,e){d=d||{};var f=yr(d.domain,d.path),g=Os(b,c,f);if(!g)return 1;var h=Dr(d,e,void 0,Qs.get(c));return tr(a,g,h)}function Us(a,b){var c=b.X;return typeof c==="function"?c(a):c.test(a)}
function Ss(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Xe:void 0},c=b.next()){var e=c.value,f=a[e];d.Xe=Hs[e];d.Xe?d.Xe.zi?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Us(h,g.Xe)}}(d)):void 0:typeof f==="string"&&Us(f,d.Xe)||(a[e]=void 0):a[e]=void 0}return a};function Vs(){var a=String,b=z.location.hostname,c=z.location.pathname,d=b=Cb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Cb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(ar((""+b+e).toLowerCase()))};var Ws=/^\w+$/,Xs=/^[\w-]+$/,Ys={},Zs=(Ys.aw="_aw",Ys.dc="_dc",Ys.gf="_gf",Ys.gp="_gp",Ys.gs="_gs",Ys.ha="_ha",Ys.ag="_ag",Ys.gb="_gb",Ys);function $s(){return["ad_storage","ad_user_data"]}function at(a){return!jg(8)||Dm(a)}function bt(a,b){function c(){var d=at(b);d&&a();return d}Km(function(){c()||Lm(c,b)},b)}function ct(a){return dt(a).map(function(b){return b.aa})}function et(a){return ft(a).filter(function(b){return b.aa}).map(function(b){return b.aa})}
function ft(a){var b=gt(a.prefix),c=ht("gb",b),d=ht("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=dt(c).map(e("gb")),g=it(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}function jt(a,b,c,d,e,f){var g=eb(a,function(h){return h.aa===c});g?(g.timestamp<d&&(g.timestamp=d,g.Zc=f),g.labels=kt(g.labels||[],e||[])):a.push({version:b,aa:c,timestamp:d,labels:e,Zc:f})}
function it(a){for(var b=Rs(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=lt(f);if(n){var p=void 0;jg(9)&&(p=f.u);jt(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}function dt(a){for(var b=[],c=ir(a,A.cookie,void 0,$s()),d=l(c),e=d.next();!e.done;e=d.next()){var f=mt(e.value);if(f!=null){var g=f;jt(b,g.version,g.aa,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return nt(b)}
function ot(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function pt(a,b){var c;c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.aa===b.aa){d=h;break}h.ja===b.ja&&(e=h)}d?(d.ja=d.ja?b.ja?b.ja===5?d.ja<5?b.ja+d.ja:d.ja:d.timestamp<b.timestamp?b.ja:d.ja:d.ja||0:b.ja||0,d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Zc=b.Zc),d.labels=ot(d.labels||[],b.labels||[]),d.tb=ot(d.tb||[],b.tb||[])):c&&e?Object.assign(e,b):a.push(b)}
function qt(){var a=Ir("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;return d&&d.match(Xs)?{version:"",aa:d,timestamp:Number(c.creationTimeMs)||0,labels:[],ja:c.linkDecorationSource||0,tb:[2]}:null}catch(e){return null}}
function rt(){var a=Ir("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Xs))return b;b.push({version:"",aa:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],ja:d.linkDecorationSource||0,tb:[2]});return b},[])}catch(b){return null}}
function st(a){for(var b=[],c=ir(a,A.cookie,void 0,$s()),d=l(c),e=d.next();!e.done;e=d.next()){var f=mt(e.value);f!=null&&(f.Zc=void 0,f.ja=0,f.tb=[1],pt(b,f))}var g=qt();g&&(g.Zc=void 0,g.ja=g.ja||0,g.tb=g.tb||[2],pt(b,g));if(jg(14)){var h=rt();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Zc=void 0;p.ja=p.ja||0;p.tb=p.tb||[2];pt(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return nt(b)}
function kt(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function gt(a){return a&&typeof a==="string"&&a.match(Ws)?a:"_gcl"}
function tt(a,b,c){var d=lk(a),e=fk(d,"query",!1,void 0,"gclsrc"),f={value:fk(d,"query",!1,void 0,"gclid"),ja:c?4:2};if(b&&(!f.value||!e)){var g=d.hash.replace("#","");f.value||(f.value=dk(g,"gclid",!1),f.ja=3);e||(e=dk(g,"gclsrc",!1))}return!f.value||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function ut(a,b){var c=lk(a),d=fk(c,"query",!1,void 0,"gclid"),e=fk(c,"query",!1,void 0,"gclsrc"),f=fk(c,"query",!1,void 0,"wbraid");f=Ab(f);var g=fk(c,"query",!1,void 0,"gbraid"),h=fk(c,"query",!1,void 0,"gad_source"),m=fk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||dk(n,"gclid",!1);e=e||dk(n,"gclsrc",!1);f=f||dk(n,"wbraid",!1);g=g||dk(n,"gbraid",!1);h=h||dk(n,"gad_source",!1)}return vt(d,e,m,f,g,h)}function wt(){return ut(z.location.href,!0)}
function vt(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Xs))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Xs.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Xs.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Xs.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function xt(a){for(var b=wt(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=ut(z.document.referrer,!1),b.gad_source=void 0);zt(b,!1,a)}
function At(a){xt(a);var b=tt(z.location.href,!0,!1);b.length||(b=tt(z.document.referrer,!1,!0));if(b.length){var c=b[0];a=a||{};var d=pb(),e=Dr(a,d,!0),f=$s(),g=function(){at(f)&&e.expires!==void 0&&Fr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSource:c.ja},expires:Number(e.expires)})};Km(function(){g();at(f)||Lm(g,f)},f)}}
function zt(a,b,c,d,e){c=c||{};e=e||[];var f=gt(c.prefix),g=d||pb(),h=Math.round(g/1E3),m=$s(),n=!1,p=!1,q=function(){if(at(m)){var r=Dr(c,g,!0);r.Lb=m;for(var t=function(L,U){var K=ht(L,f);K&&(tr(K,U,r),L!=="gb"&&(n=!0))},u=function(L){var U=["GCL",h,L];e.length>0&&U.push(e.join("."));return U.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var y=a.gb[0],B=ht("gb",f);!b&&dt(B).some(function(L){return L.aa===y&&L.labels&&L.labels.length>
0})||t("gb",u(y))}}if(!p&&a.gbraid&&at("ad_storage")&&(p=!0,!n)){var C=a.gbraid,E=ht("ag",f);if(b||!it(E).some(function(L){return L.aa===C&&L.labels&&L.labels.length>0})){var F={},I=(F.k=C,F.i=""+h,F.b=e,F);Ts(E,I,5,c,g)}}Bt(a,f,g,c)};Km(function(){q();at(m)||Lm(q,m)},m)}
function Bt(a,b,c,d){if(a.gad_source!==void 0&&at("ad_storage")){if(jg(4)){var e=Kc();if(e==="r"||e==="h")return}var f=a.gad_source,g=ht("gs",b);if(g){var h=Math.floor((pb()-(Jc()||0))/1E3),m;if(jg(9)){var n=Vs(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Ts(g,m,5,d,c)}}}
function Ct(a,b){var c=fs(!0);bt(function(){for(var d=gt(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Zs[f]!==void 0){var g=ht(f,d),h=c[g];if(h){var m=Math.min(Dt(h),pb()),n;b:{for(var p=m,q=ir(g,A.cookie,void 0,$s()),r=0;r<q.length;++r)if(Dt(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Dr(b,m,!0);t.Lb=$s();tr(g,h,t)}}}}zt(vt(c.gclid,c.gclsrc),!1,b)},$s())}
function Et(a){var b=["ag"],c=fs(!0),d=gt(a.prefix);bt(function(){for(var e=0;e<b.length;++e){var f=ht(b[e],d);if(f){var g=c[f];if(g){var h=Ns(g,5);if(h){var m=lt(h);m||(m=pb());var n;a:{for(var p=m,q=Rs(f,5),r=0;r<q.length;++r)if(lt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ts(f,h,5,a,m)}}}}},["ad_storage"])}function ht(a,b){var c=Zs[a];if(c!==void 0)return b+c}function Dt(a){return Ft(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function lt(a){return a?(Number(a.i)||0)*1E3:0}function mt(a){var b=Ft(a.split("."));return b.length===0?null:{version:b[0],aa:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Ft(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Xs.test(a[2])?[]:a}
function Gt(a,b,c,d,e){if(Array.isArray(b)&&gr(z)){var f=gt(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=ht(a[m],f);if(n){var p=ir(n,A.cookie,void 0,$s());p.length&&(h[n]=p.sort()[p.length-1])}}return h};bt(function(){ms(g,b,c,d)},$s())}}
function Ht(a,b,c,d){if(Array.isArray(a)&&gr(z)){var e=["ag"],f=gt(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=ht(e[m],f);if(!n)return{};var p=Rs(n,5);if(p.length){var q=p.sort(function(r,t){return lt(t)-lt(r)})[0];h[n]=Os(q,5)}}return h};bt(function(){ms(g,a,b,c)},["ad_storage"])}}function nt(a){return a.filter(function(b){return Xs.test(b.aa)})}
function It(a,b){if(gr(z)){for(var c=gt(b.prefix),d={},e=0;e<a.length;e++)Zs[a[e]]&&(d[a[e]]=Zs[a[e]]);bt(function(){ib(d,function(f,g){var h=ir(c+g,A.cookie,void 0,$s());h.sort(function(t,u){return Dt(u)-Dt(t)});if(h.length){var m=h[0],n=Dt(m),p=Ft(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Ft(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];zt(q,!0,b,n,p)}})},$s())}}
function Jt(a){var b=["ag"],c=["gbraid"];bt(function(){for(var d=gt(a.prefix),e=0;e<b.length;++e){var f=ht(b[e],d);if(!f)break;var g=Rs(f,5);if(g.length){var h=g.sort(function(q,r){return lt(r)-lt(q)})[0],m=lt(h),n=h.b,p={};p[c[e]]=h.k;zt(p,!0,a,m,n)}}},["ad_storage"])}function Kt(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Lt(a){function b(h,m,n){n&&(h[m]=n)}if(Hm()){var c=wt(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:fs(!1)._gs);if(Kt(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ns(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ns(function(){return g},1)}}}
function Mt(a){if(!jg(1))return null;var b=fs(!0).gad_source;if(b!=null)return z.location.hash="",b;if(jg(2)){var c=lk(z.location.href);b=fk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=wt();if(Kt(d,a))return"0"}return null}function Nt(a){var b=Mt(a);b!=null&&ns(function(){var c={};return c.gad_source=b,c},4)}
function Ot(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Pt(a,b,c,d){var e=[];c=c||{};if(!at($s()))return e;var f=dt(a),g=Ot(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.aa].concat(n.labels||[],[b]).join("."),r=Dr(c,p,!0);r.Lb=$s();tr(a,q,r)}return e}
function Qt(a,b){var c=[];b=b||{};var d=ft(b),e=Ot(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=gt(b.prefix),n=ht(h.type,m);if(!n)break;var p=h,q=p.version,r=p.aa,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Ts(n,x,5,b,u)}else if(h.type==="gb"){var y=[q,v,r].concat(t||[],[a]).join("."),B=Dr(b,u,!0);B.Lb=$s();tr(n,y,B)}}return c}
function Rt(a,b){var c=gt(b),d=ht(a,c);if(!d)return 0;var e;e=a==="ag"?it(d):dt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function St(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Tt(a){var b=Math.max(Rt("aw",a),St(at($s())?Fs():{})),c=Math.max(Rt("gb",a),St(at($s())?Fs("_gac_gb",!0):{}));c=Math.max(c,Rt("ag",a));return c>b};function iu(){return Lo("dedupe_gclid",function(){return Ar()})};var ju=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,ku=/^www.googleadservices.com$/;function lu(a){a||(a=mu());return a.qo?!1:a.pn||a.qn||a.tn||a.rn||a.df||a.Ym||a.sn||a.gn?!0:!1}function mu(){var a={},b=fs(!0);a.qo=!!b._up;var c=wt();a.pn=c.aw!==void 0;a.qn=c.dc!==void 0;a.tn=c.wbraid!==void 0;a.rn=c.gbraid!==void 0;a.sn=c.gclsrc==="aw.ds";a.df=Wt().df;var d=A.referrer?fk(lk(A.referrer),"host"):"";a.gn=ju.test(d);a.Ym=ku.test(d);return a};var nu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];
function ou(){if(H(118)){if(Bn(wn.Pe))return O(176),wn.Pe;if(Bn(wn.Uj))return O(170),wn.Pe;var a=il();if(!a)O(171);else if(a.opener){var b=function(e){if(nu.includes(e.origin)){e.data.action==="gcl_transfer"&&e.data.gadSource?An(wn.Pe,{gadSource:e.data.gadSource}):O(173);var f;(f=e.stopImmediatePropagation)==null||f.call(e);jq(a,"message",b)}else O(172)};if(iq(a,"message",b)){An(wn.Uj,!0);for(var c=l(nu),d=c.next();!d.done;d=c.next())a.opener.postMessage({action:"gcl_setup"},d.value);O(174);return wn.Pe}O(175)}}}
;var pu=function(){this.C=this.gppString=void 0};pu.prototype.reset=function(){this.C=this.gppString=void 0};var qu=new pu;var ru=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),su=/^~?[\w-]+(?:\.~?[\w-]+)*$/,tu=/^\d+\.fls\.doubleclick\.net$/,uu=/;gac=([^;?]+)/,vu=/;gacgb=([^;?]+)/;
function wu(a,b){if(tu.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(ru)?ek(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].aa);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function xu(a,b,c){for(var d=at($s())?Fs("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Pt("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{Xm:f?e.join(";"):"",Wm:wu(d,vu)}}function yu(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(su)?b[1]:void 0}
function zu(a){var b=jg(9),c={},d,e,f;tu.test(A.location.host)&&(d=yu("gclgs"),e=yu("gclst"),b&&(f=yu("gcllp")));if(d&&e&&(!b||f))c.vg=d,c.xg=e,c.wg=f;else{var g=pb(),h=it((a||"_gcl")+"_gs"),m=h.map(function(q){return q.aa}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Zc}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.vg=m.join("."),c.xg=n.join("."),b&&p.length>0&&(c.wg=p.join(".")))}return c}
function Au(a,b,c,d){d=d===void 0?!1:d;if(tu.test(A.location.host)){var e=yu(c);if(e)return e.split(".").map(function(g){return{aa:g}})}else{if(b==="gclid"){var f=(a||"_gcl")+"_aw";return d?st(f):dt(f)}if(b==="wbraid")return dt((a||"_gcl")+"_gb");if(b==="braids")return ft({prefix:a})}return[]}function Bu(a){return tu.test(A.location.host)?!(yu("gclaw")||yu("gac")):Tt(a)}
function Cu(a,b,c){var d;d=c?Qt(a,b):Pt((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Du(){var a=z.__uspapi;if($a(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
function Qu(a){var b=P(a.D,N.m.uc),c=P(a.D,N.m.sc);b&&!c?(a.eventName!==N.m.la&&a.eventName!==N.m.nd&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}function Ru(a){var b=Ao(N.m.T)?Ko.pscdl:"denied";b!=null&&V(a,N.m.Nf,b)}function Su(a){var b=gl(!0);V(a,N.m.rc,b)}function Tu(a){Xq()&&V(a,N.m.xd,1)}function Hu(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&ek(a.substring(0,b))===void 0;)b--;return ek(a.substring(0,b))||""}
function Uu(a){Vu(a,"ce",P(a.D,N.m.Ya))}function Vu(a,b,c){Gu(a,N.m.Bd)||V(a,N.m.Bd,{});Gu(a,N.m.Bd)[b]=c}function Wu(a){T(a,"transmission_type",um.V.xa)}function Xu(a){var b=Xa("GTAG_EVENT_FEATURE_CHANNEL");b&&(V(a,N.m.te,b),Ua.GTAG_EVENT_FEATURE_CHANNEL=lj)}function Yu(a){if(H(85)){var b=op(a.D,N.m.Mc);b&&V(a,N.m.Mc,b)}}
function Zu(a,b){b=b===void 0?!1:b;if(H(107)){var c=Q(a,"send_to_destinations");if(c)if(c.indexOf(a.target.destinationId)<0){if(T(a,"accept_by_default",!1),b||!$u(a,"custom_event_accept_rules",!1))a.isAborted=!0}else T(a,"accept_by_default",!0)}};function kv(a,b,c,d){var e=sc(),f;if(e===1)a:{var g=Dj;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==z.location.protocol?a:b)+c};function wv(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Gu(a,b)},setHitData:function(b,c){V(a,b,c)},setHitDataIfNotDefined:function(b,c){Gu(a,b)===void 0&&V(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return Q(a,b)},setMetadata:function(b,c){T(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.D,b)},bc:function(){return a},getHitKeys:function(){return Object.keys(a.C)}}};function Dv(a,b){return arguments.length===1?Ev("set",a):Ev("set",a,b)}function Fv(a,b){return arguments.length===1?Ev("config",a):Ev("config",a,b)}function Gv(a,b,c){c=c||{};c[N.m.Oc]=a;return Ev("event",b,c)}function Ev(){return arguments};var Iv=function(){this.messages=[];this.C=[]};Iv.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Iv.prototype.listen=function(a){this.C.push(a)};
Iv.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Iv.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Jv(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata.source_canonical_id=Wf.canonicalContainerId;Kv().enqueue(a,b,c)}
function Lv(){var a=Mv;Kv().listen(a)}function Kv(){return Lo("mb",function(){return new Iv})};var Nv,Ov=!1;function Pv(){Ov=!0;Nv=Nv||{}}function Qv(a){Ov||Pv();return Nv[a]};function Rv(){var a=z.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Sv(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!z.getComputedStyle)return!0;var c=z.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=z.getComputedStyle(d,null))}return!1}
var Uv=function(a){var b=Tv(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},Tv=function(){var a=A.body,b=A.documentElement||a&&a.parentElement,c,d;if(A.compatMode&&A.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var Sf;var qx=Number('')||5,rx=Number('')||50,sx=fb();
var ux=function(a,b){a&&(tx("sid",a.targetId,b),tx("cc",a.clientCount,b),tx("tl",a.totalLifeMs,b),tx("hc",a.heartbeatCount,b),tx("cl",a.clientLifeMs,b))},tx=function(a,b,c){b!=null&&c.push(a+"="+b)},vx=function(){var a=A.referrer;if(a){var b;return fk(lk(a),"host")===((b=z.location)==null?void 0:b.host)?1:2}return 0},xx=function(){this.R=wx;this.N=0};xx.prototype.H=function(a,b,c,d){var e=vx(),f,g=[];f=z===z.top&&e!==0&&b?(b==null?void 0:b.clientCount)>
1?e===2?1:2:e===2?0:3:4;a&&tx("si",a.lf,g);tx("m",0,g);tx("iss",f,g);tx("if",c,g);ux(b,g);d&&tx("fm",encodeURIComponent(d.substring(0,rx)),g);this.O(g);};xx.prototype.C=function(a,b,c,d,e){var f=[];tx("m",1,f);tx("s",a,f);tx("po",vx(),f);b&&(tx("st",b.state,f),tx("si",b.lf,f),tx("sm",b.yf,f));ux(c,f);tx("c",d,f);e&&tx("fm",encodeURIComponent(e.substring(0,rx)),f);this.O(f);};
xx.prototype.O=function(a){a=a===void 0?[]:a;!Bk||this.N>=qx||(tx("pid",sx,a),tx("bc",++this.N,a),a.unshift("ctid="+Wf.ctid+"&t=s"),this.R("https://www.googletagmanager.com/a?"+a.join("&")))};var yx=Number('')||500,zx=Number('')||5E3,Ax=Number('20')||10,Bx=Number('')||5E3;function Cx(a){return a.performance&&a.performance.now()||Date.now()}
var Dx=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{xk:function(){},yk:function(){},wk:function(){},onFailure:function(){}}:g;this.tm=e;this.C=f;this.N=g;this.fa=this.ia=this.heartbeatCount=this.sm=0;this.mg=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.lf=Cx(this.C);this.yf=Cx(this.C);this.R=10};d.prototype.init=function(){this.O(1);this.Ea()};d.prototype.getState=function(){return{state:this.state,
lf:Math.round(Cx(this.C)-this.lf),yf:Math.round(Cx(this.C)-this.yf)}};d.prototype.O=function(e){this.state!==e&&(this.state=e,this.yf=Cx(this.C))};d.prototype.fk=function(){return String(this.sm++)};d.prototype.Ea=function(){var e=this;this.heartbeatCount++;this.ab({type:0,clientId:this.id,requestId:this.fk(),maxDelay:this.ng()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.fa++,f.isDead||e.fa>Ax){var h=f.isDead&&f.failure.failureType;
e.R=h||10;e.O(4);e.qm();var m,n;(n=(m=e.N).wk)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.O(3),e.gk();else{if(e.heartbeatCount>f.stats.heartbeatCount+Ax){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.N).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.O(2);if(r!==2)if(e.mg){var t,u;(u=(t=e.N).yk)==null||u.call(t)}else{e.mg=!0;var v,w;(w=(v=e.N).xk)==null||w.call(v)}e.fa=0;e.vm();e.gk()}}})};d.prototype.ng=function(){return this.state===2?
zx:yx};d.prototype.gk=function(){var e=this;this.C.setTimeout(function(){e.Ea()},Math.max(0,this.ng()-(Cx(this.C)-this.ia)))};d.prototype.ym=function(e,f,g){var h=this;this.ab({type:1,clientId:this.id,requestId:this.fk(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.N).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.ab=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.R},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.C.setTimeout(function(){var r=g.H[m];r&&g.Me(r,7)},(n=e.maxDelay)!=null?n:Bx),q={request:e,Ik:f,Dk:h,Hn:p};this.H[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.ia=Cx(this.C);e.Dk=!1;this.tm(e.request)};d.prototype.vm=function(){for(var e=l(Object.keys(this.H)),f=e.next();!f.done;f=e.next()){var g=this.H[f.value];g.Dk&&this.sendRequest(g)}};d.prototype.qm=function(){for(var e=
l(Object.keys(this.H)),f=e.next();!f.done;f=e.next())this.Me(this.H[f.value],this.R)};d.prototype.Me=function(e,f){this.yc(e);var g=e.request;g.failure={failureType:f};e.Ik(g)};d.prototype.yc=function(e){delete this.H[e.request.requestId];this.C.clearTimeout(e.Hn)};d.prototype.nn=function(e){this.ia=Cx(this.C);var f=this.H[e.requestId];if(f)this.yc(f),f.Ik(e);else{var g,h;(h=(g=this.N).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,z,b);return c};var Ex;
var Fx=function(){Ex||(Ex=new xx);return Ex},wx=function(a){Tm(Vm(um.V.xc),function(){vc(a)})},Gx=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Hx=function(a){var b=a,c=oj.fa;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Ix=function(a){var b=Bn(wn.Zj);return b&&b[a]},Jx=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.O=!1;this.fa=null;this.initTime=c;this.C=15;this.N=this.Im(a);z.setTimeout(function(){f.initialize()},1E3);D(function(){f.xn(a,b,e)})};k=Jx.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),lf:this.initTime,yf:Math.round(pb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.ym(a,b,c)};k.getState=function(){return this.N.getState().state};k.xn=function(a,b,c){var d=z.location.origin,e=this,
f=tc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Gx(h):"",p;H(132)&&(p={sandbox:"allow-same-origin allow-scripts"});tc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.nn(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Im=function(a){var b=this,c=Dx(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{xk:function(){b.O=!0;b.H.H(c.getState(),c.stats)},yk:function(){},wk:function(d){b.O?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Kx(){var a=Vf(Sf.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Lx(a,b,c){c=c===void 0?!1:c;var d=z.location.origin;if(!d||!Kx())return;Lj()&&(a=""+d+Kj()+"/_/service_worker");var e=Hx(a);if(e===null||Ix(e.origin))return;if(!gc()){Fx().H(void 0,void 0,6);return}var f=new Jx(e,!!a,b||Math.round(pb()),Fx(),c),g;a:{var h=wn.Zj,m={},n=zn(h);if(!n){n=zn(h,!0);if(!n){g=void 0;break a}n.set(m)}g=n.get()}g[e.origin]=f;}
var Mx=function(a,b,c,d){var e;if((e=Ix(a))==null||!e.delegate){var f=gc()?16:6;Fx().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Ix(a).delegate(b,c,d);};
function Nx(a,b,c,d,e){var f=Hx();if(f===null){d(gc()?16:6);return}var g,h=(g=Ix(f.origin))==null?void 0:g.initTime,m=Math.round(pb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Mx(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ox(a,b,c,d){var e=Hx(a);if(e===null){d("_is_sw=f"+(gc()?16:6)+"te");return}var f=b?1:0,g=Math.round(pb()),h,m=(h=Ix(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0;Mx(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,sinceInit:n,attributionReporting:!0,referer:z.location.href}},function(){},function(p){var q="_is_sw=f"+p.failureType,r,t=(r=Ix(e.origin))==null?void 0:r.getState();t!==void 0&&(q+="s"+
t);d(n?q+("t"+n):q+"te")});};var Px="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Qx(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Rx(){var a=z.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Sx(){var a,b;return(b=(a=z.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Tx(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ux(){var a=z;if(!Tx(a))return null;var b=Qx(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Px).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Wx=function(a,b){if(a)for(var c=Vx(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;V(b,f,c[f])}},Vx=function(a){var b={};b[N.m.Be]=a.architecture;b[N.m.Ce]=a.bitness;a.fullVersionList&&(b[N.m.De]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[N.m.Ee]=a.mobile?"1":"0";b[N.m.Fe]=a.model;b[N.m.Ge]=a.platform;b[N.m.He]=a.platformVersion;b[N.m.Ie]=a.wow64?"1":"0";return b},Yx=function(a){var b=Xx.po,
c=function(g,h){try{a(g,h)}catch(m){}},d=Rx();if(d)c(d);else{var e=Sx();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=z.setTimeout(function(){c.nf||(c.nf=!0,O(106),c(null,Error("Timeout")))},b);e.then(function(g){c.nf||(c.nf=!0,O(104),z.clearTimeout(f),c(g))}).catch(function(g){c.nf||(c.nf=!0,O(105),z.clearTimeout(f),c(null,g))})}else c(null)}},$x=function(){if(Tx(z)&&(Zx=pb(),!Sx())){var a=Ux();a&&(a.then(function(){O(95)}),a.catch(function(){O(96)}))}},Zx;function ay(a){var b=a.location.href;if(a===a.top)return{url:b,Cn:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Cn:c}};function Sy(a,b){var c=!!Lj();switch(a){case 45:return c&&!H(75)?Kj()+"/g/ccm/collect":"https://www.google.com/ccm/collect";case 46:return c?Kj()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return c&&!H(79)?Kj()+"/travel/flights/click/conversion":"https://www.google.com/travel/flights/click/conversion";case 9:return!H(76)&&c?Kj()+"/pagead/viewthroughconversion":"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c&&!H(81)?(H(87)?Pn():
"").toLowerCase()==="region1"?""+Kj()+"/r1ag/g/c":""+Kj()+"/ag/g/c":Qy();case 16:return c?""+Kj()+(H(14)?"/ga/g/c":"/g/collect"):Ry();case 1:return!H(80)&&c?Kj()+"/activity;":"https://ad.doubleclick.net/activity;";case 2:return c?Kj()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return!H(80)&&c?Kj()+"/activity;register_conversion=1;":"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?H(78)?Kj()+"/d/pagead/form-data":Kj()+"/pagead/form-data":
H(140)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return!H(80)&&c?Kj()+"/activityi/"+b+";":"https://"+b+".fls.doubleclick.net/activityi;";case 5:case 6:case 7:case 8:case 12:case 13:case 14:case 15:case 18:case 19:case 20:case 21:case 22:case 23:case 24:case 25:case 26:case 27:case 28:case 29:case 30:case 31:case 32:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 42:case 43:case 44:case 47:case 48:case 49:case 50:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 59:case 0:throw Error("Unsupported endpoint");
default:Yb(a,"Unknown endpoint")}};function Ty(a){a=a===void 0?[]:a;return pj(a).join("~")}function Uy(){if(!H(117))return"";var a,b;return(((a=dm(em()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var bz={};bz.M=br.M;var cz={Go:"L",mm:"S",No:"Y",uo:"B",Do:"E",Fo:"I",Mo:"TC",Eo:"HTC"},dz={mm:"S",Co:"V",xo:"E",Lo:"tag"},ez={},fz=(ez[bz.M.Eh]="6",ez[bz.M.Fh]="5",ez[bz.M.Dh]="7",ez);function gz(){function a(c,d){var e=Xa(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var hz=!1;function xz(a){}
function yz(a){}function zz(){}
function Az(a){}function Bz(a){}
function Cz(a){}
function Dz(){}
function Ez(a,b){}
function Fz(a,b,c){}
function Gz(){};var Hz=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function Iz(a,b,c,d,e,f,g){var h=Object.assign({},Hz);c&&(h.body=c,h.method="POST");Object.assign(h,e);z.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});Jz(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():H(127)&&(b+="&_z=retryFetch",c?Bl(a,b,c):Al(a,b))})};var Kz=function(a){this.O=a;this.C=""},Lz=function(a,b){a.H=b;return a},Mz=function(a,b){a.N=b;return a},Jz=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}Nz(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},Oz=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};Nz(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},Nz=function(a,b){b&&(Pz(b.send_pixel,b.options,a.O),Pz(b.create_iframe,b.options,a.H),Pz(b.fetch,b.options,a.N))};function Qz(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Pz(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=Wc(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function yA(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function zA(a,b,c){c=c===void 0?!1:c;AA().addRestriction(0,a,b,c)}function BA(a,b,c){c=c===void 0?!1:c;AA().addRestriction(1,a,b,c)}function CA(){var a=bm();return AA().getRestrictions(1,a)}var DA=function(){this.container={};this.C={}},EA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
DA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=EA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
DA.prototype.getRestrictions=function(a,b){var c=EA(this,b);if(a===0){var d,e;return[].concat(sa((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),sa((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(sa((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),sa((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
DA.prototype.getExternalRestrictions=function(a,b){var c=EA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};DA.prototype.removeExternalRestrictions=function(a){var b=EA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function AA(){return Lo("r",function(){return new DA})};var FA=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),GA={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},HA={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},IA="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function JA(){var a=Rj("gtm.allowlist")||Rj("gtm.whitelist");a&&O(9);xj&&(a=["google","gtagfl","lcl","zone"],H(47)&&a.push("cmpPartners"));FA.test(z.location&&z.location.hostname)&&(xj?O(116):(O(117),KA&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&tb(mb(a),GA),c=Rj("gtm.blocklist")||Rj("gtm.blacklist");c||(c=Rj("tagTypeBlacklist"))&&O(3);c?O(8):c=[];FA.test(z.location&&z.location.hostname)&&(c=mb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));mb(c).indexOf("google")>=0&&O(2);var d=c&&tb(mb(c),HA),e={};return function(f){var g=f&&f[Pe.Aa];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=Hj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(H(47)&&xj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=gb(d,h||
[]);t&&O(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:H(47)&&xj&&h.indexOf("cmpPartners")>=0?!LA():b&&b.indexOf("sandboxedScripts")!==-1?0:gb(d,IA))&&(u=!0);return e[g]=u}}function LA(){var a=Vf(Sf.C,$l(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var KA=!1;KA=!0;
function MA(){Rl&&zA(bm(),function(a){var b=Df(a.entityId),c;if(Gf(b)){var d=b[Pe.Aa];if(!d)throw Error("Error: No function name given for function call.");var e=uf[d];c=!!e&&!!e.runInSiloedMode}else c=!!yA(b[Pe.Aa],4);return c})};function NA(a,b,c,d,e){if(!OA()){var f=d.siloed?Xl(a):a;if(!km(f)){d.loadExperiments=pj();mm(f,d,e);var g=PA(a),h=function(){Nl().container[f]&&(Nl().container[f].state=3);QA()},m={destinationId:f,endpoint:0};if(Lj())El(m,Kj()+"/"+g,void 0,h);else{var n=ub(a,"GTM-"),p=sk(),q=c?"/gtag/js":"/gtm.js",r=rk(b,q+g);if(!r){var t=rj.Ff+q;p&&ic&&n&&(t=ic.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=kv("https://","http://",t+g)}El(m,r,void 0,h)}}}}
function QA(){om()||ib(pm(),function(a,b){RA(a,b.transportUrl,b.context);O(92)})}
function RA(a,b,c,d){if(!OA()){var e=c.siloed?Xl(a):a;if(!lm(e))if(c.loadExperiments||(c.loadExperiments=pj()),om())Nl().destination[e]={state:0,transportUrl:b,context:c,parent:em()},Ml({ctid:e,isDestination:!0},d),O(91);else{c.siloed&&nm({ctid:e,isDestination:!0});Nl().destination[e]={state:1,context:c,parent:em()};Ml({ctid:e,isDestination:!0},d);var f={destinationId:e,endpoint:0};if(Lj())El(f,Kj()+("/gtd"+PA(a,!0)));else{var g="/gtag/destination"+PA(a,!0),h=rk(b,g);h||(h=kv("https://","http://",
rj.Ff+g));El(f,h)}}}}function PA(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);H(123)&&rj.vb==="dataLayer"||(c+="&l="+rj.vb);if(!ub(a,"GTM-")||b)c=H(129)?c+(Lj()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+$q();sk()&&(c+="&sign="+rj.yh);var d=oj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");H(69)&&Jj()&&(c+="&tag_exp="+Jj());return c}function OA(){if(Yq()){return!0}return!1};var SA=function(){this.H=0;this.C={}};SA.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Mb:c};return d};SA.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var UA=function(a,b){var c=[];ib(TA.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Mb===void 0||b.indexOf(e.Mb)>=0)&&c.push(e.listener)});return c};function VA(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:$l()}};var XA=function(a,b){this.C=!1;this.O=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;WA(this,a,b)},YA=function(a,b,c,d){if(tj.hasOwnProperty(b)||b==="__zone")return-1;var e={};Wc(d)&&(e=Xc(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},ZA=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},$A=function(a){if(!a.C){for(var b=a.O,c=0;c<b.length;c++)b[c]();a.C=!0;a.O.length=0}},WA=function(a,b,c){b!==void 0&&a.Se(b);c&&z.setTimeout(function(){$A(a)},
Number(c))};XA.prototype.Se=function(a){var b=this,c=rb(function(){D(function(){a($l(),b.eventData)})});this.C?c():this.O.push(c)};var aB=function(a){a.N++;return rb(function(){a.H++;a.R&&a.H>=a.N&&$A(a)})},bB=function(a){a.R=!0;a.H>=a.N&&$A(a)};var cB={};function dB(){return z[eB()]}
function eB(){return z.GoogleAnalyticsObject||"ga"}function hB(){var a=$l();}
function iB(a,b){return function(){var c=dB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var oB=["es","1"],pB={},qB={};function rB(a,b){if(Bk){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";pB[a]=[["e",c],["eid",a]];Qp(a)}}function sB(a){var b=a.eventId,c=a.hd;if(!pB[b])return[];var d=[];qB[b]||d.push(oB);d.push.apply(d,sa(pB[b]));c&&(qB[b]=!0);return d};var tB={},uB={},vB={};function wB(a,b,c,d){Bk&&H(119)&&((d===void 0?0:d)?(vB[b]=vB[b]||0,++vB[b]):c!==void 0?(uB[a]=uB[a]||{},uB[a][b]=Math.round(c)):(tB[a]=tB[a]||{},tB[a][b]=(tB[a][b]||0)+1))}function xB(a){var b=a.eventId,c=a.hd,d=tB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete tB[b];return e.length?[["md",e.join(".")]]:[]}
function yB(a){var b=a.eventId,c=a.hd,d=uB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete uB[b];return e.length?[["mtd",e.join(".")]]:[]}function zB(){for(var a=[],b=l(Object.keys(vB)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+vB[d])}return a.length?[["mec",a.join(".")]]:[]};var AB={},BB={};function CB(a,b,c){if(Bk&&b){var d=wk(b);AB[a]=AB[a]||[];AB[a].push(c+d);var e=(Gf(b)?"1":"2")+d;BB[a]=BB[a]||[];BB[a].push(e);Qp(a)}}function DB(a){var b=a.eventId,c=a.hd,d=[],e=AB[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=BB[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete AB[b],delete BB[b]);return d};function EB(a,b,c,d){var e=sf[a],f=FB(a,b,c,d);if(!f)return null;var g=Hf(e[Pe.bk],c,[]);if(g&&g.length){var h=g[0];f=EB(h.index,{onSuccess:f,onFailure:h.qk===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function FB(a,b,c,d){function e(){function w(){vn(3);var I=pb()-F;CB(c.id,f,"7");ZA(c.zc,C,"exception",I);H(108)&&Fz(c,f,bz.M.Dh);E||(E=!0,h())}if(f[Pe.gm])h();else{var x=Ff(f,c,[]),y=x[Pe.Wk];if(y!=null)for(var B=0;B<y.length;B++)if(!Ao(y[B])){h();return}var C=YA(c.zc,String(f[Pe.Aa]),Number(f[Pe.og]),x[Pe.METADATA]),E=!1;x.vtp_gtmOnSuccess=function(){if(!E){E=!0;var I=pb()-F;CB(c.id,sf[a],"5");ZA(c.zc,C,"success",I);H(108)&&Fz(c,f,bz.M.Fh);g()}};x.vtp_gtmOnFailure=function(){if(!E){E=!0;var I=pb()-
F;CB(c.id,sf[a],"6");ZA(c.zc,C,"failure",I);H(108)&&Fz(c,f,bz.M.Eh);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);CB(c.id,f,"1");H(108)&&Ez(c,f);var F=pb();try{If(x,{event:c,index:a,type:1})}catch(I){w(I)}H(108)&&Fz(c,f,bz.M.dk)}}var f=sf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Hf(f[Pe.ek],c,[]);if(n&&n.length){var p=n[0],q=EB(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.qk===
2?m:q}if(f[Pe.Tj]||f[Pe.im]){var r=f[Pe.Tj]?tf:c.jo,t=g,u=h;if(!r[a]){var v=GB(a,r,rb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function GB(a,b,c){var d=[],e=[];b[a]=HB(d,e,c);return{onSuccess:function(){b[a]=IB;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=JB;for(var f=0;f<e.length;f++)e[f]()}}}function HB(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function IB(a){a()}function JB(a,b){b()};var MB=function(a,b){for(var c=[],d=0;d<sf.length;d++)if(a[d]){var e=sf[d];var f=aB(b.zc);try{var g=EB(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[Pe.Aa];if(!h)throw Error("Error: No function name given for function call.");var m=uf[h];c.push({Nk:d,priorityOverride:(m?m.priorityOverride||0:0)||yA(e[Pe.Aa],1)||0,execute:g})}else KB(d,b),f()}catch(p){f()}}c.sort(LB);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function NB(a,b){if(!TA)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=UA(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=aB(b);try{d[e](a,f)}catch(g){f()}}return!0}function LB(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Nk,h=b.Nk;f=g>h?1:g<h?-1:0}return f}
function KB(a,b){if(Bk){var c=function(d){var e=b.isBlocked(sf[d])?"3":"4",f=Hf(sf[d][Pe.bk],b,[]);f&&f.length&&c(f[0].index);CB(b.id,sf[d],e);var g=Hf(sf[d][Pe.ek],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var OB=!1,TA;function PB(){TA||(TA=new SA);return TA}
function QB(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(H(108)){}if(d==="gtm.js"){if(OB)return!1;OB=!0}var e=!1,f=CA(),g=Xc(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}rB(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:RB(g,e),jo:[],logMacroError:function(){O(6);vn(0)},cachedModelValues:SB(),zc:new XA(function(){if(H(108)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};H(119)&&Bk&&(n.reportMacroDiscrepancy=wB);H(108)&&Bz(n.id);var p=Nf(n);H(108)&&Cz(n.id);e&&(p=TB(p));H(108)&&Az(b);var q=MB(p,n),r=NB(a,n.zc);bB(n.zc);d!=="gtm.js"&&d!=="gtm.sync"||hB();return UB(p,q)||r}function SB(){var a={};a.event=Wj("event",1);a.ecommerce=Wj("ecommerce",1);a.gtm=Wj("gtm");a.eventModel=Wj("eventModel");return a}
function RB(a,b){var c=JA();return function(d){if(c(d))return!0;var e=d&&d[Pe.Aa];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=bm();f=AA().getRestrictions(0,g);var h=a;b&&(h=Xc(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=Hj[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function TB(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(sf[c][Pe.Aa]);if(sj[d]||sf[c][Pe.jm]!==void 0||yA(d,2))b[c]=!0}return b}function UB(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&sf[c]&&!tj[String(sf[c][Pe.Aa])])return!0;return!1};function VB(){PB().addListener("gtm.init",function(a,b){oj.R=!0;fn();b()})};var WB=!1,XB=0,YB=[];function ZB(a){if(!WB){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){WB=!0;for(var e=0;e<YB.length;e++)D(YB[e])}YB.push=function(){for(var f=wa.apply(0,arguments),g=0;g<f.length;g++)D(f[g]);return 0}}}function $B(){if(!WB&&XB<140){XB++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");ZB()}catch(c){z.setTimeout($B,50)}}}
function aC(){WB=!1;XB=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")ZB();else{wc(A,"DOMContentLoaded",ZB);wc(A,"readystatechange",ZB);if(A.createEventObject&&A.documentElement.doScroll){var a=!0;try{a=!z.frameElement}catch(b){}a&&$B()}wc(z,"load",ZB)}}function bC(a){WB?a():YB.push(a)};var cC=0;var dC={},eC={};function fC(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={ui:void 0,Xh:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.ui=Uo(g,b),e.ui){var h=Sl?Sl:Zl();eb(h,function(r){return function(t){return r.ui.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=dC[g]||[];e.Xh={};m.forEach(function(r){return function(t){r.Xh[t]=!0}}(e));for(var n=Vl(),p=0;p<n.length;p++)if(e.Xh[n[p]]){c=c.concat(Yl());break}var q=eC[g]||[];q.length&&(c=c.concat(q))}}return{ji:c,Jn:d}}
function gC(a){ib(dC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function hC(a){ib(eC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var iC=!1,jC=!1;function kC(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=Xc(b,null),b[N.m.oe]&&(d.eventCallback=b[N.m.oe]),b[N.m.Uf]&&(d.eventTimeout=b[N.m.Uf]));return d}function lC(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Po()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function mC(a,b){var c=a&&a[N.m.Oc];c===void 0&&(c=Rj(N.m.Oc,2),c===void 0&&(c="default"));if(ab(c)||Array.isArray(c)){var d;d=b.isGtmEvent?ab(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=fC(d,b.isGtmEvent),f=e.ji,g=e.Jn;if(g.length)for(var h=nC(a),m=0;m<g.length;m++){var n=Uo(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=ub(p,"siloed_"))){var r=n.destinationId,t=Nl().destination[r];q=!!t&&t.state===0}q||RA(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{ji:Vo(f,b.isGtmEvent),zm:Vo(u,b.isGtmEvent)}}}var oC=void 0,pC=void 0;function qC(a,b,c){var d=Xc(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=Xc(b,null);Xc(c,e);Jv(Fv(Vl()[0],e),a.eventId,d)}function nC(a){for(var b=l([N.m.Pc,N.m.Yb]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Yp.C[d];if(e)return e}}
var rC={config:function(a,b){var c=lC(a,b);if(!(a.length<2)&&ab(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!Wc(a[2])||a.length>3)return;d=a[2]}var e=Uo(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Ql.Le){var m=dm(em());if(qm(m)){var n=m.parent,p=n.isDestination;h={Nn:dm(n),Gn:p};break a}}h=void 0}var q=h;q&&(f=q.Nn,g=q.Gn);rB(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Yl().indexOf(r)===-1:Vl().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[N.m.uc]){var u=nC(d);if(t)RA(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;oC?qC(b,v,oC):pC||(pC=Xc(v,null))}else NA(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var w;var x=d;pC?(qC(b,pC,x),w=!1):(!x[N.m.Rc]&&vj&&oC||(oC=Xc(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}Ck&&(cC===1&&(Zm.mcc=!1),cC=2);if(vj&&!t&&!d[N.m.Rc]){var y=jC;jC=!0;if(y)return}iC||O(43);if(!b.noTargetGroup)if(t){hC(e.id);
var B=e.id,C=d[N.m.Xf]||"default";C=String(C).split(",");for(var E=0;E<C.length;E++){var F=eC[C[E]]||[];eC[C[E]]=F;F.indexOf(B)<0&&F.push(B)}}else{gC(e.id);var I=e.id,L=d[N.m.Xf]||"default";L=L.toString().split(",");for(var U=0;U<L.length;U++){var K=dC[L[U]]||[];dC[L[U]]=K;K.indexOf(I)<0&&K.push(I)}}delete d[N.m.Xf];var Z=b.eventMetadata||{};Z.hasOwnProperty("is_external_event")||(Z.is_external_event=!b.fromContainerExecution);b.eventMetadata=Z;delete d[N.m.oe];for(var Y=t?[e.id]:Yl(),ha=0;ha<Y.length;ha++){var S=
d,R=Y[ha],ja=Xc(b,null),ia=Uo(R,ja.isGtmEvent);ia&&Yp.push("config",[S],ia,ja)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=lC(a,b),d=a[1],e={},f=Sn(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===N.m.Cf?Array.isArray(h)?NaN:Number(h):g===N.m.Nb?(Array.isArray(h)?h:[h]).map(Tn):Un(h)}b.fromContainerExecution||(e[N.m.U]&&O(139),e[N.m.Da]&&O(140));d==="default"?wo(e):d==="update"?yo(e,c):d==="declare"&&b.fromContainerExecution&&vo(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&ab(c)){var d=void 0;if(a.length>2){if(!Wc(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=kC(c,d),f=lC(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=mC(d,b);if(m){var n=m.ji,p=m.zm,q,r,t;if(!Rl&&H(107)){q=p.map(function(I){return I.id});r=p.map(function(I){return I.destinationId});t=n.map(function(I){return I.id});for(var u=l(Sl?Sl:Zl()),v=u.next();!v.done;v=u.next()){var w=v.value;
!ub(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(Xl(w))<0&&t.push(w)}}else q=n.map(function(I){return I.id}),r=n.map(function(I){return I.destinationId}),t=q;rB(g,c);for(var x=l(t),y=x.next();!y.done;y=x.next()){var B=y.value,C=Xc(b,null),E=Xc(d,null);delete E[N.m.oe];var F=C.eventMetadata||{};F.hasOwnProperty("is_external_event")||(F.is_external_event=!C.fromContainerExecution);F.send_to_targets=q.slice();F.send_to_destinations=r.slice();C.eventMetadata=F;Zp(c,E,B,C);Ck&&F.source_canonical_id===void 0&&
cC===0&&(bn("mcc","1"),cC=1)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[N.m.Oc]=q.join(","):delete e.eventModel[N.m.Oc];iC||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata.syn_or_mod&&(b.noGtmEvent=!0);e.eventModel[N.m.sc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&ab(a[1])&&ab(a[2])&&$a(a[3])){var c=Uo(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){iC||O(43);var f=nC();if(eb(Yl(),function(h){return c.destinationId===h})){lC(a,
b);var g={};Xc((g[N.m.Vb]=d,g[N.m.qc]=e,g),null);$p(d,function(h){D(function(){e(h)})},c.id,b)}else RA(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){iC=!0;var c=lC(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&ab(a[1])&&$a(a[2])){if(Tf(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=
a[2]($l(),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===2&&Wc(a[1])?c=Xc(a[1],null):a.length===3&&ab(a[1])&&(c={},Wc(a[2])||Array.isArray(a[2])?c[a[1]]=Xc(a[2],null):c[a[1]]=a[2]);if(c){var d=lC(a,b),e=d.eventId,f=d.priorityId;Xc(c,null);var g=Xc(c,null);Yp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},sC={policy:!0};var uC=function(a){if(tC(a))return a;this.value=a};uC.prototype.getUntrustedMessageValue=function(){return this.value};var tC=function(a){return!a||Uc(a)!=="object"||Wc(a)?!1:"getUntrustedMessageValue"in a};uC.prototype.getUntrustedMessageValue=uC.prototype.getUntrustedMessageValue;var vC=!1,wC=[];function xC(){if(!vC){vC=!0;for(var a=0;a<wC.length;a++)D(wC[a])}}function yC(a){vC?D(a):wC.push(a)};var zC=0,AC={},BC=[],CC=[],DC=!1,EC=!1;function FC(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function GC(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return HC(a)}function IC(a,b){if(!bb(b)||b<0)b=0;var c=Ko[rj.vb],d=0,e=!1,f=void 0;f=z.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(z.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function JC(a,b){var c=a._clear||b.overwriteModelFields;ib(a,function(e,f){e!=="_clear"&&(c&&Uj(e),Uj(e,f))});Ej||(Ej=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Po(),a["gtm.uniqueEventId"]=d,Uj("gtm.uniqueEventId",d));return QB(a)}function KC(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(jb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function LC(){var a;if(CC.length)a=CC.shift();else if(BC.length)a=BC.shift();else return;var b;var c=a;if(DC||!KC(c.message))b=c;else{DC=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Po(),f=Po(),c.message["gtm.uniqueEventId"]=Po());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};BC.unshift(n,c);b=h}return b}
function MC(){for(var a=!1,b;!EC&&(b=LC());){EC=!0;delete Oj.eventModel;Qj();var c=b,d=c.message,e=c.messageContext;if(d==null)EC=!1;else{e.fromContainerExecution&&Vj();try{if($a(d))try{d.call(Sj)}catch(u){}else if(Array.isArray(d)){if(ab(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Rj(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(jb(d))a:{if(d.length&&ab(d[0])){var p=rC[d[0]];if(p&&(!e.fromContainerExecution||!sC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=JC(n,e)||a)}}finally{e.fromContainerExecution&&Qj(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=AC[String(q)]||[],t=0;t<r.length;t++)CC.push(NC(r[t]));r.length&&CC.sort(FC);delete AC[String(q)];q>zC&&(zC=q)}EC=!1}}}return!a}
function OC(){if(H(108)){var a=!oj.N;}var c=MC();if(H(108)){}try{var e=$l(),f=z[rj.vb].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Mv(a){if(zC<a.notBeforeEventId){var b=String(a.notBeforeEventId);AC[b]=AC[b]||[];AC[b].push(a)}else CC.push(NC(a)),CC.sort(FC),D(function(){EC||MC()})}function NC(a){return{message:a.message,messageContext:a.messageContext}}
function PC(){function a(f){var g={};if(tC(f)){var h=f;f=tC(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=jc(rj.vb,[]),c=Oo();c.pruned===!0&&O(83);AC=Kv().get();Lv();bC(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});yC(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Ko.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new uC(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});BC.push.apply(BC,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return MC()&&p};var e=b.slice(0).map(function(f){return a(f)});BC.push.apply(BC,e);if(!oj.N){if(H(108)){}D(OC)}}var HC=function(a){return z[rj.vb].push(a)};function QC(a){HC(a)};function RC(){var a,b=lk(z.location.href);(a=b.hostname+b.pathname)&&bn("dl",encodeURIComponent(a));var c;var d=Wf.ctid;if(d){var e=Ql.Le?1:0,f,g=dm(em());f=g&&g.context;c=d+";"+Wf.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&bn("tdp",h);var m=gl(!0);m!==void 0&&bn("frm",String(m))};function SC(){H(54)&&Ck&&z.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){var b=zl(a.effectiveDirective);if(b){var c;var d=xl(b,a.blockedURI);c=d?vl[b][d]:void 0;var e;if(e=c)a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(q){}e=void 0}if(e){for(var h=l(c),m=h.next();!m.done;m=h.next()){var n=m.value;if(!n.Gk){n.Gk=!0;var p=String(n.endpoint);gn.hasOwnProperty(p)||(gn[p]=
!0,bn("csp",Object.keys(gn).join("~")))}}yl(b,a.blockedURI)}}}})};function TC(){var a;var b=cm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&bn("pcid",e)};var UC=/^(https?:)?\/\//;
function VC(){var a;var b=dm(em());if(b){for(;b.parent;){var c=dm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Lc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=l(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(UC,"")===g.replace(UC,""))){e=n;break a}}O(146)}else O(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
bn("rtg",String(d.canonicalContainerId)),bn("slo",String(t)),bn("hlo",d.htmlLoadOrder||"-1"),bn("lst",String(d.loadScriptType||"0")))}else O(144)};
function pD(){};var qD=function(){};qD.prototype.toString=function(){return"undefined"};var rD=new qD;
var tD=function(){Lo("rm",function(){return{}})[bm()]=function(a){if(sD.hasOwnProperty(a))return sD[a]}},wD=function(a,b,c){if(a instanceof uD){var d=a,e=d.resolve,f=b,g=String(Po());vD[g]=[f,c];a=e.call(d,g);b=Za}return{vn:a,onSuccess:b}},xD=function(a){var b=a?0:1;return function(c){O(a?134:135);var d=vD[c];if(d&&typeof d[b]==="function")d[b]();vD[c]=void 0}},uD=function(a){this.valueOf=this.toString;this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===rD?b:a[d]);return c.join("")}};
uD.prototype.toString=function(){return this.resolve("undefined")};var sD={},vD={};function yD(a,b){function c(g){var h=lk(g),m=fk(h,"protocol"),n=fk(h,"host",!0),p=fk(h,"port"),q=fk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function zD(a){return AD(a)?1:0}
function AD(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=Xc(a,{});Xc({arg1:c[d],any_of:void 0},e);if(zD(e))return!0}return!1}switch(a["function"]){case "_cn":return Eg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<zg.length;g++){var h=zg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Ag(b,c);case "_eq":return Fg(b,c);case "_ge":return Gg(b,c);case "_gt":return Ig(b,c);case "_lc":return Bg(b,c);case "_le":return Hg(b,
c);case "_lt":return Jg(b,c);case "_re":return Dg(b,c,a.ignore_case);case "_sw":return Kg(b,c);case "_um":return yD(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var BD=function(a,b,c,d){nq.call(this);this.mg=b;this.Me=c;this.yc=d;this.ab=new Map;this.ng=0;this.ia=new Map;this.Ea=new Map;this.R=void 0;this.H=a};qa(BD,nq);BD.prototype.N=function(){delete this.C;this.ab.clear();this.ia.clear();this.Ea.clear();this.R&&(jq(this.H,"message",this.R),delete this.R);delete this.H;delete this.yc;nq.prototype.N.call(this)};
var CD=function(a){if(a.C)return a.C;a.Me&&a.Me(a.H)?a.C=a.H:a.C=fl(a.H,a.mg);var b;return(b=a.C)!=null?b:null},ED=function(a,b,c){if(CD(a))if(a.C===a.H){var d=a.ab.get(b);d&&d(a.C,c)}else{var e=a.ia.get(b);if(e&&e.ii){DD(a);var f=++a.ng;a.Ea.set(f,{Eg:e.Eg,Lm:e.tk(c),persistent:b==="addEventListener"});a.C.postMessage(e.ii(c,f),"*")}}},DD=function(a){a.R||(a.R=function(b){try{var c;c=a.yc?a.yc(b):void 0;if(c){var d=c.Rn,e=a.Ea.get(d);if(e){e.persistent||a.Ea.delete(d);var f;(f=e.Eg)==null||f.call(e,
e.Lm,c.Pn)}}}catch(g){}},iq(a.H,"message",a.R))};var FD=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},GD=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},HD={tk:function(a){return a.listener},ii:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Eg:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},ID={tk:function(a){return a.listener},ii:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Eg:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function JD(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{Pn:b,Rn:b.__gppReturn.callId}}
var KD=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;nq.call(this);this.caller=new BD(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},JD);this.caller.ab.set("addEventListener",FD);this.caller.ia.set("addEventListener",HD);this.caller.ab.set("removeEventListener",GD);this.caller.ia.set("removeEventListener",ID);this.timeoutMs=c!=null?c:500};qa(KD,nq);KD.prototype.N=function(){this.caller.dispose();nq.prototype.N.call(this)};
KD.prototype.addEventListener=function(a){var b=this,c=Xk(function(){a(LD,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);ED(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(MD,!0);return}a(ND,!0)}}})};
KD.prototype.removeEventListener=function(a){ED(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var ND={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},LD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},MD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function OD(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){qu.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");qu.C=d}}function PD(){try{if(H(105)){var a=new KD(z,{timeoutMs:-1});CD(a.caller)&&a.addEventListener(OD)}}catch(b){}};function QD(){var a;a=a===void 0?"":a;var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(1))?String(data.blob[1]):a};function RD(){var a=[["cv",H(139)?QD():"8"],["rv",rj.xh],["tc",sf.filter(function(b){return b}).length]];rj.wh&&a.push(["x",rj.wh]);Jj()&&a.push(["tag_exp",Jj()]);return a};var SD={},TD={};function UD(a){var b=a.eventId,c=a.hd,d=[],e=SD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=TD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete SD[b],delete TD[b]);return d};function VD(){return!1}function WD(){var a={};return function(b,c,d){}};function XD(){var a=YD;return function(b,c,d){var e=d&&d.event;ZD(c);var f=ph(b)?void 0:1,g=new Ma;ib(c,function(r,t){var u=nd(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.C.C.H=Lf();var h={jk:$f(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Se:e!==void 0?function(r){e.zc.Se(r)}:void 0,qb:function(){return b},log:function(){},Tm:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},Zn:!!yA(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(VD()){var m=WD(),n,p;h.fb={Di:[],Te:{},Ib:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Dg:Hh()};h.log=function(r){var t=wa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Je(a,h,[b,g]);a.C.C.H=void 0;q instanceof ya&&(q.type==="return"?q=q.data:q=void 0);return md(q,void 0,f)}}function ZD(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;$a(b)&&(a.gtmOnSuccess=function(){D(b)});$a(c)&&(a.gtmOnFailure=function(){D(c)})};function $D(a){}$D.K="internal.addAdsClickIds";function aE(a,b){var c=this;}aE.publicName="addConsentListener";var bE=!1;function cE(a){for(var b=0;b<a.length;++b)if(bE)try{a[b]()}catch(c){O(77)}else a[b]()}function dE(a,b,c){var d=this,e;return e}dE.K="internal.addDataLayerEventListener";function eE(a,b,c){}eE.publicName="addDocumentEventListener";function fE(a,b,c,d){}fE.publicName="addElementEventListener";function gE(a){return a.J.C};function hE(a){}hE.publicName="addEventCallback";
var iE=function(a){return typeof a==="string"?a:String(Po())},lE=function(a,b){jE(a,"init",!1)||(kE(a,"init",!0),b())},jE=function(a,b,c){var d=mE(a);return qb(d,b,c)},nE=function(a,b,c,d){var e=mE(a),f=qb(e,b,d);e[b]=c(f)},kE=function(a,b,c){mE(a)[b]=c},mE=function(a){var b=Lo("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},oE=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Ic(a,"className"),"gtm.elementId":a.for||yc(a,"id")||"","gtm.elementTarget":a.formTarget||
Ic(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Ic(a,"href")||a.src||a.code||a.codebase||"";return d};
function xE(a){}xE.K="internal.addFormAbandonmentListener";function yE(a,b,c,d){}
yE.K="internal.addFormData";var zE={},AE=[],BE={},CE=0,DE=0;
function KE(a,b){}KE.K="internal.addFormInteractionListener";
function RE(a,b){}RE.K="internal.addFormSubmitListener";
function WE(a){}WE.K="internal.addGaSendListener";function XE(a){if(!a)return{};var b=a.Tm;return VA(b.type,b.index,b.name)}function YE(a){return a?{originatingEntity:XE(a)}:{}};
var $E=function(a,b,c){ZE().updateZone(a,b,c)},bF=function(a,b,c,d,e,f){var g=ZE();c=c&&tb(c,aF);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,$l(),h)){var p=n,q=a,r=d,t=e,u=f;if(ub(p,"GTM-"))NA(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Ev("js",ob());NA(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};H(145)||Jv(v,q,w);Jv(Fv(p,r),q,w)}}}return h},ZE=function(){return Lo("zones",function(){return new cF})},
dF={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},aF={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},cF=function(){this.C={};this.H={};this.N=0};k=cF.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.si],b))return!1;for(var e=0;e<c.Bf.length;e++)if(this.H[c.Bf[e]].Kd(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.Bf.length;f++){var g=this.H[c.Bf[f]];g.Kd(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.si],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].N(m,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.N);this.H[c]=new eF(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.O(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&Ko[a]||!d&&km(a)||d&&d.si!==b)return!1;if(d)return d.Bf.push(c),!1;this.C[a]={si:b,Bf:[c]};return!0};var eF=function(a,b){this.H=null;this.C=[{eventId:a,Kd:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};eF.prototype.O=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.Kd!==b&&this.C.push({eventId:a,Kd:b})};eF.prototype.Kd=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].Kd;return!1};eF.prototype.N=function(a,b){b=b||[];if(!this.H||dF[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function fF(a){var b=Ko.zones;return b?b.getIsAllowedFn(Vl(),a):function(){return!0}}function gF(){var a=Ko.zones;a&&a.unregisterChild(Vl())}
function hF(){BA(bm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Ko.zones;return c?c.isActive(Vl(),b):!0});zA(bm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return fF(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var iF=function(a,b){this.tagId=a;this.Ve=b};
function jF(a,b){var c=this,d=void 0;if(!ah(a)||!Ug(b)&&!Wg(b))throw J(this.getName(),["string","Object|undefined"],arguments);var e=md(b,this.J,1)||{},f=e.firstPartyUrl,g=e.onLoad,h=e.loadByDestination===!0,m=e.isGtmEvent===!0,n=e.siloed===!0;d=n?Xl(a):a;cE([function(){M(c,"load_google_tags",a,f)}]);if(h){if(lm(a))return d}else if(km(a))return d;var p=6,q=gE(this);m&&(p=7);q.qb()==="__zone"&&(p=1);var r={source:p,fromContainerExecution:!0,
siloed:n},t=function(u){zA(u,function(v){for(var w=AA().getExternalRestrictions(0,bm()),x=l(w),y=x.next();!y.done;y=x.next()){var B=y.value;if(!B(v))return!1}return!0},!0);BA(u,function(v){for(var w=AA().getExternalRestrictions(1,bm()),x=l(w),y=x.next();!y.done;y=x.next()){var B=y.value;if(!B(v))return!1}return!0},!0);g&&g(new iF(a,u))};h?RA(a,f,r,t):NA(a,f,!ub(a,"GTM-"),r,t);g&&q.qb()==="__zone"&&bF(Number.MIN_SAFE_INTEGER,[a],null,{},XE(gE(this)));
return d}jF.K="internal.loadGoogleTag";function kF(a){return new ed("",function(b){var c=this.evaluate(b);if(c instanceof ed)return new ed("",function(){var d=wa.apply(0,arguments),e=this,f=Xc(gE(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Fa(this.J);h.C=f;return c.kb.apply(c,[h].concat(sa(g)))})})};function lF(a,b,c){var d=this;}lF.K="internal.addGoogleTagRestriction";var mF={},nF=[];
function uF(a,b){}
uF.K="internal.addHistoryChangeListener";function vF(a,b,c){}vF.publicName="addWindowEventListener";function wF(a,b){return!0}wF.publicName="aliasInWindow";function xF(a,b,c){}xF.K="internal.appendRemoteConfigParameter";function yF(a){var b;return b}
yF.publicName="callInWindow";function zF(a){}zF.publicName="callLater";function AF(a){}AF.K="callOnDomReady";function BF(a){}BF.K="callOnWindowLoad";function CF(a,b){var c;return c}CF.K="internal.computeGtmParameter";function DF(a,b){var c=this;}DF.K="internal.consentScheduleFirstTry";function EF(a,b){var c=this;}EF.K="internal.consentScheduleRetry";function FF(a){var b;return b}FF.K="internal.copyFromCrossContainerData";function GF(a,b){var c;if(!ah(a)||!fh(b)&&b!==null&&!Wg(b))throw J(this.getName(),["string","number|undefined"],arguments);M(this,"read_data_layer",a);c=(b||2)!==2?Rj(a,1):Tj(a,[z,A]);var d=nd(c,this.J,ph(gE(this).qb())?2:1);d===void 0&&c!==void 0&&O(45);return d}GF.publicName="copyFromDataLayer";
function HF(a){var b=void 0;M(this,"read_data_layer",a);a=String(a);var c;a:{for(var d=gE(this).cachedModelValues,e=l(a.split(".")),f=e.next();!f.done;f=e.next()){if(d==null){c=void 0;break a}d=d[f.value]}c=d}b=nd(c,this.J,1);return b}HF.K="internal.copyFromDataLayerCache";function IF(a){var b;return b}IF.publicName="copyFromWindow";function JF(a){var b=void 0;return nd(b,this.J,1)}JF.K="internal.copyKeyFromWindow";var KF=function(a){this.C=a},LF=function(a,b,c,d){var e;return(e=a.C[b])!=null&&e[c]?a.C[b][c].some(function(f){return f(d)}):!1},MF=function(a){return a===um.V.xa&&Nm[a]===tm.Ba.Dd&&!Ao(N.m.T)};var NF=function(){return"0"},OF=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];H(101)&&b.push("gbraid");return mk(a,b,"0")};var PF={},QF={},RF={},SF={},TF={},UF={},VF={},WF={},XF={},YF={},ZF={},$F={},aG={},bG={},cG={},dG={},eG={},fG={},gG={},hG={},iG={},jG={},kG={},lG={},mG={},nG={},oG=(nG[N.m.Ia]=(PF[2]=[MF],PF),nG[N.m.ze]=(QF[2]=[MF],QF),nG[N.m.pe]=(RF[2]=[MF],RF),nG[N.m.kh]=(SF[2]=[MF],SF),nG[N.m.lh]=(TF[2]=[MF],TF),nG[N.m.mh]=(UF[2]=[MF],UF),nG[N.m.nh]=(VF[2]=[MF],VF),nG[N.m.oh]=(WF[2]=[MF],WF),nG[N.m.Cb]=(XF[2]=[MF],XF),nG[N.m.Be]=(YF[2]=[MF],YF),nG[N.m.Ce]=(ZF[2]=[MF],ZF),nG[N.m.De]=($F[2]=[MF],$F),nG[N.m.Ee]=(aG[2]=
[MF],aG),nG[N.m.Fe]=(bG[2]=[MF],bG),nG[N.m.Ge]=(cG[2]=[MF],cG),nG[N.m.He]=(dG[2]=[MF],dG),nG[N.m.Ie]=(eG[2]=[MF],eG),nG[N.m.Wa]=(fG[1]=[MF],fG),nG[N.m.Ec]=(gG[1]=[MF],gG),nG[N.m.Ic]=(hG[1]=[MF],hG),nG[N.m.sd]=(iG[1]=[MF],iG),nG[N.m.Yd]=(jG[1]=[function(a){return H(101)&&MF(a)}],jG),nG[N.m.Jc]=(kG[1]=[MF],kG),nG[N.m.sa]=(lG[1]=[MF],lG),nG[N.m.La]=(mG[1]=[MF],mG),nG),pG={},qG=(pG[N.m.Wa]=NF,pG[N.m.Ec]=NF,pG[N.m.Ic]=NF,pG[N.m.sd]=NF,pG[N.m.Yd]=NF,pG[N.m.Jc]=function(a){if(!Wc(a))return{};var b=Xc(a,
null);delete b.match_id;return b},pG[N.m.sa]=OF,pG[N.m.La]=OF,pG),rG={},sG={},tG=(sG.user_data=(rG[2]=[MF],rG),sG),uG={};var vG=function(a,b){this.conditions=a;this.C=b},wG=function(a,b,c,d){return LF(a.conditions,b,2,d)?{status:2}:LF(a.conditions,b,1,d)?a.C[b]===void 0?{status:2}:{status:1,value:a.C[b](c,d)}:{status:0,value:c}},xG=new vG(new KF(oG),qG),yG=new vG(new KF(tG),uG);function zG(a,b,c){return wG(xG,a,b,c)}function AG(a,b,c){return wG(yG,a,b,c)}var BG=function(a,b,c,d){this.C=a;this.N=b;this.O=c;this.R=d};
BG.prototype.getValue=function(a){a=a===void 0?um.V.pb:a;if(!this.N.some(function(b){return b(a)}))return this.O.some(function(b){return b(a)})?this.R(this.C):this.C};BG.prototype.H=function(){return Uc(this.C)==="array"||Wc(this.C)?Xc(this.C,null):this.C};var CG=function(){},DG=function(a,b){this.conditions=a;this.C=b},EG=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new BG(c,e,g,a.C[b]||CG)},FG,GG;function HG(a,b,c,d,e){if(b===void 0)c[a]=b;else{var f=d(a,b,e);f.status===2?delete c[a]:c[a]=f.value}}
var IG=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;if(H(56)){this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;T(this,g,d[g])}}else this.metadata=Xc(c.eventMetadata||{},{})},Gu=function(a,b){if(H(56)){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,Q(a,"transmission_type"))}return a.C[b]},V=function(a,b,c){if(H(56)){var d=a.C,e;c===void 0?e=void 0:(FG!=null||(FG=new DG(oG,
qG)),e=EG(FG,b,c));d[b]=e}else HG(b,c,a.C,zG,Q(a,"transmission_type"))},JG=function(a,b){b=b===void 0?{}:b;if(H(56)){for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b}return Xc(a.C,b)};IG.prototype.copyToHitData=function(a,b,c){var d=P(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&ab(d)&&H(89))try{d=c(d)}catch(e){}d!==void 0&&V(this,a,d)};
var Q=function(a,b){if(H(56)){var c=a.metadata[b];if(b==="transmission_type"){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,Q(a,"transmission_type"))}return a.metadata[b]},T=function(a,b,c){if(H(56)){var d=a.metadata,e;c===void 0?e=void 0:(GG!=null||(GG=new DG(tG,uG)),e=EG(GG,b,c));d[b]=e}else if(HG(b,c,a.metadata,AG,Q(a,"transmission_type")),b==="transmission_type"){for(var f=l(Object.keys(a.metadata)),g=f.next();!g.done;g=
f.next()){var h=g.value;h!=="transmission_type"&&T(a,h,Q(a,h))}for(var m=l(Object.keys(a.C)),n=m.next();!n.done;n=m.next()){var p=n.value;V(a,p,Gu(a,p))}}},KG=function(a,b){b=b===void 0?{}:b;if(H(56)){for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b}return Xc(a.metadata,b)},$u=function(a,b,c){var d=a.target.destinationId;Rl||(d=fm(d));var e=Qv(d);return e&&e[b]!==
void 0?e[b]:c};function LG(a,b){var c;return c}LG.K="internal.copyPreHit";function MG(a,b){var c=null;if(!ah(a)||!ah(b))throw J(this.getName(),["string","string"],arguments);M(this,"access_globals","readwrite",a);M(this,"access_globals","readwrite",b);var d=[z,A],e=a.split("."),f=vb(e,d),g=e[e.length-1];if(f===void 0)throw Error("Path "+a+" does not exist.");var h=f[g];if(h)return $a(h)?nd(h,this.J,2):null;var m;h=function(){if(!$a(m.push))throw Error("Object at "+b+" in window is not an array.");m.push.call(m,
arguments)};f[g]=h;var n=b.split("."),p=vb(n,d),q=n[n.length-1];if(p===void 0)throw Error("Path "+n+" does not exist.");m=p[q];m===void 0&&(m=[],p[q]=m);c=function(){h.apply(h,Array.prototype.slice.call(arguments,0))};return nd(c,this.J,2)}MG.publicName="createArgumentsQueue";function NG(a){return nd(function(c){var d=dB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
dB(),n=m&&m.getByName&&m.getByName(f);return(new z.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}NG.K="internal.createGaCommandQueue";function OG(a){return nd(function(){if(!$a(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
ph(gE(this).qb())?2:1)}OG.publicName="createQueue";function PG(a,b){var c=null;if(!ah(a)||!bh(b))throw J(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new jd(new RegExp(a,d))}catch(e){}return c}PG.K="internal.createRegex";function QG(){var a={};return a};function RG(a){}RG.K="internal.declareConsentState";function SG(a){var b="";return b}SG.K="internal.decodeUrlHtmlEntities";function TG(a,b,c){var d;return d}TG.K="internal.decorateUrlWithGaCookies";function UG(){}UG.K="internal.deferCustomEvents";function VG(a){var b;return b}VG.K="internal.detectUserProvidedData";
var YG=function(a){var b=Bc(a,["button","input"],50);if(!b)return null;var c=String(b.tagName).toLowerCase();if(c==="button")return b;if(c==="input"){var d=yc(b,"type");if(d==="button"||d==="submit"||d==="image"||d==="file"||d==="reset")return b}return null},ZG=function(a,b,c){var d=c.target;if(d){var e=jE(a,"individualElementIds",[]);if(e.length>0){var f=oE(d,b,e);HC(f)}var g=!1,h=jE(a,"commonButtonIds",[]);if(h.length>0){var m=YG(d);if(m){var n=oE(m,b,h);HC(n);g=!0}}var p=jE(a,"selectorToTriggerIds",
{}),q;for(q in p)if(p.hasOwnProperty(q)){var r=g?p[q].filter(function(v){return h.indexOf(v)===-1}):p[q];if(r.length!==0){var t=si(d,q);if(t){var u=oE(t,b,r);HC(u)}}}}};
function $G(a,b){if(!Vg(a))throw J(this.getName(),["Object|undefined","any"],arguments);var c=a?md(a):{},d=lb(c.matchCommonButtons),e=!!c.cssSelector,f=iE(b);M(this,"detect_click_events",c.matchCommonButtons,c.cssSelector);var g=c.useV2EventName?"gtm.click-v2":"gtm.click",h=c.useV2EventName?"ecl":"cl",m=function(p){p.push(f);return p};if(e||d){if(d&&nE(h,"commonButtonIds",m,[]),e){var n=nb(String(c.cssSelector));nE(h,"selectorToTriggerIds",
function(p){p.hasOwnProperty(n)||(p[n]=[]);m(p[n]);return p},{})}}else nE(h,"individualElementIds",m,[]);lE(h,function(){wc(A,"click",function(p){ZG(h,g,p)},!0)});return f}$G.K="internal.enableAutoEventOnClick";
function hH(a,b){return p}hH.K="internal.enableAutoEventOnElementVisibility";function iH(){}iH.K="internal.enableAutoEventOnError";var jH={},kH=[],lH={},mH=0,nH=0;
function tH(a,b){var c=this;return d}tH.K="internal.enableAutoEventOnFormInteraction";
function yH(a,b){var c=this;return f}yH.K="internal.enableAutoEventOnFormSubmit";
function DH(){var a=this;}DH.K="internal.enableAutoEventOnGaSend";var EH={},FH=[];
var HH=function(a,b){var c=""+b;if(EH[c])EH[c].push(a);else{var d=[a];EH[c]=d;var e=GH("gtm.historyChange-v2"),f=-1;FH.push(function(g){f>=0&&z.clearTimeout(f);b?f=z.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},GH=function(a){var b=z.location.href,c={source:null,state:z.history.state||null,url:ik(lk(b)),Sa:fk(lk(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.Sa!==d.Sa){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.Sa,
"gtm.newUrlFragment":d.Sa,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;HC(h)}}},IH=function(a,b){var c=z.history,d=c[a];if($a(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=z.location.href;b({source:a,state:e,url:ik(lk(h)),Sa:fk(lk(h),"fragment")})}}catch(e){}},KH=function(a){z.addEventListener("popstate",function(b){var c=JH(b);a({source:"popstate",state:b.state,url:ik(lk(c)),Sa:fk(lk(c),
"fragment")})})},LH=function(a){z.addEventListener("hashchange",function(b){var c=JH(b);a({source:"hashchange",state:null,url:ik(lk(c)),Sa:fk(lk(c),"fragment")})})},JH=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||z.location.href};
function MH(a,b){var c=this;if(!Vg(a))throw J(this.getName(),["Object|undefined","any"],arguments);cE([function(){M(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!jE(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<FH.length;n++)FH[n](m)},f=iE(b),HH(f,e),kE(d,"reg",HH)):g=GH("gtm.historyChange");LH(g);KH(g);IH("pushState",
g);IH("replaceState",g);kE(d,"init",!0)}else if(d==="ehl"){var h=jE(d,"reg");h&&(f=iE(b),h(f,e))}d==="hl"&&(f=void 0);return f}MH.K="internal.enableAutoEventOnHistoryChange";var NH=["http://","https://","javascript:","file://"];
var OH=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Ic(b,"href");if(c.indexOf(":")!==-1&&!NH.some(function(h){return ub(c,h)}))return!1;var d=c.indexOf("#"),e=Ic(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=ik(lk(c)),g=ik(lk(z.location.href));return f!==g}return!0},PH=function(a,b){for(var c=fk(lk((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Ic(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},QH=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Bc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=jE("lcl",e?"nv.mwt":"mwt",0),g;g=e?jE("lcl","nv.ids",[]):jE("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=jE("lcl","aff.map",{})[n];p&&!PH(p,d)||h.push(n)}if(h.length){var q=OH(c,d),r=oE(d,"gtm.linkClick",
h);r["gtm.elementText"]=zc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!eb(String(Ic(d,"rel")||"").split(" "),function(x){return x.toLowerCase()==="noreferrer"}),u=z[(Ic(d,"target")||"_self").substring(1)],v=!0,w=IC(function(){var x;if(x=v&&u){var y;a:if(t){var B;try{B=new MouseEvent(c.type,{bubbles:!0})}catch(C){if(!A.createEvent){y=!1;break a}B=A.createEvent("MouseEvents");B.initEvent(c.type,!0,!0)}B.C=!0;c.target.dispatchEvent(B);y=!0}else y=!1;x=!y}x&&(u.location.href=Ic(d,
"href"))},f);if(GC(r,w,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else GC(r,function(){},f||2E3);return!0}}}var b=0;wc(A,"click",a,!1);wc(A,"auxclick",a,!1)};
function RH(a,b){var c=this;if(!Vg(a))throw J(this.getName(),["Object|undefined","any"],arguments);var d=md(a);cE([function(){M(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=iE(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};nE("lcl","mwt",n,0);f||nE("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};nE("lcl","ids",p,[]);f||nE("lcl","nv.ids",p,[]);g&&nE("lcl","aff.map",function(q){q[h]=g;return q},{});jE("lcl","init",!1)||(QH(),kE("lcl","init",!0));return h}RH.K="internal.enableAutoEventOnLinkClick";var SH,TH;
function dI(a,b){var c=this;return d}dI.K="internal.enableAutoEventOnScroll";function eI(a){return function(){if(a.limit&&a.mi>=a.limit)a.Ag&&z.clearInterval(a.Ag);else{a.mi++;var b=pb();HC({event:a.eventName,"gtm.timerId":a.Ag,"gtm.timerEventNumber":a.mi,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Mk,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Mk,"gtm.triggers":a.oo})}}}
function fI(a,b){
return f}fI.K="internal.enableAutoEventOnTimer";var $b=ua(["data-gtm-yt-inspected-"]),hI=["www.youtube.com","www.youtube-nocookie.com"],iI,jI=!1;
function tI(a,b){var c=this;return e}tI.K="internal.enableAutoEventOnYouTubeActivity";jI=!1;function uI(a,b){if(!ah(a)||!Vg(b))throw J(this.getName(),["string","Object|undefined"],arguments);var c=b?md(b):{},d=a,e=!1;return e}uI.K="internal.evaluateBooleanExpression";var vI;function wI(a){var b=!1;return b}wI.K="internal.evaluateMatchingRules";function fJ(){return Hq(7)&&Hq(9)&&Hq(10)};function kK(a,b,c,d){}kK.K="internal.executeEventProcessor";function lK(a){var b;return nd(b,this.J,1)}lK.K="internal.executeJavascriptString";function mK(a){var b;return b};function nK(a){var b={};return nd(b)}nK.K="internal.getAdsCookieWritingOptions";function oK(a,b){var c=!1;return c}oK.K="internal.getAllowAdPersonalization";function pK(a,b){b=b===void 0?!0:b;var c;return c}pK.K="internal.getAuid";var qK=null;
function rK(){var a=new Ma;M(this,"read_container_data"),H(48)&&qK?a=qK:(a.set("containerId",'GTM-N2764VHX'),a.set("version",'8'),a.set("environmentName",''),a.set("debugMode",ag),a.set("previewMode",bg.Ok),a.set("environmentMode",bg.Pm),a.set("firstPartyServing",Lj()||zj),a.set("containerUrl",ic),a.Qa(),H(48)&&(qK=a));return a}
rK.publicName="getContainerVersion";function sK(a,b){b=b===void 0?!0:b;var c;return c}sK.publicName="getCookieValues";function tK(){var a="";return a}tK.K="internal.getCorePlatformServicesParam";function uK(){return Ln()}uK.K="internal.getCountryCode";function vK(){var a=[];return nd(a)}vK.K="internal.getDestinationIds";function wK(a){var b=new Ma;return b}wK.K="internal.getDeveloperIds";function xK(a,b){var c=null;if(!$g(a)||!ah(b))throw J(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementAttribute requires an HTML Element.");M(this,"get_element_attributes",d,b);c=yc(d,b);return c}xK.K="internal.getElementAttribute";function yK(a){var b=null;M(this,"read_dom_elements","id",a);var c=A.getElementById(a);if(c===null)return c;b=new jd(c);return b}yK.K="internal.getElementById";function zK(a){var b="";if(!$g(a))throw J(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementInnerText requires an HTML Element.");M(this,"read_dom_element_text",c);b=zc(c);return b}zK.K="internal.getElementInnerText";function AK(a,b){var c=null;if(!$g(a)||!ah(b))throw J(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementProperty requires an HTML element.");M(this,"access_dom_element_properties",d,"read",b);c=d[b];return nd(c)}AK.K="internal.getElementProperty";function BK(a){var b;if(!$g(a))throw J(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementValue requires an HTML Element.");M(this,"access_element_values",c,"read");b=c instanceof HTMLInputElement?c.value:yc(c,"value")||"";return b}BK.K="internal.getElementValue";function CK(a){var b=0;if(!$g(a))throw J(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementVisibilityRatio requires an HTML Element.");M(this,"read_document_dimensions");M(this,"read_document_visibility_state");M(this,"read_element_style",c,"visibility");M(this,"read_element_style",c,"display");M(this,"read_element_style",c,"opacity");M(this,"read_element_style",
c,"filter");M(this,"read_element_dimensions",c);Sv(c)||(b=Uv(c));return b}CK.K="internal.getElementVisibilityRatio";function DK(a){var b=null;if(!ah(a))throw J(this.getName(),["string"],arguments);M(this,"read_dom_elements","css",a);b=new ad;var c;try{c=qi(a)}catch(e){return null}if(c===null)return b;for(var d=0;d<c.length;d++)b.set(d,new jd(c[d]));return b}DK.K="internal.getElementsByCssSelector";
function EK(a){var b;if(!ah(a))throw J(this.getName(),["string"],arguments);M(this,"read_event_data",a);var c;a:{var d=a,e=gE(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",y=l(n),B=y.next();!B.done;B=
y.next()){var C=B.value;C===m?(w.push(x),x=""):x=C===g?x+"\\":C===h?x+".":x+C}x&&w.push(x);for(var E=l(w),F=E.next();!F.done;F=E.next()){if(f==null){c=void 0;break a}f=f[F.value]}c=f}else c=void 0}b=nd(c,this.J,1);return b}EK.K="internal.getEventData";var FK={};FK.enableAWFledge=H(33);FK.enableAdsConversionValidation=H(17);FK.enableAdsSupernovaParams=H(29);FK.enableAutoPhoneAndAddressDetection=H(31);FK.enableAutoPiiOnPhoneAndAddress=H(32);FK.enableCachedEcommerceData=H(39);FK.enableCcdSendTo=H(40);FK.enableCloudRecommentationsErrorLogging=H(41);FK.enableCloudRecommentationsSchemaIngestion=H(42);FK.enableCloudRetailInjectPurchaseMetadata=H(44);FK.enableCloudRetailLogging=H(43);FK.enableCloudRetailPageCategories=H(45);FK.enableDCFledge=H(55);
FK.enableDataLayerSearchExperiment=H(128);FK.enableDecodeUri=H(89);FK.enableDeferAllEnhancedMeasurement=H(57);FK.enableFormSkipValidation=H(74);FK.enableGa4OutboundClicksFix=H(93);FK.enableGaAdsConversions=H(121);FK.enableGaAdsConversionsClientId=H(120);FK.enableGppForAds=H(105);FK.enableMerchantRenameForBasketData=H(112);FK.enableUrlDecodeEventUsage=H(138);FK.enableZoneConfigInChildContainers=H(141);FK.useEnableAutoEventOnFormApis=H(155);function GK(){return nd(FK)}GK.K="internal.getFlags";function HK(){return new jd(rD)}HK.K="internal.getHtmlId";function IK(a){var b;return b}IK.K="internal.getIframingState";function JK(a,b){var c={};return nd(c)}JK.K="internal.getLinkerValueFromLocation";function KK(){var a=new Ma;return a}KK.K="internal.getPrivacyStrings";function LK(a,b){var c;return c}LK.K="internal.getProductSettingsParameter";function MK(a,b){var c;return c}MK.publicName="getQueryParameters";function NK(a,b){var c;return c}NK.publicName="getReferrerQueryParameters";function OK(a){var b="";if(!bh(a))throw J(this.getName(),["string|undefined"],arguments);M(this,"get_referrer",a);b=hk(lk(A.referrer),a);return b}OK.publicName="getReferrerUrl";function PK(){return Mn()}PK.K="internal.getRegionCode";function QK(a,b){var c;return c}QK.K="internal.getRemoteConfigParameter";function RK(){var a=new Ma;a.set("width",0);a.set("height",0);return a}RK.K="internal.getScreenDimensions";function SK(){var a="";return a}SK.K="internal.getTopSameDomainUrl";function TK(){var a="";return a}TK.K="internal.getTopWindowUrl";function UK(a){var b="";if(!bh(a))throw J(this.getName(),["string|undefined"],arguments);M(this,"get_url",a);b=fk(lk(z.location.href),a);return b}UK.publicName="getUrl";function VK(){M(this,"get_user_agent");return fc.userAgent}VK.K="internal.getUserAgent";function WK(){var a;return a?nd(Vx(a)):a}WK.K="internal.getUserAgentClientHints";function dL(){return z.gaGlobal=z.gaGlobal||{}}function eL(){var a=dL();a.hid=a.hid||fb();return a.hid}function fL(a,b){var c=dL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};function FL(a){(ox(a)||Lj())&&V(a,N.m.Ij,Mn()||Ln());!ox(a)&&Lj()&&V(a,N.m.Rj,"::")}function GL(a){if(H(77)&&Lj()){Uu(a);Vu(a,"cpf",Vn(P(a.D,N.m.Ta)));var b=P(a.D,N.m.oc);Vu(a,"cu",b===!0?1:b===!1?0:void 0);Vu(a,"cf",Vn(P(a.D,N.m.ib)));Vu(a,"cd",yr(Un(P(a.D,N.m.Xa)),Un(P(a.D,N.m.yb))))}};var bM={AW:wn.Uk,G:wn.Yl,DC:wn.Xl};function cM(a){var b=Gi(a);return""+ar(b.map(function(c){return c.value}).join("!"))}function dM(a){var b=Uo(a);return b&&bM[b.prefix]}function eM(a,b){var c=a[b];c&&(c.clearTimerId&&z.clearTimeout(c.clearTimerId),c.clearTimerId=z.setTimeout(function(){delete a[b]},36E5))};var KM=window,LM=document,MM=function(a){var b=KM._gaUserPrefs;if(b&&b.ioo&&b.ioo()||LM.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&KM["ga-disable-"+a]===!0)return!0;try{var c=KM.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(LM.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m;(m=g.slice(1).join("=").replace(/^\s*|\s*$/g,""))&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return LM.getElementById("__gaOptOutExtension")?!0:!1};
function XM(a){ib(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[N.m.Db]||{};ib(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function DN(a,b){}function EN(a,b){var c=function(){};return c}
function FN(a,b,c){};var GN=EN;var HN=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function IN(a,b,c){var d=this;if(!ah(a)||!Vg(b)||!Vg(c))throw J(this.getName(),["string","Object|undefined","Object|undefined"],arguments);var e=b?md(b):{};cE([function(){return M(d,"configure_google_tags",a,e)}]);var f=c?md(c):{},g=gE(this);f.originatingEntity=XE(g);Jv(Fv(a,e),g.eventId,f);}IN.K="internal.gtagConfig";
function KN(a,b){}
KN.publicName="gtagSet";function LN(){var a={};return a};function MN(a,b){}MN.publicName="injectHiddenIframe";var NN=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function ON(a,b,c,d,e){if(!((ah(a)||$g(a))&&Xg(b)&&Xg(c)&&eh(d)&&eh(e)))throw J(this.getName(),["string|OpaqueValue","function","function","boolean|undefined","boolean|undefined"],arguments);var f=gE(this);d&&NN(3);e&&(NN(1),NN(2));var g=f.eventId,h=f.qb(),m=NN(void 0);if(Bk){var n=String(m)+h;SD[g]=SD[g]||[];SD[g].push(n);TD[g]=TD[g]||[];TD[g].push("p"+h)}
if(d&&e)throw Error("useIframe and supportDocumentWrite cannot both be true.");M(this,"unsafe_inject_arbitrary_html",d,e);var p=md(b,this.J),q=md(c,this.J),r=md(a,this.J,1);PN(r,p,q,!!d,!!e,f);}
var QN=function(a,b,c,d){return function(){try{if(b.length>0){var e=b.shift(),f=QN(a,b,c,d),g=e;if(String(g.nodeName).toUpperCase()==="SCRIPT"&&g.type==="text/gtmscript"){var h=g.text||g.textContent||g.innerHTML||"",m=g.getAttribute("data-gtmsrc"),n=g.charset||"";m?rc(m,f,d,{async:!1,id:e.id,text:h,charset:n},a):(g=A.createElement("script"),g.async=!1,g.type="text/javascript",g.id=e.id,g.text=h,g.charset=n,f&&(g.onload=f),a.insertBefore(g,null));m||f()}else if(e.innerHTML&&e.innerHTML.toLowerCase().indexOf("<script")>=
0){for(var p=[];e.firstChild;)p.push(e.removeChild(e.firstChild));a.insertBefore(e,null);QN(e,p,f,d)()}else a.insertBefore(e,null),f()}else c()}catch(q){d()}}},PN=function(a,b,c,d,e,f){if(A.body){var g=wD(a,b,c);a=g.vn;b=g.onSuccess;if(d){}else e?
RN(a,b,c):QN(A.body,Ac(a),b,c)()}else z.setTimeout(function(){PN(a,b,c,d,e,f)})};ON.K="internal.injectHtml";var SN={};
function UN(a,b,c,d){}var VN={dl:1,id:1},WN={};
function XN(a,b,c,d){}H(159)?XN.publicName="injectScript":UN.publicName="injectScript";XN.K="internal.injectScript";function YN(){return Qn()}YN.K="internal.isAutoPiiEligible";function ZN(a){var b=!0;return b}ZN.publicName="isConsentGranted";function $N(a){var b=!1;return b}$N.K="internal.isDebugMode";function aO(){return On()}aO.K="internal.isDmaRegion";function bO(a){var b=!1;return b}bO.K="internal.isEntityInfrastructure";function cO(){var a=!1;return a}cO.K="internal.isLandingPage";function dO(){var a=Ch(function(b){gE(this).log("error",b)});a.publicName="JSON";return a};function eO(a){var b=void 0;if(!ah(a))throw J(this.getName(),["string"],arguments);b=lk(a);return nd(b)}eO.K="internal.legacyParseUrl";function fO(){return!1}
var gO={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function hO(){try{M(this,"logging")}catch(c){return}if(!console)return;for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=md(a[b],this.J);console.log.apply(console,a);}hO.publicName="logToConsole";function iO(a,b){}iO.K="internal.mergeRemoteConfig";function jO(a,b,c){c=c===void 0?!0:c;var d=[];return nd(d)}jO.K="internal.parseCookieValuesFromString";function kO(a){var b=void 0;if(typeof a!=="string")return;a&&ub(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=nd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=lk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=decodeURIComponent(t.splice(1).join("=")).replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password=
"";b=nd(n);return b}kO.publicName="parseUrl";function lO(a){}lO.K="internal.processAsNewEvent";function mO(a,b,c){var d;return d}mO.K="internal.pushToDataLayer";function nO(a){var b=wa.apply(1,arguments),c=!1;return c}nO.publicName="queryPermission";function oO(a){var b=this;}oO.K="internal.queueAdsTransmission";function pO(){var a="";return a}pO.publicName="readCharacterSet";function qO(){return rj.vb}qO.K="internal.readDataLayerName";function rO(){var a="";return a}rO.publicName="readTitle";function sO(a,b){var c=this;}sO.K="internal.registerCcdCallback";function tO(a){
return!0}tO.K="internal.registerDestination";var uO=["config","event","get","set"];function vO(a,b,c){}vO.K="internal.registerGtagCommandListener";function wO(a,b){var c=!1;return c}wO.K="internal.removeDataLayerEventListener";function xO(a,b){}
xO.K="internal.removeFormData";function yO(){}yO.publicName="resetDataLayer";function zO(a,b,c){var d=void 0;return d}zO.K="internal.scrubUrlParams";function AO(a){}AO.K="internal.sendAdsHit";function BO(a,b,c,d){}BO.K="internal.sendGtagEvent";function CO(a,b,c){}CO.publicName="sendPixel";function DO(a,b){}DO.K="internal.setAnchorHref";function EO(a){}EO.K="internal.setContainerConsentDefaults";function FO(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}FO.publicName="setCookie";function GO(a){}GO.K="internal.setCorePlatformServices";function HO(a,b){}HO.K="internal.setDataLayerValue";function IO(a){}IO.publicName="setDefaultConsentState";function JO(a,b){}JO.K="internal.setDelegatedConsentType";function KO(a,b){}KO.K="internal.setFormAction";function LO(a,b,c){c=c===void 0?!1:c;}LO.K="internal.setInCrossContainerData";function MO(a,b,c){return!1}MO.publicName="setInWindow";function NO(a,b,c){}NO.K="internal.setProductSettingsParameter";function OO(a,b,c){}OO.K="internal.setRemoteConfigParameter";function PO(a,b){}PO.K="internal.setTransmissionMode";function QO(a,b,c,d){var e=this;}QO.publicName="sha256";function RO(a,b,c){}
RO.K="internal.sortRemoteConfigParameters";function SO(a,b){var c=void 0;return c}SO.K="internal.subscribeToCrossContainerData";var TO={},UO={};TO.getItem=function(a){var b=null;M(this,"access_template_storage");var c=gE(this).qb();UO[c]&&(b=UO[c].hasOwnProperty("gtm."+a)?UO[c]["gtm."+a]:null);return b};TO.setItem=function(a,b){M(this,"access_template_storage");var c=gE(this).qb();UO[c]=UO[c]||{};UO[c]["gtm."+a]=b;};
TO.removeItem=function(a){M(this,"access_template_storage");var b=gE(this).qb();if(!UO[b]||!UO[b].hasOwnProperty("gtm."+a))return;delete UO[b]["gtm."+a];};TO.clear=function(){M(this,"access_template_storage"),delete UO[gE(this).qb()];};TO.publicName="templateStorage";function VO(a,b){var c=!1;if(!$g(a)||!ah(b))throw J(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}VO.K="internal.testRegex";function WO(a){var b;return b};function XO(a){var b;return b}XO.K="internal.unsiloId";function YO(a,b){var c;return c}YO.K="internal.unsubscribeFromCrossContainerData";function ZO(a){}ZO.publicName="updateConsentState";var $O;function aP(a,b,c){$O=$O||new Nh;$O.add(a,b,c)}function bP(a,b){var c=$O=$O||new Nh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=$a(b)?ih(a,b):jh(a,b)}
function cP(){return function(a){var b;var c=$O;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.C;if(e){var f=!1,g=e.qb();if(g){ph(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function dP(){var a=function(c){return void bP(c.K,c)},b=function(c){return void aP(c.publicName,c)};b(aE);b(hE);b(wF);b(yF);b(zF);b(GF);b(IF);b(MG);b(dO());b(OG);b(rK);b(sK);b(MK);b(NK);b(OK);b(UK);b(KN);b(MN);b(ZN);b(hO);b(kO);b(nO);b(pO);b(rO);b(CO);b(FO);b(IO);b(MO);b(QO);b(TO);b(ZO);aP("Math",nh());aP("Object",Lh);aP("TestHelper",Qh());aP("assertApi",kh);aP("assertThat",lh);aP("decodeUri",qh);aP("decodeUriComponent",rh);aP("encodeUri",sh);aP("encodeUriComponent",th);aP("fail",yh);aP("generateRandom",
zh);aP("getTimestamp",Ah);aP("getTimestampMillis",Ah);aP("getType",Bh);aP("makeInteger",Dh);aP("makeNumber",Eh);aP("makeString",Fh);aP("makeTableMap",Gh);aP("mock",Jh);aP("mockObject",Kh);aP("fromBase64",mK,!("atob"in z));aP("localStorage",gO,!fO());aP("toBase64",WO,!("btoa"in z));a($D);a(dE);a(yE);a(KE);a(RE);a(WE);a(lF);a(uF);a(xF);a(AF);a(BF);a(CF);a(DF);a(EF);a(FF);a(HF);a(JF);a(LG);a(NG);a(PG);a(RG);a(SG);a(TG);a(UG);a(VG);a($G);a(hH);a(iH);a(tH);a(yH);a(DH);a(MH);a(RH);a(dI);a(fI);a(tI);a(uI);
a(wI);a(kK);a(lK);a(nK);a(oK);a(pK);a(uK);a(vK);a(wK);a(xK);a(yK);a(zK);a(AK);a(BK);a(CK);a(DK);a(EK);a(GK);a(HK);a(IK);a(JK);a(KK);a(LK);a(PK);a(QK);a(RK);a(SK);a(TK);a(WK);a(IN);a(ON);a(XN);a(YN);a($N);a(aO);a(bO);a(cO);a(eO);a(jF);a(iO);a(jO);a(lO);a(mO);a(oO);a(qO);a(sO);a(tO);a(vO);a(wO);a(xO);a(Ph);a(zO);a(AO);a(BO);a(DO);a(EO);a(GO);a(HO);a(JO);a(KO);a(LO);a(NO);a(OO);a(PO);a(RO);a(SO);a(VO);a(XO);a(YO);bP("internal.CrossContainerSchema",QG());bP("internal.IframingStateSchema",LN());H(103)&&a(tK);H(159)?b(XN):b(UN);return cP()};var YD;
function eP(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;YD=new He;fP();of=XD();var e=YD,f=dP(),g=new fd("require",f);g.Qa();e.C.C.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Kf(n,d[m]);try{YD.execute(n),H(119)&&Bk&&n[0]===50&&h.push(n[1])}catch(r){}}H(119)&&(Bf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");Hj[q]=
["sandboxedScripts"]}gP(b)}function fP(){YD.C.C.N=function(a,b,c){Ko.SANDBOXED_JS_SEMAPHORE=Ko.SANDBOXED_JS_SEMAPHORE||0;Ko.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Ko.SANDBOXED_JS_SEMAPHORE--}}}function gP(a){a&&ib(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Hj[e]=Hj[e]||[];Hj[e].push(b)}})};function hP(a){Jv(Dv("developer_id."+a,!0),0,{})};var iP=Array.isArray;function jP(a,b){return Xc(a,b||null)}function W(a){return window.encodeURIComponent(a)}function kP(a,b,c){vc(a,b,c)}function lP(a,b){if(!a)return!1;var c=fk(lk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function mP(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var vP=z.clearTimeout,wP=z.setTimeout;function xP(a,b,c){if(Yq()){b&&D(b)}else return rc(a,b,c,void 0)}function yP(){return z.location.href}function zP(a,b){return Rj(a,b||2)}function AP(a,b){z[a]=b}function BP(a,b,c){b&&(z[a]===void 0||c&&!z[a])&&(z[a]=b);return z[a]}function CP(a,b){if(Yq()){b&&D(b)}else tc(a,b)}
var DP={};var X={securityGroups:{}};

X.securityGroups.access_template_storage=["google"],X.__access_template_storage=function(){return{assert:function(){},P:function(){return{}}}},X.__access_template_storage.F="access_template_storage",X.__access_template_storage.isVendorTemplate=!0,X.__access_template_storage.priorityOverride=0,X.__access_template_storage.isInfrastructure=!1,X.__access_template_storage.runInSiloedMode=!1;
X.securityGroups.access_element_values=["google"],function(){function a(b,c,d,e){return{element:c,operation:d,newValue:e}}(function(b){X.__access_element_values=b;X.__access_element_values.F="access_element_values";X.__access_element_values.isVendorTemplate=!0;X.__access_element_values.priorityOverride=0;X.__access_element_values.isInfrastructure=!1;X.__access_element_values.runInSiloedMode=!1})(function(b){var c=b.vtp_allowRead,d=b.vtp_allowWrite,e=b.vtp_createPermissionError;return{assert:function(f,
g,h,m){if(!(g instanceof HTMLElement))throw e(f,{},"Element must be a HTMLElement.");if(h!=="read"&&h!=="write")throw e(f,{},"Unknown operation: "+h+".");if(h=="read"&&!c)throw e(f,{},"Attempting to perform disallowed operation: read.");if(h=="write"){if(!d)throw e(f,{},"Attempting to perform disallowed operation: write.");if(!ab(m))throw e(f,{},"Attempting to write value without valid new value.");}},P:a}})}();

X.securityGroups.access_globals=["google"],function(){function a(b,c,d){var e={key:d,read:!1,write:!1,execute:!1};switch(c){case "read":e.read=!0;break;case "write":e.write=!0;break;case "readwrite":e.read=e.write=!0;break;case "execute":e.execute=!0;break;default:throw Error("Invalid "+b+" request "+c);}return e}(function(b){X.__access_globals=b;X.__access_globals.F="access_globals";X.__access_globals.isVendorTemplate=!0;X.__access_globals.priorityOverride=0;X.__access_globals.isInfrastructure=!1;
X.__access_globals.runInSiloedMode=!1})(function(b){for(var c=b.vtp_keys||[],d=b.vtp_createPermissionError,e=[],f=[],g=[],h=0;h<c.length;h++){var m=c[h],n=m.key;m.read&&e.push(n);m.write&&f.push(n);m.execute&&g.push(n)}return{assert:function(p,q,r){if(!ab(r))throw d(p,{},"Key must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else if(q==="readwrite"){if(f.indexOf(r)>-1&&e.indexOf(r)>-1)return}else if(q==="execute"){if(g.indexOf(r)>-1)return}else throw d(p,
{},"Operation must be either 'read', 'write', or 'execute', was "+q);throw d(p,{},"Prohibited "+q+" on global variable: "+r+".");},P:a}})}();
X.securityGroups.access_dom_element_properties=["google"],function(){function a(b,c,d,e){var f={property:e,read:!1,write:!1};switch(d){case "read":f.read=!0;break;case "write":f.write=!0;break;default:throw Error("Invalid "+b+" operation "+d);}return f}(function(b){X.__access_dom_element_properties=b;X.__access_dom_element_properties.F="access_dom_element_properties";X.__access_dom_element_properties.isVendorTemplate=!0;X.__access_dom_element_properties.priorityOverride=0;X.__access_dom_element_properties.isInfrastructure=
!1;X.__access_dom_element_properties.runInSiloedMode=!1})(function(b){for(var c=b.vtp_properties||[],d=b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],m=h.property;h.read&&e.push(m);h.write&&f.push(m)}return{assert:function(n,p,q,r){if(!ab(r))throw d(n,{},"Property must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else throw d(n,{},'Operation must be either "read" or "write"');throw d(n,{},'"'+q+'" operation is not allowed.');
},P:a}})}();
X.securityGroups.read_dom_element_text=["google"],function(){function a(b,c){return{element:c}}(function(b){X.__read_dom_element_text=b;X.__read_dom_element_text.F="read_dom_element_text";X.__read_dom_element_text.isVendorTemplate=!0;X.__read_dom_element_text.priorityOverride=0;X.__read_dom_element_text.isInfrastructure=!1;X.__read_dom_element_text.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(!(e instanceof HTMLElement))throw c(d,{},"Wrong element type. Must be HTMLElement.");
},P:a}})}();

X.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){X.__get_referrer=b;X.__get_referrer.F="get_referrer";X.__get_referrer.isVendorTemplate=!0;X.__get_referrer.priorityOverride=0;X.__get_referrer.isInfrastructure=!1;X.__get_referrer.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&
c.push("extension"),b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!ab(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!ab(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},
"Prohibited query key: "+h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},P:a}})}();
X.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){X.__read_event_data=b;X.__read_event_data.F="read_event_data";X.__read_event_data.isVendorTemplate=!0;X.__read_event_data.priorityOverride=0;X.__read_event_data.isInfrastructure=!1;X.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!ab(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&yg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},P:a}})}();
X.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){X.__read_data_layer=b;X.__read_data_layer.F="read_data_layer";X.__read_data_layer.isVendorTemplate=!0;X.__read_data_layer.priorityOverride=0;X.__read_data_layer.isInfrastructure=!1;X.__read_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!ab(g))throw e(f,{},"Keys must be strings.");if(c!==
"any"){try{if(yg(g,d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},P:a}})}();
X.securityGroups.read_document_dimensions=["google"],function(){function a(){return{}}(function(b){X.__read_document_dimensions=b;X.__read_document_dimensions.F="read_document_dimensions";X.__read_document_dimensions.isVendorTemplate=!0;X.__read_document_dimensions.priorityOverride=0;X.__read_document_dimensions.isInfrastructure=!1;X.__read_document_dimensions.runInSiloedMode=!1})(function(){return{assert:function(){},P:a}})}();


X.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){X.__detect_history_change_events=b;X.__detect_history_change_events.F="detect_history_change_events";X.__detect_history_change_events.isVendorTemplate=!0;X.__detect_history_change_events.priorityOverride=0;X.__detect_history_change_events.isInfrastructure=!1;X.__detect_history_change_events.runInSiloedMode=!1})(function(){return{assert:function(){},P:a}})}();

X.securityGroups.gaawe=["google"],function(){function a(f,g,h){for(var m=0;m<g.length;m++)f.hasOwnProperty(g[m])&&(f[g[m]]=h(f[g[m]]))}function b(f,g,h){var m={},n=function(u,v){m[u]=m[u]||v},p=function(u,v,w){w=w===void 0?!1:w;c.push(6);if(u){m.items=m.items||[];for(var x={},y=0;y<u.length;x={tf:void 0},y++)x.tf={},ib(u[y],function(C){return function(E,F){w&&E==="id"?C.tf.promotion_id=F:w&&E==="name"?C.tf.promotion_name=F:C.tf[E]=F}}(x)),m.items.push(x.tf)}if(v)for(var B in v)d.hasOwnProperty(B)?n(d[B],
v[B]):n(B,v[B])},q;f.vtp_getEcommerceDataFrom==="dataLayer"?(q=f.vtp_gtmCachedValues.eventModel)||(q=f.vtp_gtmCachedValues.ecommerce):(q=f.vtp_ecommerceMacroData,Wc(q)&&q.ecommerce&&!q.items&&(q=q.ecommerce));if(Wc(q)){var r=!1,t;for(t in q)q.hasOwnProperty(t)&&(r||(c.push(5),r=!0),t==="currencyCode"?n("currency",q.currencyCode):t==="impressions"&&g===N.m.Pb?p(q.impressions,null):t==="promoClick"&&g===N.m.nc?p(q.promoClick.promotions,q.promoClick.actionField,!0):t==="promoView"&&g===N.m.Qb?p(q.promoView.promotions,
q.promoView.actionField,!0):e.hasOwnProperty(t)?g===e[t]&&p(q[t].products,q[t].actionField):m[t]=q[t]);jP(m,h)}}var c=[],d={id:"transaction_id",revenue:"value",list:"item_list_name"},e={click:"select_item",detail:"view_item",add:"add_to_cart",remove:"remove_from_cart",checkout:"begin_checkout",checkout_option:"checkout_option",purchase:"purchase",refund:"refund"};(function(f){X.__gaawe=f;X.__gaawe.F="gaawe";X.__gaawe.isVendorTemplate=!0;X.__gaawe.priorityOverride=0;X.__gaawe.isInfrastructure=!1;X.__gaawe.runInSiloedMode=
!1})(function(f){var g;g=f.vtp_migratedToV2?String(f.vtp_measurementIdOverride):String(f.vtp_measurementIdOverride||f.vtp_measurementId);if(ab(g)&&g.indexOf("G-")===0){var h=String(f.vtp_eventName),m={};c=[];f.vtp_sendEcommerceData&&(Wh.hasOwnProperty(h)||h==="checkout_option")&&b(f,h,m);var n=f.vtp_eventSettingsVariable;if(n)for(var p in n)n.hasOwnProperty(p)&&(m[p]=n[p]);if(f.vtp_eventSettingsTable){var q=mP(f.vtp_eventSettingsTable,"parameter","parameterValue"),r;for(r in q)m[r]=q[r]}var t=mP(f.vtp_eventParameters,
"name","value"),u;for(u in t)t.hasOwnProperty(u)&&(m[u]=t[u]);var v=f.vtp_userDataVariable;v&&(m[N.m.Na]=v);if(m.hasOwnProperty(N.m.Db)||f.vtp_userProperties){var w=m[N.m.Db]||{};jP(mP(f.vtp_userProperties,"name","value"),w);m[N.m.Db]=w}var x={originatingEntity:VA(1,f.vtp_gtmEntityIndex,f.vtp_gtmEntityName)};if(c.length>0){var y={};x.eventMetadata=(y.event_usage=c,y)}a(m,Xh,function(C){return lb(C)});a(m,Zh,function(C){return Number(C)});var B=f.vtp_gtmEventId;x.noGtmEvent=!0;Jv(Gv(g,h,m),B,x);D(f.vtp_gtmOnSuccess)}else D(f.vtp_gtmOnFailure)})}();



X.securityGroups.get_element_attributes=["google"],function(){function a(b,c,d){return{element:c,attribute:d}}(function(b){X.__get_element_attributes=b;X.__get_element_attributes.F="get_element_attributes";X.__get_element_attributes.isVendorTemplate=!0;X.__get_element_attributes.priorityOverride=0;X.__get_element_attributes.isInfrastructure=!1;X.__get_element_attributes.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedAttributes||"specific",d=b.vtp_attributes||[],e=b.vtp_createPermissionError;
return{assert:function(f,g,h){if(!ab(h))throw e(f,{},"Attribute must be a string.");if(!(g instanceof HTMLElement))throw e(f,{},"Wrong element type. Must be HTMLElement.");if(h==="value"||c!=="any"&&(c!=="specific"||d.indexOf(h)===-1))throw e(f,{},'Reading attribute "'+h+'" is not allowed.');},P:a}})}();
X.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){X.__detect_link_click_events=b;X.__detect_link_click_events.F="detect_link_click_events";X.__detect_link_click_events.isVendorTemplate=!0;X.__detect_link_click_events.priorityOverride=0;X.__detect_link_click_events.isInfrastructure=!1;X.__detect_link_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},P:a}})}();
X.securityGroups.load_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,firstPartyUrl:d}}(function(b){X.__load_google_tags=b;X.__load_google_tags.F="load_google_tags";X.__load_google_tags.isVendorTemplate=!0;X.__load_google_tags.priorityOverride=0;X.__load_google_tags.isInfrastructure=!1;X.__load_google_tags.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_allowFirstPartyUrls||!1,e=b.vtp_allowedFirstPartyUrls||"specific",f=b.vtp_urls||[],g=b.vtp_tagIds||
[],h=b.vtp_createPermissionError;return{assert:function(m,n,p){(function(q){if(!ab(q))throw h(m,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||g.indexOf(q)===-1))throw h(m,{},"Prohibited Tag ID: "+q+".");})(n);(function(q){if(q!==void 0){if(!ab(q))throw h(m,{},"First party URL must be a string.");if(d){if(e==="any")return;if(e==="specific")try{if(Qg(lk(q),f))return}catch(r){throw h(m,{},"Invalid first party URL filter.");}}throw h(m,{},"Prohibited first party URL: "+q);}})(p)},P:a}})}();
X.securityGroups.read_container_data=["google"],X.__read_container_data=function(){return{assert:function(){},P:function(){return{}}}},X.__read_container_data.F="read_container_data",X.__read_container_data.isVendorTemplate=!0,X.__read_container_data.priorityOverride=0,X.__read_container_data.isInfrastructure=!1,X.__read_container_data.runInSiloedMode=!1;



X.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){X.__get_url=b;X.__get_url.F="get_url";X.__get_url.isVendorTemplate=!0;X.__get_url.priorityOverride=0;X.__get_url.isInfrastructure=!1;X.__get_url.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),
b.vtp_fragment&&c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!ab(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!ab(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},P:a}})}();



X.securityGroups.read_dom_elements=["google"],function(){function a(b,c,d){return{type:c,value:d}}(function(b){X.__read_dom_elements=b;X.__read_dom_elements.F="read_dom_elements";X.__read_dom_elements.isVendorTemplate=!0;X.__read_dom_elements.priorityOverride=0;X.__read_dom_elements.isInfrastructure=!1;X.__read_dom_elements.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedElementIds||"none",d=b.vtp_allowedCssSelectors||"none",e=b.vtp_elementIds||[],f=b.vtp_cssSelectors||[],g=b.vtp_createPermissionError;
return{assert:function(h,m,n){switch(m){case "id":if(c==="none")break;if(c==="any"||e.indexOf(n)>-1)return;break;case "css":if(d==="none")break;if(d==="any"||f.indexOf(n)>-1)return;break;default:throw g(h,{},"Unknown selector type "+m+".");}throw g(h,{},"Prohibited selector value "+n+" for selector type "+m+".");},P:a}})}();
X.securityGroups.unsafe_inject_arbitrary_html=["google"],function(){function a(b,c,d){return{useIframe:c,supportDocumentWrite:d}}(function(b){X.__unsafe_inject_arbitrary_html=b;X.__unsafe_inject_arbitrary_html.F="unsafe_inject_arbitrary_html";X.__unsafe_inject_arbitrary_html.isVendorTemplate=!0;X.__unsafe_inject_arbitrary_html.priorityOverride=0;X.__unsafe_inject_arbitrary_html.isInfrastructure=!1;X.__unsafe_inject_arbitrary_html.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;
return{assert:function(d,e,f){if(e&&f)throw c(d,{},"Only one of useIframe and supportDocumentWrite can be true.");if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"useIframe must be a boolean.");if(f!==void 0&&typeof f!=="boolean")throw c(d,{},"supportDocumentWrite must be a boolean.");},P:a}})}();

X.securityGroups.detect_click_events=["google"],function(){function a(b,c,d){return{matchCommonButtons:c,cssSelector:d}}(function(b){X.__detect_click_events=b;X.__detect_click_events.F="detect_click_events";X.__detect_click_events.isVendorTemplate=!0;X.__detect_click_events.priorityOverride=0;X.__detect_click_events.isInfrastructure=!1;X.__detect_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e,f){if(e!==void 0&&typeof e!=="boolean")throw c(d,
{},"matchCommonButtons must be a boolean.");if(f!==void 0&&typeof f!=="string")throw c(d,{},"cssSelector must be a string.");},P:a}})}();
X.securityGroups.logging=["google"],function(){function a(){return{}}(function(b){X.__logging=b;X.__logging.F="logging";X.__logging.isVendorTemplate=!0;X.__logging.priorityOverride=0;X.__logging.isInfrastructure=!1;X.__logging.runInSiloedMode=!1})(function(b){var c=b.vtp_environments||"debug",d=b.vtp_createPermissionError;return{assert:function(e){var f;if(f=c!=="all"&&!0){var g=!1;f=!g}if(f)throw d(e,{},"Logging is not enabled in all environments");
},P:a}})}();

X.securityGroups.configure_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,configuration:d}}(function(b){X.__configure_google_tags=b;X.__configure_google_tags.F="configure_google_tags";X.__configure_google_tags.isVendorTemplate=!0;X.__configure_google_tags.priorityOverride=0;X.__configure_google_tags.isInfrastructure=!1;X.__configure_google_tags.runInSiloedMode=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_tagIds||[],e=b.vtp_createPermissionError;return{assert:function(f,
g){if(!ab(g))throw e(f,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||d.indexOf(g)===-1))throw e(f,{},"Prohibited configuration for Tag ID: "+g+".");},P:a}})}();
X.securityGroups.read_document_visibility_state=["google"],function(){function a(){return{}}(function(b){X.__read_document_visibility_state=b;X.__read_document_visibility_state.F="read_document_visibility_state";X.__read_document_visibility_state.isVendorTemplate=!0;X.__read_document_visibility_state.priorityOverride=0;X.__read_document_visibility_state.isInfrastructure=!1;X.__read_document_visibility_state.runInSiloedMode=!1})(function(){return{assert:function(){},P:a}})}();

X.securityGroups.read_element_dimensions=["google"],function(){function a(b,c){return{element:c}}(function(b){X.__read_element_dimensions=b;X.__read_element_dimensions.F="read_element_dimensions";X.__read_element_dimensions.isVendorTemplate=!0;X.__read_element_dimensions.priorityOverride=0;X.__read_element_dimensions.isInfrastructure=!1;X.__read_element_dimensions.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(!(e instanceof HTMLElement))throw c(d,
{},"Element must be a HTMLElement.");},P:a}})}();




X.securityGroups.read_element_style=["google"],function(){function a(b,c,d){return{element:c,style:d}}(function(b){X.__read_element_style=b;X.__read_element_style.F="read_element_style";X.__read_element_style.isVendorTemplate=!0;X.__read_element_style.priorityOverride=0;X.__read_element_style.isInfrastructure=!1;X.__read_element_style.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e,f){if(!(e instanceof HTMLElement))throw c(d,{},"Element must be a HTMLElement.");
if(!ab(f))throw c(d,{},"Style must be a string.");},P:a}})}();
var No={dataLayer:Sj,callback:function(a){Gj.hasOwnProperty(a)&&$a(Gj[a])&&Gj[a]();delete Gj[a]},bootstrap:0};No.onHtmlSuccess=xD(!0),No.onHtmlFailure=xD(!1);
function EP(){Mo();im();QA();sb(Hj,X.securityGroups);var a=dm(em()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;ko(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);tD(),xf({An:function(d){return d===rD},Jm:function(d){return new uD(d)},Bn:function(d){for(var e=!1,f=!1,g=2;g<d.length;g++)e=e||d[g]===8,f=f||d[g]===16;return e&&f},Tn:function(d){var e;if(d===rD)e=d;else{var f=Po();sD[f]=d;e='google_tag_manager["rm"]["'+bm()+'"]('+f+")"}return e}});
Af={Em:Qf}}var FP=!1;
function In(){try{if(FP||!rm()){qj();oj.O="";oj.ab="ad_storage|analytics_storage|ad_user_data|ad_personalization";
oj.ia="ad_storage|analytics_storage|ad_user_data";oj.fa="54l0";oj.fa="5510";gm();if(H(108)){}hg[8]=
!0;var a=Lo("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});ro(a);Jo();PD();Aq();Qo();if(jm()){gF();AA().removeExternalRestrictions(bm());}else{$x();MA();yf();uf=X;vf=zD;Sf=new Zf;eP();EP();Gn||(Fn=Kn());Go();PC();aC();vC=!1;A.readyState==="complete"?xC():wc(z,"load",xC);VB();Bk&&(Fp(Tp),z.setInterval(Sp,864E5),Fp(RD),Fp(sB),Fp(gz),Fp(Wp),Fp(UD),Fp(DB),H(119)&&(Fp(xB),Fp(yB),Fp(zB)));Ck&&(ln(),kp(),RC(),VC(),TC(),bn("bt",String(oj.C?2:zj?1:0)),bn("ct",String(oj.C?0:zj?1:Yq()?2:3)),SC());pD();vn(1);hF();Fj=pb();No.bootstrap=Fj;oj.N&&OC();H(108)&&zz();H(133)&&(typeof z.name==="string"&&
ub(z.name,"web-pixel-sandbox-CUSTOM")&&Mc()?hP("dMDg0Yz"):z.Shopify&&(hP("dN2ZkMj"),Mc()&&hP("dNTU0Yz")))}}}catch(b){vn(4),Pp()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Xn(n)&&(m=h.Nj)}function c(){m&&ic?g(m):a()}if(!z["__TAGGY_INSTALLED"]){var d=!1;if(A.referrer){var e=lk(A.referrer);d=hk(e,"host")==="cct.google"}if(!d){var f=ir("googTaggyReferrer");d=!(!f.length||!f[0].length)}d&&(z["__TAGGY_INSTALLED"]=!0,rc("https://cct.google/taggy/agent.js"))}var g=function(u){var v="GTM",w="GTM";xj&&(v="OGT",w="GTAG");var x=z["google.tagmanager.debugui2.queue"];x||(x=
[],z["google.tagmanager.debugui2.queue"]=x,rc("https://"+rj.Ff+"/debug/bootstrap?id="+Wf.ctid+"&src="+w+"&cond="+u+"&gtm="+$q()));var y={messageType:"CONTAINER_STARTING",data:{scriptSource:ic,containerProduct:v,debug:!1,id:Wf.ctid,targetRef:{ctid:Wf.ctid,isDestination:Tl()},aliases:Wl(),destinations:Ul()}};y.data.resume=function(){a()};rj.Xk&&(y.data.initialPublish=!0);x.push(y)},h={Zl:1,Pj:2,Xj:3,Qi:4,Nj:5};h[h.Zl]="GTM_DEBUG_LEGACY_PARAM";h[h.Pj]="GTM_DEBUG_PARAM";h[h.Xj]="REFERRER";h[h.Qi]="COOKIE";h[h.Nj]="EXTENSION_PARAM";
var m=void 0,n=void 0,p=fk(z.location,"query",!1,void 0,"gtm_debug");Xn(p)&&(m=h.Pj);if(!m&&A.referrer){var q=lk(A.referrer);hk(q,"host")==="tagassistant.google.com"&&(m=h.Xj)}if(!m){var r=ir("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Qi)}m||b();if(!m&&Wn(n)){var t=!1;wc(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);z.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){H(82)&&FP&&!Kn()["0"]?Hn():In()});

})()

