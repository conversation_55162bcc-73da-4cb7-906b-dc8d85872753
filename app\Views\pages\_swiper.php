<div class="swiper-wrapper flex" style="transition-duration: 0ms; transform: translate3d(-20093.5px, 0px, 0px);">
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="0">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/1.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction worker using an angle grinder, creating sparks at a construction site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="1">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            
                            <img src="/images/leadership/2.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction workers in high-visibility clothing signing a beam at a construction site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="2">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/3.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two professionals analyzing data on a laptop in an office setting with a data visualization projected on the screen in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="3">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/4.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two construction workers in hard hats and reflective vests engage in a discussion on site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="4">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/5.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction worker making measurements at a building site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="5">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/6.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two men engaged in a conversation at a workspace.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate swiper-slide-duplicate-prev" data-swiper-slide-index="6">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/7.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction workers engaging in building activities on a construction site with modern buildings in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>



    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate-active" data-swiper-slide-index="0">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/1.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction worker using an angle grinder, creating sparks at a construction site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate-next" data-swiper-slide-index="1">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/2.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction workers in high-visibility clothing signing a beam at a construction site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab" data-swiper-slide-index="2">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/3.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two professionals analyzing data on a laptop in an office setting with a data visualization projected on the screen in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab" data-swiper-slide-index="3">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/4.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two construction workers in hard hats and reflective vests engage in a discussion on site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab" data-swiper-slide-index="4">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/5.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction worker making measurements at a building site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab" data-swiper-slide-index="5">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/6.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two men engaged in a conversation at a workspace.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-prev" data-swiper-slide-index="6">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/7.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction workers engaging in building activities on a construction site with modern buildings in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>



    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate swiper-slide-active" data-swiper-slide-index="0">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/1.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction worker using an angle grinder, creating sparks at a construction site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate swiper-slide-next" data-swiper-slide-index="1">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/2.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction workers in high-visibility clothing signing a beam at a construction site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="2">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/3.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two professionals analyzing data on a laptop in an office setting with a data visualization projected on the screen in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="3">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/4.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two construction workers in hard hats and reflective vests engage in a discussion on site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="4">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/5.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction worker making measurements at a building site.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate" data-swiper-slide-index="5">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/6.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Two men engaged in a conversation at a workspace.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
    <div x-data="" x-ref="slide" class="swiper-slide px-2 basis-[calc(100vw-4rem+1rem)] lg:basis-[calc(100vw-6rem+1rem)] 2xl:basis-[calc(100vw-6rem+1rem-(100vw-1536px))] cursor-grab swiper-slide-duplicate swiper-slide-duplicate-prev" data-swiper-slide-index="6">
        <div class="relative overflow-hidden aspect-square md:aspect-16/9 ultrawide:aspect-16/7">
            <div x-data="animate()" x-init="
                opacity.active = false;
                scale = {
                ...scale, active: true,
                start: 1.08,
                end: 1.08,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: -4,
                end: 4,
                ease: 'none',
                };
                trigger = $refs.slider;
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="absolute inset-0 w-full h-full overflow-hidden" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0.4382%) translate3d(0px, 0px, 0px) scale(1.08, 1.08);">
                <div x-data="animate()" x-init="
                    element = $refs.media;
                    opacity.active = false;
                    xPercent = {
                    ...xPercent, active: true,
                    start: -40,
                    end: 40,
                    ease: 'none',
                    };
                    scrollSettings = {
                    horizontal: true,
                    start: 'left right',
                    end: 'right left',
                    toggleActions: toggleActions,
                    scroller: $refs.slider,
                    trigger: $refs.slider,
                    scrub: 1.2,
                    };
                    animate();
                    " x-ref="media" class="absolute inset-0 w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px);">
                    <div class="
                        absolute inset-0
                        w-full h-full
                        ">
                        <picture>
                            <img src="/images/leadership/7.jpg" class="
                                absolute inset-0 w-full h-full object-cover
                                w-full h-full
                                " alt="Construction workers engaging in building activities on a construction site with modern buildings in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div data-slide-cover="" class="
                bg-primary
                absolute z-10 inset-0
                " style="translate: none; rotate: none; scale: none; transform: translate(0%, 110%);"></div>
        </div>
    </div>
</div>