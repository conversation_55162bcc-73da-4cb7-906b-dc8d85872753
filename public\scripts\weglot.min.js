var Weglot=function(){"use strict";var e,t={},n={v1:[],v2:["ABBR","ACRONYM","B","BDO","BIG","CITE","EM","I","KBD","Q","SMALL","STRONG","SUB","SUP","U"],v3:["A","BDI","BR","DEL","DFN","INS","S","SPAN"]};var r=function(){if(e)return t;e=1;var r=n;return r.v2.unshift("#text"),t.mergeNodesList=r,t}(),a=function(e){return document.getElementById(e)},o=function(){var e=a("weglot-data");if(!e)return null;try{return JSON.parse(e.textContent)}catch(e){return null}};function i(e,t){var n=document.createElement("style");l(a(t)),n.id=t,n.type="text/css",n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e)),document.head&&document.head.appendChild(n)}function l(e){e&&e.parentNode&&e.parentNode.removeChild(e)}var s=function(e){return"function"==typeof ShadowRoot&&e instanceof ShadowRoot},u={excluded_blocks:[],media_enabled:!1,external_enabled:!1,extra_definitions:[],translation_engine:2,noTranslateAttribute:"data-wg-notranslate",mergeNodes:[]},c=function(){try{return JSON.parse('{"TRANSLATION":"translations.weglot.io","SWITCHER":"switchers.weglot.io","EXCLUSION":"exclusions.weglot.io","DEFINITION":"definitions.weglot.io"}')}catch(e){return{}}}(),d=Object.keys(c).map((function(e){return c[e]}));function f(e){for(var t=0,n=d;t<n.length;t+=1){var r=n[t];if(-1!==e.indexOf(r))return!0}return!1}var g={ddtags:"env:prod",clientToken:"pub4efaec96ce2494088ba70a2049d58dc3",site:"datadoghq.com",version:"5.57.2"},p={"dd-api-key":"pub4efaec96ce2494088ba70a2049d58dc3",ddsource:"browser"},v="prod";function _(e){var t=e.service;function n(e,n,r){var a=window.Weglot&&window.Weglot.options&&window.Weglot.options.disable_datadog_logging,o=r.sendToConsole;void 0===o&&(o=!0);var i=r.consoleOverride,l=r.sendToDatadog;if(void 0===l&&(l=!a),l&&"dev"!==v&&function(e,t,n){"string"==typeof t&&(t={message:t});var r=Object.assign({},t,{service:e,status:n},window.location&&{view:{url:window.location.href}},t.message&&{message:t.message},t.stack&&{stack:t.stack},t.status&&{logStatus:t.status},g);window.Weglot&&window.Weglot.options&&(r.projectInfo=["host","api_key","url_type","technology_name","technology_id","is_connect","auto_switch"].reduce((function(e,t){var n;return Object.assign({},e,((n={})[t]=window.Weglot.options[t],n))}),{}));var a=Object.keys(p).map((function(e){return e+"="+p[e]})).join("&");fetch("https://http-intake.logs.datadoghq.com/api/v2/logs?"+a,{method:"POST",body:JSON.stringify(r),headers:{"Content-Type":"application/json"}})}(t,e,n),o){var s=i||e,u=["notice","info"].includes(n)?"log":n;console[u]("[Weglot]",s)}}var r=function(e){return function(t,r){return void 0===r&&(r={}),n(t,e,r)}};return{log:r("info"),info:r("info"),notice:r("notice"),warn:r("warn"),error:r("error")}}var h=_({service:"html-parser-engine"});function m(e,t,n){var r=t&&t[e];if(r&&r.textContent===t.textContent)return r.result;var a=n(t);return t?(t[e]={result:a,textContent:t.textContent},a):a}function y(e){return m("__validMergeNodes",e,(function(e){return e&&k(e)&&w(e)&&!function(e){return m("__containsNoTranslateNodes",e,(function(e){return 1===e.nodeType&&(!!e.querySelector("["+u.noTranslateAttribute+"]")||x(e))}))}(e)}))}function w(e,t){return void 0===t&&(t=!0),m("__validTextNodes",e,(function(e){return!(!e.textContent||t&&!e.textContent.trim()||-1!==e.textContent.indexOf("BESbswy")||e.parentNode&&e.parentNode.nodeName&&-1!==["script","style","noscript","textarea"].indexOf(e.parentNode.nodeName.toLowerCase())||function(e){if(!(e=e.trim()))return!1;var t=e.charAt(0);if("["!==t&&"{"!==t)return!1;var n=e[e.length-1];if("]"!==n&&"}"!==n)return!1;return e=e.replace(/\\(?:["\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""),/^[\],:{}\s]*$/.test(e)}(e.textContent))}))}function b(e){try{if(u.mergedSelectorRemove&&N(e,u.mergedSelectorRemove))return!1}catch(e){}return!(!u.mergeNodes||-1===u.mergeNodes.indexOf(e.nodeName))||(e.dataset&&void 0!==e.dataset.wgMerge||u.selectorMerging&&e.matches&&e.matches(u.selectorMerging))}function k(e){return m("__onlyInlineChildsNodes",e,(function(e){if(!e.childNodes)return!0;for(var t=0,n=e.childNodes;t<n.length;t+=1){var r=n[t];if(r.weglot||!b(r)||!k(r))return!1}return!0}))}function x(e){if(!e.children)return!1;for(var t=0,n=e.children;t<n.length;t+=1){var r=n[t];if("wgNoTranslate"in r&&r.wgNoTranslate||x(r))return!0}return!1}function S(e){return!!e&&(!(!("wgNoTranslate"in e)||!e.wgNoTranslate)||S(e.parentNode))}function E(e){if(!e)return!1;var t=e.closest?e:e.parentNode;return!(!t||!N(t,"["+u.noTranslateAttribute+"]"))||S(e)}var C=function(e,t){return function(n,r){try{var a=r;return-1!==a.indexOf(":")&&(a=a.replace(/([^\\]):/g,"$1\\:")),n[e]?n[e](a):t}catch(a){try{return n[e]?n[e](r):t}catch(e){h.warn(e,{consoleOverride:"Your CSS rules are incorrect: "+r,sendToDatadog:!1})}}return t}},O=C("querySelectorAll",[]),T=C("matches",!1),N=C("closest",null);function L(e){return e.indexOf("<")>-1&&e.indexOf(">")>-1}var A=new WeakMap;function j(e){if(!e)return[];var t=e.querySelectorAll?e:e.parentNode;if(!t)return[];if(W(t),!u.whitelist||!u.whitelist.length)return[].concat(function(e){var t=document.getElementsByTagName("title")[0];if(e!==document.documentElement||!document.title||!t||E(t))return[];return[{element:t.firstChild,type:9,words:t.textContent,properties:{}}]}(t),R(t));var n=u.whitelist.map((function(e){return e.value})).join(",");if(N(t,n))return R(t);for(var r=[],a=0,o=O(t,n);a<o.length;a+=1){var i=o[a];[].push.apply(r,R(i))}return r}function R(e){return[].concat(function(e){var t=[];return X.forEach((function(n){for(var r,a,o,i=n.attribute,l=n.type,s=n.selectors,u=0,c=(r=e,a=[],o=s.join(","),r.matches&&r.matches(o)&&a.push(r),r.childElementCount>0&&a.push.apply(a,r.querySelectorAll(o)),a);u<c.length;u+=1){var d=c[u];if(d&&!E(d)){var f=i.get(d);H(f)||t.push({element:d,words:f,type:l,attrSetter:i.set,attrName:i.name})}}})),t}(e),function(e){var t,n=[],r=u.translation_engine>=2,a=document.createTreeWalker(e,4,P,!1);for(;t=a.nextNode();){var o=(r&&(b(t.parentNode)||t.parentNode.childNodes.length>1)?I:D)(t,a);o&&n.push(o)}return n}(e))}function P(e){return!w(e)||E(e)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}function I(e,t){var n=function(e){if(e.wgResolved)return!1;var t=e;do{if(t.wgResolved)return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return!1}(e);if(n&&A.has(n)){var r=A.get(n);return{element:r[0],words:r[1],type:1,properties:r[2]}}var a=function(e,t){var n=[],r=e;for(;y(e.parentNode);)e=e.parentNode,r.textContent.trim()!==e.textContent.trim()&&(r=e);r.textContent.trim()===e.textContent.trim()&&(e=r);for(;t.nextNode();)if(!e.contains||!e.contains(t.currentNode)){t.previousNode();break}var a=s(e)&&!e.clonable?(o=e,i=document.createDocumentFragment(),o.childNodes.forEach((function(e){return i.appendChild(e.cloneNode(!0))})),i):e.cloneNode(!0);var o,i;if(u.translation_engine>2){U(e,(function(e){if(1===e.nodeType){var t=function(e){for(var t=[],n=0,r=e.attributes;n<r.length;n+=1){var a=r[n];t.push(a)}return t}(e);n.push({attributes:t,child:e})}}));var l=1;U(a,(function(e){1===e.nodeType&&(!function(e){if(!e.attributes)return e;for(;e.attributes.length>0;)e.removeAttribute(e.attributes[0].name)}(e),e.setAttribute("wg-"+l++,""))}))}if(e){return e.wgResolved=!0,[e,(a.innerHTML||a.textContent||"").replace(/<!--[^>]*-->/g,""),n]}}(e,t);if(a){var o=a[0],i=a[1],l=a[2];if(!H(i))return A.set(o,a),{element:o,words:i,type:1,properties:l}}}function D(e){var t=e.textContent;if(!H(t))return{element:e,words:t,type:1,properties:{}}}function M(e,t){e.wgNoTranslate=!u.private_mode||"Excluded by selector: "+t.find((function(t){return T(e,t)}))}function F(e,t){if("wgNoTranslate"in e&&e.wgNoTranslate&&!T(e,t)&&(e.wgNoTranslate=!1),e.children)for(var n=0,r=e.children;n<r.length;n+=1){F(r[n],t)}}function W(e){var t=u.excluded_blocks;if(t&&t.length){var n=t.map((function(e){return e.value})),r=n.join(",");T(e,r)?M(e,n):e.wgNoTranslate=!1;for(var a=0,o=O(e,r);a<o.length;a+=1){M(o[a],n)}F(e,r)}else e.wgNoTranslate=!1}function U(e,t){if(e.childNodes)for(var n=0,r=e.childNodes;n<r.length;n+=1){var a=r[n];if(!a)return;t(a),U(a,t)}}function H(e){return!e||!e.trim()||!isNaN(e)||"​"===e}function q(e,t){for(var n=0,r=e;n<r.length;n+=1){var a=r[n],o=a.weglot.content;if(o&&a.isConnected){for(var i=0,l=o;i<l.length;i+=1){var s=l[i],c=s.original,d=s.properties,f=s.attrSetter,g=s.translations,p=g[t]||c;(t===u.language_from||g[t]&&c!==g[t])&&(d&&(a.weglot.setted=!0,B(a,p,d,c,e)),f&&(a.weglot.setted=!0,f(a,p,c)))}a.wgResolved=!1}}}function B(e,t,n,r,a){if(1===e.nodeType){var o=function(e,t,n){var r=document.createElement("div");return r.innerHTML=e,z(t,r,n)}(t,e,n);return e.innerHTML="",void e.appendChild(o)}if(L(t)&&!L(r)){if(!e.parentNode)return h.warn("Unable to translate some words, <NAME_EMAIL>."),void h.warn(e,{sendToDatadog:!1});if(1===e.parentNode.childNodes.length)return e.parentNode.weglot=e.weglot,void(a?a.push(e.parentNode):B(e.parentNode,t,n,r));var i=e.closest&&e.closest("[data-wg-translation-wrapper]")||e.parentNode.closest("[data-wg-translation-wrapper]");if(!i||i.innerHTML!==t){var l=document.createElement("span");l.dataset.wgTranslationWrapper="",l.weglot=e.weglot,e.parentNode.replaceChild(l,e),a?a.push(l):B(e.parentNode,t,n,r)}}else e.textContent=t}function z(e,t,n){var r=document.createDocumentFragment();if(1!==e.nodeType)return r.appendChild(t),r;for(var a=t.childNodes.length,o=0;o<a;o++){var i,l=t.firstChild;if(i=G(l)){var s=n[i-1];if(!s)continue;var u=s.used?s.child.cloneNode(!0):s.child,c=z(u,l,n);if(c.contains(u))return h.warn("There is an HTML error in the translation of: "+e.innerHTML.toString(),{sendToDatadog:!1}),r;u.innerHTML="",u.appendChild(c),r.appendChild(u),document.createDocumentFragment().appendChild(l),s.used=!0}else r.appendChild(l)}return r}function G(e){if(e&&1===e.nodeType&&e.attributes&&e.attributes[0]){var t=parseInt(e.attributes[0].name.split("wg-")[1]);return isNaN(t)?void 0:t}}function $(e){return{name:e,get:function(t){return t.getAttribute(e)},set:function(t,n){return t.setAttribute(e,n)}}}function V(e,t){if(e.parentNode&&"PICTURE"===e.parentNode.tagName)for(var n=0,r=e.parentNode.children;n<r.length;n+=1){var a=r[n];"SOURCE"===a.tagName&&(a.getAttribute("srcset")&&a.setAttribute("srcset",t))}}function J(e){return e&&e.split&&e.split("www.")[1]||e}function K(e){var t=[{type:1,selectors:["[title]"],attribute:$("title")},{type:2,selectors:["input[type='submit']","input[type='button']","button"],attribute:$("value")},{type:3,selectors:["input[placeholder]","textarea[placeholder]"],attribute:$("placeholder")},{type:4,selectors:["meta[name='description']","meta[property='og:description']","meta[property='og:site_name']","meta[property='og:image:alt']","meta[name='twitter:description']","meta[itemprop='description']","meta[itemprop='name']"],attribute:$("content")},{type:7,selectors:["img"],attribute:$("alt")},{type:8,selectors:["[href$='.pdf']","[href$='.docx']","[href$='.doc']"],attribute:$("href")},{type:9,selectors:["meta[property='og:title']","meta[name='twitter:title']"],attribute:$("content")}];if(!e)return t;if(e.media_enabled&&t.push({type:5,selectors:["youtube.com","youtu.be","vimeo.com","dailymotion.com"].map((function(e){return"iframe[src*='"+e+"']"})),attribute:$("src")},{type:6,selectors:["img","source"],attribute:{name:"src",get:function(e){var t=e.getAttribute("src");if(!t||!t.split)return"";if(0===t.indexOf("data:image"))return"";var n=t.split("?");return n[1]&&(e.queryString=n[1]),n[0]},set:function(e,t,n){var r=e.getAttribute("src"),a=e.getAttribute("srcset");if(t===n){if(e.removeAttribute("data-wgtranslated"),e.isChanged){var o=""+t+(e.queryString?"?"+e.queryString:"");e.setAttribute("src",o),V(e,o),e.hasAttribute("wgsrcset")&&(e.setAttribute("srcset",e.getAttribute("wgsrcset")||e.dataset.srcset),e.removeAttribute("wgsrcset"))}}else r.split("?")[0]!==t&&n!==t&&(e.setAttribute("src",t),V(e,t),e.hasAttribute("srcset")&&(e.setAttribute("wgsrcset",a),e.setAttribute("srcset","")),e.dataset.wgtranslated=!0,e.isChanged=!0)}}},{type:6,selectors:["meta[property='og:image']","meta[property='og:logo']"],attribute:$("content")},{type:6,selectors:["img"],attribute:$("srcset")}),e.translate_aria&&t.push({type:1,selectors:["[aria-label]"],attribute:$("aria-label")}),e.external_enabled){var n=J(function(){var e=window.location,t=e.hostname,n=e.search;if(!f(t)||!n)return t;var r=o();if(r&&r.editorCurrentURL)return new URL(r.editorCurrentURL).hostname;var a=decodeURIComponent(n).match(/url=https?:\/\/([^/]+)/);return a?a[1]:(h.warn("[Weglot] Unable to get current hostname"),t)}());t.push({type:10,selectors:["iframe"],attribute:$("src")},{type:10,selectors:["a[rel=external]"],attribute:$("href")},{type:10,selectors:['[href^="mailto"]'],attribute:$("href")},{type:10,selectors:['[href^="tel"]'],attribute:$("href")},{type:10,selectors:["http:","https:","//"].map((function(e){return'[href^="'+e+'"]:not(link)'})),attribute:{name:"href",get:function(e){if(!e.href||!e.href.split)return"";var t=e.href.split("/")[2];return t&&J(t)!==n?e.getAttribute("href"):""},set:function(e,t){return e.setAttribute("href",t)}}})}if(e.extra_definitions&&e.extra_definitions.length)for(var r=function(){var e=i[a],n=e.type,r=e.selector,o=e.attribute;o&&r?t.push({type:n,selectors:[r],attribute:{name:o,get:function(e){return e.getAttribute(o)},set:function(e,t){return e.setAttribute(o,t)}}}):h.warn("Each extra definition option needs at least {attribute,selector} https://bit.ly/2yDsLxy",{sendToDatadog:!1})},a=0,i=e.extra_definitions;a<i.length;a+=1)r();return t}P.acceptNode=P;var X=[];function Y(e,t){if(!t||!t.translation_engine)throw"translation_engine is required";var n;return Object.assign(u,t),u.document=e,u.mergeNodes=(n=t.translation_engine,r.mergeNodesList.v2.unshift("#text","#comment"),Object.keys(r.mergeNodesList).reduce((function(e,t,a){return n>=a+1&&[].push.apply(e,r.mergeNodesList[t]),e}),[])),Array.isArray(u.extra_merged_selectors)&&(u.selectorMerging=t.extra_merged_selectors.filter((function(e){return e&&"string"==typeof e})).join(",")),t.merged_selectors_remove&&(u.mergedSelectorRemove=t.merged_selectors_remove.map((function(e){return e.value})).join(",")),{getTextNodes:j,setTextNodes:q,definitions:X=K(u)}}function Z(e){"loading"!==document.readyState?e():document.addEventListener("DOMContentLoaded",(function(){return e()}))}var Q,ee={};try{document.cookie,Q=!0}catch(or){Q=!1}ee.set=function(e){var t=e.name,n=e.value,r=e.domain;void 0===r&&(r=null);var a=e.path;void 0===a&&(a=null);var o=e.expires;void 0===o&&(o=null);var i=e.partitioned;void 0===i&&(i=null);var l=e.options;if(Q&&t){var s=l.host,u=l.is_connect,c=l.subdirectory;t=t.replace(/[^+#$&^`|]/g,encodeURIComponent).replace("(","%28").replace(")","%29"),n=n.toString().replace(/[^+#$&/:<-[\]-}]/g,encodeURIComponent),!r&&u&&s&&(r=c?s:s.split("www.").pop());var d=r?";domain="+r:"",f=o?";expires="+o:"",g=";path=/"+(a||""),p=i?";Partitioned":"";document.cookie=t+"="+n+d+g+f+p+";SameSite=None;Secure"}},ee.get=function(e){if(!Q)return null;for(var t=document.cookie.split(";");t.length;){var n=t.pop(),r=n.indexOf("=");if(r=r<0?n.length:r,decodeURIComponent(n.slice(0,r).replace(/^\s+/,""))===e)return decodeURIComponent(n.slice(r+1))}return null},ee.erase=function(e){var t=e.name,n=e.domain;void 0===n&&(n=null);var r=e.path;void 0===r&&(r=null);var a=e.options;ee.set({name:t,value:"",domain:n,path:r,expires:"Thu, 01 Jan 1970 00:00:00 GMT",options:a})};var te=_({service:"js-library"}),ne="wgVeExtension",re="getCurrentLanguage",ae="setCurrentLanguage",oe="languageChanged",ie="library",le="extension",se="https://cdn.weglot.com/projects-settings/",ue="preview.weglot.io",ce="wglang",de="wg-style-trans",fe="data-wg-notranslate",ge="proxy.weglot.com",pe="proxy_internal",ve="proxy_dedicated",_e="wg-translations",he="wg-slugs",me={wordpress:{name:"WordPress",id:1},shopify:{name:"Shopify",id:2},bigcommerce:{name:"BigCommerce",id:3},jimdo:{name:"Jimdo",id:4},squarespace:{name:"Squarespace",id:5},wix:{name:"Wix",id:6},weebly:{name:"Weebly",id:9},drupal:{name:"Drupal",id:10},other:{name:"Other",id:12},webflow:{name:"Webflow",id:13},prestashop:{name:"Prestashop",id:14},magento:{name:"Magento",id:15},squareonline:{name:"Square Online",id:16},bubble:{name:"Bubble",id:17},salesforce:{name:"Salesforce",id:18}},ye=["excluded_blocks","excluded_blocks_remove","dynamics","excluded_paths","dangerously_force_dynamic","extra_definitions","translate_event","whitelist"],we=["polyfillReady","languageChanged","initialized","start","switchersReady"],be={button_style:{full_name:!0,with_name:!0,is_dropdown:!0,with_flags:!1,flag_type:""},switchers:[],auto_switch:!1,auto_switch_fallback:"",excluded_blocks:[],excluded_blocks_remove:[],whitelist:[],translate_event:[{selector:"[data-wg-translate-event]",eventName:null}],customer_tag:!1,order_tag:!0,dynamics:[],excluded_paths:[],wait_transition:!0,hide_switcher:!1,translate_search:!1,media_enabled:!1,search_forms:"",cache:!1,live:!0,loading_bar:!0,search_parameter:"",translation_engine:2,override_hreflang:!0},ke=["none","shiny","square","circle","rectangle_mat"],xe={};!function(e){var t=function(){try{return!!Symbol.iterator}catch(e){return!1}}(),n=function(e){var n={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return t&&(n[Symbol.iterator]=function(){return n}),n},r=function(e){try{return encodeURIComponent(e).replace(/%20/g,"+")}catch(t){return e}},a=function(e){try{return decodeURIComponent(String(e).replace(/\+/g," "))}catch(t){return e}};(function(){try{var t=e.URLSearchParams;return"a=1"===new t("?a=1").toString()&&"function"==typeof t.prototype.set&&"function"==typeof t.prototype.entries}catch(e){return!1}})()||function(){var a=function(e){Object.defineProperty(this,"_entries",{writable:!0,value:{}});var t=typeof e;if("undefined"===t);else if("string"===t)""!==e&&this._fromString(e);else if(e instanceof a){var n=this;e.forEach((function(e,t){n.append(t,e)}))}else{if(null===e||"object"!==t)throw new TypeError("Unsupported input's type for URLSearchParams");if("[object Array]"===Object.prototype.toString.call(e))for(var r=0;r<e.length;r++){var o=e[r];if("[object Array]"!==Object.prototype.toString.call(o)&&2===o.length)throw new TypeError("Expected [string, any] as entry at index "+r+" of URLSearchParams's input");this.append(o[0],o[1])}else for(var i=0,l=Object.keys(e);i<l.length;i+=1){var s=l[i];this.append(s,e[s])}}},o=a.prototype;o.append=function(e,t){e in this._entries?this._entries[e].push(String(t)):this._entries[e]=[String(t)]},o.delete=function(e){delete this._entries[e]},o.get=function(e){return e in this._entries?this._entries[e][0]:null},o.getAll=function(e){return e in this._entries?this._entries[e].slice(0):[]},o.has=function(e){return e in this._entries},o.set=function(e,t){this._entries[e]=[String(t)]},o.forEach=function(e,t){for(var n,r=0,a=Object.keys(this._entries);r<a.length;r+=1){var o=a[r];n=this._entries[o];for(var i=0;i<n.length;i++)e.call(t,n[i],o,this)}},o.keys=function(){var e=[];return this.forEach((function(t,n){e.push(n)})),n(e)},o.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),n(e)},o.entries=function(){var e=[];return this.forEach((function(t,n){e.push([n,t])})),n(e)},t&&(o[Symbol.iterator]=o.entries),o.toString=function(){var e=[];return this.forEach((function(t,n){e.push(r(n)+"="+r(t))})),e.join("&")},e.URLSearchParams=a}();var o=e.URLSearchParams.prototype;"function"!=typeof o.sort&&(o.sort=function(){var e=this,t=[];this.forEach((function(n,r){t.push([r,n]),e._entries||e.delete(r)})),t.sort((function(e,t){return e[0]<t[0]?-1:e[0]>t[0]?1:0})),e._entries&&(e._entries={});for(var n=0;n<t.length;n++)this.append(t[n][0],t[n][1])}),"function"!=typeof o._fromString&&Object.defineProperty(o,"_fromString",{enumerable:!1,configurable:!1,writable:!1,value:function(e){if(this._entries)this._entries={};else{var t=[];this.forEach((function(e,n){t.push(n)}));for(var n=0;n<t.length;n++)this.delete(t[n])}var r,o=(e=e.replace(/^\?/,"")).split("&");for(n=0;n<o.length;n++)r=o[n].split("="),this.append(a(r[0]),r.length>1?a(r[1]):"")}})}(xe),function(e){if(function(){try{var t=new e.URL("b","http://a");return t.pathname="c d","http://a/c%20d"===t.href&&t.searchParams}catch(e){return!1}}()||function(){var t=e.URL,n=function(t,n){"string"!=typeof t&&(t=String(t)),n&&"string"!=typeof n&&(n=String(n));var r,a=document;if(n&&(void 0===e.location||n!==e.location.href)){n=n.toLowerCase(),(r=(a=document.implementation.createHTMLDocument("")).createElement("base")).href=n,a.head.appendChild(r);try{if(0!==r.href.indexOf(n))throw new Error(r.href)}catch(e){throw new Error("URL unable to set base "+n+" due to "+e)}}var o=a.createElement("a");o.href=t,r&&(a.body.appendChild(o),o.href=o.href);var i=a.createElement("input");if(i.type="url",i.value=t,":"===o.protocol||!/:/.test(o.href)||!i.checkValidity()&&!n)throw new TypeError("Invalid URL");Object.defineProperty(this,"_anchorElement",{value:o});var l=new e.URLSearchParams(this.search),s=!0,u=!0,c=this;["append","delete","set"].forEach((function(e){var t=l[e];l[e]=function(){t.apply(l,arguments),s&&(u=!1,c.search=l.toString(),u=!0)}})),Object.defineProperty(this,"searchParams",{value:l,enumerable:!0});var d=void 0;Object.defineProperty(this,"_updateSearchParams",{enumerable:!1,configurable:!1,writable:!1,value:function(){this.search!==d&&(d=this.search,u&&(s=!1,this.searchParams._fromString(this.search),s=!0))}})},r=n.prototype;["hash","host","hostname","port","protocol"].forEach((function(e){!function(e){Object.defineProperty(r,e,{get:function(){return this._anchorElement[e]},set:function(t){this._anchorElement[e]=t},enumerable:!0})}(e)})),Object.defineProperty(r,"search",{get:function(){return this._anchorElement.search},set:function(e){this._anchorElement.search=e,this._updateSearchParams()},enumerable:!0}),Object.defineProperties(r,{toString:{get:function(){var e=this;return function(){return e.href}}},href:{get:function(){return this._anchorElement.href.replace(/\?$/,"")},set:function(e){this._anchorElement.href=e,this._updateSearchParams()},enumerable:!0},pathname:{get:function(){return this._anchorElement.pathname.replace(/(^\/?)/,"/")},set:function(e){this._anchorElement.pathname=e},enumerable:!0},origin:{get:function(){var e={"http:":80,"https:":443,"ftp:":21}[this._anchorElement.protocol],t=this._anchorElement.port!=e&&""!==this._anchorElement.port;return this._anchorElement.protocol+"//"+this._anchorElement.hostname+(t?":"+this._anchorElement.port:"")},enumerable:!0},password:{get:function(){return""},set:function(){},enumerable:!0},username:{get:function(){return""},set:function(){},enumerable:!0}}),n.createObjectURL=function(){return t.createObjectURL.apply(t,arguments)},n.revokeObjectURL=function(){return t.revokeObjectURL.apply(t,arguments)},e.URL=n}(),void 0!==e.location&&!("origin"in e.location)){var t=function(){return e.location.protocol+"//"+e.location.hostname+(e.location.port?":"+e.location.port:"")};try{Object.defineProperty(e.location,"origin",{get:t,enumerable:!0})}catch(n){setInterval((function(){e.location.origin=t()}),100)}}}(xe);var Se=xe.URL,Ee=function(e,t){return function(n,r){if(!n||!n[e]||!r)return t;try{return n[e](r)}catch(e){te.error(e,{consoleOverride:"The CSS selectors that you provided are incorrect: "+r,sendToDatadog:!1})}return t}},Ce=Ee("querySelectorAll",[]),Oe=Ee("querySelector",null),Te=Ee("closest",null);function Ne(e){e=""+e;return["&nbsp;","&amp;","&quot;","&lt;","&gt;"].some((function(t){return-1!==e.indexOf(t)}))?e.replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">"):e}function Le(e){var t=window.location.search.slice(1).split("&").map((function(e){return e&&e.split("=")})).find((function(t){return t[0]===e}));return t&&t[1]}function Ae(){try{if(window.frameElement||window.self!==window.top)return"with-window-top"}catch(e){return"no-window-top"}}var je=function(){return/google|facebook|bing|yahoo|baidu|yandex|lighthouse/i.test(navigator.userAgent)};function Re(e){try{document.createDocumentFragment().querySelector(e)}catch(e){return!1}return!0}var Pe=function(e,t,n){var r=new Se(e,location.href);return r.searchParams.set(t,n),""+r.pathname+r.search},Ie=function(e,t){var n;return void 0===t&&(t=1e3),function(){for(var r=this,a=[],o=arguments.length;o--;)a[o]=arguments[o];clearTimeout(n),n=setTimeout((function(){e.apply(r,a)}),t)}},De=function(e){var t=(new Date).getTime().toString();try{var n=e.contentWindow;return n[t]="asd","asd"===n[t]}catch(e){return!1}};var Me={},Fe=function(e,t){Me[e]||(Me[e]=[]),Me[e].push(t)},We=function(e,t){if(Me[e])for(var n=0,r=Me[e];n<r.length;n+=1){(0,r[n])(t)}};var Ue=[];function He(e,t,n){if(e)return n();qe(t,n,!0)}function qe(e,t,n){return"function"!=typeof t?(te.error("You should provide a callback function as second argument",{sendToDatadog:n}),!1):!n&&we.indexOf(e)<0?(te.error("No Weglot event is named "+e,{sendToDatadog:!1}),!1):(Ue.push({name:e,callback:t,internal:n}),!0)}function Be(e){for(var t=[],n=arguments.length-1;n-- >0;)t[n]=arguments[n+1];for(var r=Ue.filter((function(t){return t.name===e})),a=0,o=r;a<o.length;a+=1){var i=o[a];try{i.callback.apply(i,t)}catch(e){if(i.internal)throw e;te.error("Error triggering callback function: "+e,{sendToDatadog:!1})}}}function ze(){if(!f(window.location.hostname)||!document.baseURI){var e=window.location;return{url:e.href,hostname:e.hostname,pathname:e.pathname,search:e.search}}var t=o();if(t&&t.editorCurrentURL){var n=new Se(t.editorCurrentURL),r=n.hostname,a=n.pathname,i=n.search;return{url:document.baseURI,hostname:r,pathname:a,search:i}}te.error("Problem finding editor page location",{sendToConsole:!1});var l=new Se(document.baseURI),s=l.hostname,u=l.pathname,c=l.search;return{url:document.baseURI,hostname:s,pathname:u,search:c}}He(Lo&&Object.keys(Lo).length>0,"onOptionsReady",(function(){if(Lo.dynamicPushState){var e=history.pushState;history.pushState=function(){for(var t=[],n=arguments.length;n--;)t[n]=arguments[n];e.apply(history,t),Be("onCurrentLocationChanged")}}}));var Ge,$e={};function Ve(e){var t=1;return e.replace(/\((.*?)\)/g,(function(){return"$"+t++}))}function Je(e){void 0===e&&(e=null),e=e||ze().pathname;var t=Lo.localeRules;void 0===t&&(t=[]);var n=Lo.languages,r={position:0,translatedFormat:"CODE",originalFormat:"",addedByDefault:!0},a=Lo.language_from;if(t.length){var o=[];t.map((function(e){var t=e.position,n=e.translatedFormat;n&&"CODE"!==n&&o.push(t||0)}));var i=o.filter((function(e,t,n){return n.indexOf(e)===t})).map((function(e){return Object.assign({},r,{position:e})}));t.unshift.apply(t,i)}else t.push(r);var l=null,s=null,u=t.find((function(t){var r=t.position;void 0===r&&(r=0);var o=t.translatedFormat;void 0===o&&(o="CODE");var i=t.originalFormat;void 0===i&&(i="");var u=t.addedByDefault;if(!o.includes("CODE"))return!1;var c=e.split("/");if(c.length<=r)return!1;var d=c[r+1],f=n.find((function(e){var t=e.custom_code||e.language_to,n=o.replace("CODE",t),r=new RegExp("^"+n+"$","g");return!!r.test(d)&&(s=r,!0)}));if(f)return l=f.custom_code||f.language_to,!0;if(i){var g=i.replace("CODE",a);return new RegExp("^"+g+"$","g").test(d)}return!u}))||r;return $e.convertLocale=function(t,n,r,o){if(void 0===n&&(n=e),void 0===r&&(r=l||a),void 0===o&&(o=null),r===t)return n;var i=u.position;void 0===i&&(i=0);var c=u.originalFormat;void 0===c&&(c="");var d=u.translatedFormat;void 0===d&&(d="CODE");var f=n.split("/");if(f.length<=i)return n;var g=f[i+1];if(r===a){var p=d.replace(/CODE/g,t),v=!1;if(c){var _=c.replace(/CODE/g,a),h=new RegExp("^"+_+"$","g"),m=Ve(p);p=g.replace(h,m),o&&!h.test(g)&&(v=!0,p=o.split("/")[i+1])}var y=c&&!v?2:1;return f.slice(0,i+1).concat([p],f.slice(i+y)).join("/")}if(t===a&&!c)return f.slice(0,i+1).concat(f.slice(i+2)).join("/");var w=Ve((t===a?c:d).replace(/CODE/g,t)),b=g.replace(s,w);return f.slice(0,i+1).concat([b],f.slice(i+2)).join("/")},$e.language=l||a,$e}function Ke(){var e=ze().hostname,t=Lo.languages.find((function(t){return t.connect_host_destination&&t.connect_host_destination.host===e}));return t?t.custom_code||t.language_to:Lo.language_from}function Xe(){return Je().language}function Ye(e){var t=document.documentElement&&document.documentElement.getAttribute("lang");if(t){if(e.includes(t))return t;for(var n in e)if(n.slice(0,2)===t.slice(0,2))return n;return null}}function Ze(){if(Ge)return Ge;if(Lo.is_connect){var e=document.documentElement.dataset.wgTranslated;return e?(Ge=e,e):Ge=Lo.subdirectory?Xe():Ke()}if("WordPress"===Lo.technology_name){var t=Ye(Lo.languages.map((function(e){return e.language_to})));if(t)return t}return Ge=Lo.language_from}function Qe(e,t){var n=t;n||(n=Ze());for(var r=0,a=e;r<a.length;r+=1){var o=a[r];if(!o||!o.dataset||!o.dataset.wgOnlyDisplay)return;o.hidden=o.dataset.wgOnlyDisplay!==n}}qe("onCurrentLocationChanged",(function(){$e={}}),!0);var et={getItem:function(e){return ee.get(e)},setItem:function(e,t,n){void 0===n&&(n={});var r=n.domain,a=n.path,o=n.expires;ee.set({name:e,value:t,domain:r,path:a,expires:o,options:Lo})},removeItem:function(e){return ee.erase({name:e,options:Lo})}},tt={getItem:function(){},setItem:function(){},removeItem:function(){}};function nt(e){void 0===e&&(e={});var t=e.type||"local";try{return"cookie"===t?et:window[t+"Storage"]}catch(e){}return e.type?tt:nt({type:"local"===t?"cookie":"local"})}var rt={slugs:{},version:0,network:void 0};function at(){return new Promise((function(e){for(var t=Lo.languages,n={},r=function(){var r=o[a],i=r.custom_code,l=r.language_to;(function(e){var t=Lo.api_key,n=Lo.versions;if(!n||!n.slugTranslation)return Promise.resolve({});var r="https://cdn-api-weglot.com/translations/slugs?api_key="+t+"&language_to="+e+"&v="+n.slugTranslation;return fetch(r).then((function(e){return e.json()})).then((function(e){return Array.isArray(e)?{}:e})).catch((function(e){return{}}))})(l).then((function(r){n[i||l]=r,Object.keys(n).length===t.length&&e(n)}))},a=0,o=t;a<o.length;a+=1)r()}))}function ot(e){return e?Object.keys(e).reduce((function(t,n){return t[n]=function(e){return Object.keys(e).reduce((function(t,n){return e[n]&&(t.original[n]=e[n],t.translated[e[n]]=n),t}),{original:{},translated:{}})}(e[n]),t}),{}):{}}function it(e){var t=Lo.versions;if(t&&t.slugTranslation){var n=t.slugTranslation;rt.version<n&&(rt.network?rt.network.resolved||rt.network.then((function(t){return e(ot(t))})):rt.network=at().then((function(t){return rt.network.resolved=!0,function(e){var t=Lo.versions,n={version:t?t.slugTranslation:1,slugs:e};try{var r=nt({type:"local"});r&&r.setItem(he,JSON.stringify(n))}catch(e){te.warn(e)}rt=Object.assign({},rt,n)}(t),e(ot(t)),t})).catch((function(){return e({}),{}}))),e(ot(rt.slugs))}else e({})}function lt(e){if(e&&e.toLowerCase){var t=e.toLowerCase(),n=Lo.languages.find((function(e){var n=e.language_to,r=e.custom_code;return n===t||(r?r.toLowerCase()===t:void 0)}));return n?n.language_to:e}}!function(){if(Object.keys(rt.slugs).length)return rt.slugs;try{var e=nt({type:"local"});if(!e)return{};var t=e.getItem(he);t&&(Object.assign(rt,JSON.parse(t)),rt.slugs)}catch(e){return{}}}();var st={};function ut(e,t){return e.split("/").map((function(e){return t[decodeURIComponent(e)]||e})).join("/")}function ct(e,t){(Lo.auto_switch||Lo.geo_auto_switch)&&(Lo.is_tld||Lo.rendered)&&(e===Lo.language_from?t.searchParams.set("no_redirect","true"):t.searchParams.delete("no_redirect"))}function dt(e,t,n){var r;if(void 0===n&&(n=null),n)try{r=new Se(n,window.location.origin)}catch(e){return n}else r=new Se(ze().url),Lo.visual_editor&&r.searchParams.has("url")&&(r=new Se(r.searchParams.get("url"))),r.searchParams.has("lang")&&r.searchParams.delete("lang"),ct(e,r);var a=function(e){if(Lo.subdirectory)return!1;var t=Lo.language_from,n=Lo.host,r=Lo.languages;if(e===t)return n;var a=r.find((function(t){return t.custom_code===e||t.language_to===e}))||{},o=a.connect_host_destination;return o&&o.host}(e);return a&&(r.hostname=a),r.pathname=function(e,t,n,r){if(!Object.keys(e).length)return t;if(st[t]||(st[t]={}),!st[t].originalPath)if(n!==Lo.language_from&&e[n]){var a=e[n].translated;st[t].originalPath=ut(t,a)}else st[t].originalPath=t;return r===Lo.language_from?st[t].originalPath:e[r]&&e[r].original?ut(st[t].originalPath,e[r].original):t}(t,r.pathname,Ze(),e),Lo.subdirectory&&e&&(r.pathname=Je(r.pathname).convertLocale(e,r.pathname)),r.toString()}function ft(e,t){if("WordPress"===Lo.technology_name&&!Lo.is_connect){if(Lo.injectedData&&Lo.injectedData.switcher_links){var n=lt(e);return t(Lo.injectedData.switcher_links[n])}var r=function(e){var t=document.querySelector('link[hreflang*="'+e+'"]');if(!t){var n=e.slice(0,2);t=document.querySelector('link[hreflang*="'+n+'"]')}return t&&t.href}(e);return r?t(r):(te.error("No switcher_links or hreflang elements found.",{sendToDatadog:!1}),t("#"))}if(!Lo.is_connect||!e)return t("#");var a=Lo.dynamicPushState,o=Lo.injectedData;void 0===o&&(o={});var i=o.allLanguageUrls;if(void 0===i&&(i={}),!a&&i&&i[e]){var l=new Se(i[e]);return ct(e,l),t(l.toString())}it((function(n){return t(dt(e,n))}))}var gt={};function pt(){var e=Lo.host;return void 0===e&&(e=window.location.hostname),0===e.indexOf("www.")?e.slice(3):"."+e}function vt(){var e=document.cookie.match(/(^cart=[^;]+|[\W]cart=[^;]+)/g);if(e){var t=e.map((function(e){return decodeURIComponent(e.split("=").slice(1).join("="))}));1!==t.length&&t[0]===t[1]||ee.set({name:"cart",value:t[0],domain:pt(),options:Lo})}else setTimeout(vt,100)}function _t(e){var t="/checkout?locale="+e+(Lo.shopify_skip_shop_pay?"&skip_shop_pay=true":"");fetch(t).then((function(e){document.location.href=encodeURI(e.url)})).catch((function(){document.location.href=encodeURI(t)}))}function ht(e){var t=e||Ze(),n=mt(t),r=[{name:"locale",value:n}].concat(Lo.shopify_skip_shop_pay?[{name:"skip_shop_pay",value:"true"}]:[]);[{name:"action",selector:['form[method="post"][action*="/cart"]','form[method="post"][action*="/checkout"]'],testRegex:/\/(cart|checkout|)\/?(\?|$)/,event:"submit"},{name:"href",selector:['a[href*="/checkout"]','a[href*="/cart/checkout"]'],testRegex:/\/(cart\/)?checkout\/?(\?|$)/,event:"click"}].forEach((function(e){for(var a=e.name,o=e.selector,i=e.testRegex,l=e.event,s=document.querySelectorAll(o.join(",")),u=function(){var e=d[c],o=e.getAttribute(a);if(i.test(o)&&!r.every((function(e){return o.includes(e.name+"="+e.value)}))){for(var s=0,u=r;s<u.length;s+=1){var f=u[s];o=Pe(o,f.name,f.value)}e.setAttribute(a,o),e.wgCheckoutListener&&e.removeEventListener(l,e.wgCheckoutListener),t!==Lo.language_from&&Lo.fix_shopify_checkout_locale&&(e.wgCheckoutListener=function(e){return e.preventDefault(),e.stopPropagation(),Lo.is_connect&&!Lo.subdirectory?(nt({type:"cookie"}).setItem("wg_checkout_redirect",t),document.location.href=encodeURI((Lo.is_https?"https:":"http:")+"//"+Lo.host)):_t(n),!1},e.addEventListener(l,e.wgCheckoutListener))}},c=0,d=s;c<d.length;c+=1)u()}))}function mt(e){var t={br:"pt-BR",no:"nb",pt:"pt-PT",ro:"ro-RO",fl:"fil",sa:"sr-lt",zh:"zh-CN",tw:"zh-TW"};return t[e]?t[e]:e.substr(0,2)}function yt(e){var t,n,r,a=e||Ze(),o=document.getElementById("create_customer")||document.querySelector('form[action="'+(t=a,n="/account",(Lo.is_connect&&t!==Lo.language_from?gt[n]?gt[n]:(it((function(e){var a=e&&e[t]?ut(n,e[t].original):n;r=Lo.subdirectory?Je().convertLocale(t,a,Lo.language_from):a})),gt[n]=r,r):n)+'"]'))||"string"==typeof Lo.customer_tag&&Oe(document,Lo.customer_tag);if(o){var i=document.getElementById("weglot-lang-form");i&&i.parentNode.removeChild(i);var l=document.createElement("input");Object.assign(l,{type:"hidden",id:"weglot-lang-form",name:"customer[tags]",value:"#wg"+a+"#wg"}),o.appendChild(l)}}function wt(e){var t=function(){var e=document.getElementById("shopify-features");if(!e)return null;var t=e.textContent.match(/"shopId":(\d*)/);return t?t[1]:null}();t&&ee.set({name:"checkout_locale",value:mt(e),path:t,options:Lo})}function bt(e){var t=e||Ze();if(!Lo.visual_editor&&!Ae()){var n=Lo.cart_attributes,r=Lo.is_connect,a=Lo.original_shopify_checkout,o=Lo.subdirectory,i=Lo.language_from,l=nt({type:"cookie"}).getItem("cart")&&decodeURIComponent(nt({type:"cookie"}).getItem("cart")),s=nt({type:"session"}).getItem("wg-cart-update-token")&&decodeURIComponent(nt({type:"session"}).getItem("wg-cart-update-token"));if(nt({type:"session"}).getItem("wg-cart-update-lang")!==mt(t)||l!==s){var u=n.map((function(e){return"attributes["+e+"]="+mt(t)})).join("&"),c=fetch("/cart/update.js",{method:"POST",body:u,headers:{"Content-Type":"application/x-www-form-urlencoded"}}).catch((function(){}));!1!==a&&r&&!o&&i===Ze()&&c.then((function(e){return e.json()})).then((function(e){var t=e.token;return ee.set({name:"cart",value:t,domain:pt(),options:Lo})})),nt({type:"session"}).setItem("wg-cart-update-token",l),nt({type:"session"}).setItem("wg-cart-update-lang",mt(t))}for(var d=document.querySelectorAll('a[href*="/cart/"]'),f="attributes[lang]="+t,g=0,p=d;g<p.length;g+=1){var v=p[g],_=v.getAttribute("href");if(_){var h=_.match(/\/cart\/\d+:\d+(\?)?/);h&&(_=_.replace(/&?attributes\[lang\]=([a-zA-Z-]+)/g,""),v.setAttribute("href",_+(h[1]?"&":"?")+f))}}}}function kt(e){if(Lo.language_from!==e){window.Shopify&&(window.Shopify.locale=e,!je()&&Lo.order_tag&&bt(e)),ht(e),wt(e);var t=document.querySelectorAll("[data-wg-only-display]");t.length&&Qe(t,e),Lo.customer_tag&&yt(e)}}qe("onCurrentLocationChanged",(function(){gt={}}),!0);var xt,St={};function Et(e){return{START_WITH:function(t){return 0===e.indexOf(t)},NOT_START_WITH:function(t){return 0!==e.indexOf(t)},END_WITH:function(t){return-1!==e.indexOf(t,e.length-t.length)},NOT_END_WITH:function(t){return-1===e.indexOf(t,e.length-t.length)},CONTAIN:function(t){return-1!==e.indexOf(t)},NOT_CONTAIN:function(t){return-1===e.indexOf(t)},IS_EXACTLY:function(t){return e===t},NOT_IS_EXACTLY:function(t){return e!==t},MATCH_REGEX:function(t){try{return new RegExp(t,"i").test(e)}catch(e){return te.warn(e,{consoleOverride:t+" is an invalid regex",sendToDatadog:!1}),!1}}}}function Ct(e,t){var n=Lo.excluded_paths;if(!n||!n.length)return!1;if(t=function(e){return e||(Lo.injectedData&&Lo.injectedData.originalPath?Lo.injectedData.originalPath:ze().pathname)}(t).toLowerCase(),e=lt(e||Ze()),St[t]&&St[t][e])return St[t][e];St[t]||(St[t]={});var r=function(e,t){var n=Lo.excluded_paths;if("string"==typeof n)return n.split(",").some((function(e){return new RegExp(e,"i").test(t)}))?{allExcluded:!0,language_button_displayed:!0}:null;var r=n.find((function(n){var r=n.type,a=n.value,o=n.excluded_languages;if(!o||!o.length||o.includes(e)){if(Et(t)[r](a))return n;var i=t;try{i=decodeURIComponent(t)}catch(e){return}if(i!==t)return Et(i)[r](a)?n:void 0}}));return r?{language_button_displayed:r.language_button_displayed,allExcluded:!r.excluded_languages.length||r.excluded_languages.length>=Lo.languages.length}:null}(e,t);return St[t][e]=r,r}function Ot(){if(xt)return xt;if(!Lo.api_key)return te.warn("Weglot must be initialized to use it.",{sendToDatadog:!1}),[];var e=(Lo.languages||[]).filter((function(e){var t=Ct(e.language_to),n=!t||t.language_button_displayed;return(!1!==e.enabled||Lo.private_mode)&&n&&(Lo.subdirectory||!Lo.is_connect||e.connect_host_destination&&e.connect_host_destination.created_on_aws)})).map((function(e){return e.custom_code||e.language_to})),t=[Lo.language_from].concat(e);return xt=t.filter((function(e,n){return e&&t.indexOf(e)==n})),e.length||te.log("No public language available.",{sendToDatadog:!1}),xt}function Tt(){var e=nt().getItem(ce);if(e&&Ot().includes(e))return e}qe("onCurrentLocationChanged",(function(){xt=null}),!0);var Nt=function(e){return e&&nt().setItem(ce,e)},Lt=_e+"-v",At=nt({type:"local"}),jt=It(At.getItem(Lt)),Rt=It(At.getItem(_e)),Pt={};function It(e){if(!e)return{};try{var t=JSON.parse(e);return"object"!=typeof t?{}:t}catch(e){return{}}}try{Object.keys(Rt).forEach((function(e){Object.keys(Rt[e]).forEach((function(t){Object.keys(Rt[e][t]).forEach((function(n){if(2===n.length){Pt[e]||(Pt[e]={}),Pt[e][n]||(Pt[e][n]={});var r=Rt[e][t][n];Pt[e][n][r]=t}}))}))}))}catch(or){Object.keys(Rt).forEach((function(e){delete Rt[e]})),Object.keys(jt).forEach((function(e){delete jt[e]})),Ft()}function Dt(e,t){return!(!Pt[Lo.api_key]||!Pt[Lo.api_key][t])&&Pt[Lo.api_key][t][e]}function Mt(e){return Rt[Lo.api_key]&&Rt[Lo.api_key][e]}function Ft(){try{At.setItem(Lt,JSON.stringify(jt)),At.setItem(_e,JSON.stringify(Rt))}catch(e){}}var Wt=[{$schema:"../../schemas/urls.schema.json",title:"Shopify",technology_id:2,urls:[{condition:[{type:"PATH_MATCH",payload:{type:"MATCH_REGEX",value:"^/checkouts/(?:[\\w]{32})(/.*)?$"}}],value:[{original:"^/checkouts/(?:[\\w]{32})(/.*)?$",formatted:"/checkouts$1"},{original:"^/account/(orders|activate)/(?:[\\w]{32})$",formatted:"/account/$1/"},{original:"^/orders/(?:[\\w]{32})$",formatted:"/orders/"},{original:"^/wallets/checkouts/(?:.*)$",formatted:"/wallets/checkouts/"},{original:"^/(.+)\\.(json|xml)$",formatted:"/$1"}]}]}],Ut=[{$schema:"../../schemas/technologies.schema.json",title:"BigCommerce",technology_id:3,settings:[{name:"BigCommerce default settings for all pages",value:[{type:"search_forms",payload:"form[action='/search.php']"},{type:"search_parameter",payload:"search_query"},{type:"dynamics",payload:[{value:".quick-shop-details"},{value:"#QuickViewProductDetails"},{value:".QuickViewModal"},{value:".modalContainer"},{value:".ng-checkout-container"},{value:".previewCartAction"},{value:"#checkout-app"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Bubble",technology_id:17,settings:[{name:"Bubble default settings for javascript integration",condition:[{type:"INTEGRATION",payload:"javascript"}],value:[{type:"dynamics",payload:[{value:".content"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Jimdo",technology_id:4,settings:[{name:"Jimdo default settings for all pages",value:[{type:"excluded_blocks",payload:[{value:'[data-display="cms-only"]'},{value:".j-admin-links"},{value:".cc-m-status-empty"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Magento",technology_id:15,settings:[{name:"Magento default settings for all pages",value:[{type:"excluded_blocks",payload:[{value:".price"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Salesforce",technology_id:18,settings:[{name:"Salesforce default settings for all pages",value:[{type:"ignoreDynamicFragments",payload:!0},{type:"dynamicPushState",payload:!0},{type:"merged_selectors_remove",payload:[{value:".themeProfileMenu"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Shopify",technology_id:2,proxyHosts:["feeds.datafeedwatch.com","feeds.adwordsrobot.com"],settings:[{name:"Shopify default settings for all pages",value:[{type:"extra_definitions",payload:[{type:1,selector:".snize-color-swatch",attribute:"data-sntooltip"},{type:1,selector:"button[data-pf-type=ProductATC]",attribute:"data-soldout"},{type:1,selector:"button[data-pf-type=ProductATC]",attribute:"data-adding"},{type:1,selector:"button[data-pf-type=ProductATC]",attribute:"data-added"}]},{type:"search_forms",payload:"form[action='/pages/search-results'],form[action='/search']"},{type:"search_parameter",payload:"q"},{type:"cart_attributes",payload:["lang","Invoice Language"]},{type:"excluded_blocks",payload:[{value:"input[type='radio']"},{value:".money"},{value:".price"},{value:".product__prices"},{value:"#admin-bar-iframe"},{value:".notranslate"},{value:".skiptranslate"},{value:"#isp_refine_nevigation"},{value:"#isp_header_subtitle"},{value:".isp_sorting_and_result_view_wrapper"},{value:"#isp_results_did_you_mean > span"},{value:".isp_facet_show_hide_values"},{value:"#isp_main_search_box"},{value:".snize-filter-variant-count"},{value:".snize-search-results-header a"},{value:".snize-search-results-header b"},{value:".hc-author__text"},{value:".hc-avatar__initials"},{value:".hc-rating-chart__count"},{value:".hc-rating-chart__percentage-value"},{value:".yotpo-review-date"},{value:".yotpo-user-name"},{value:".yotpo-user-letter"},{value:".yotpo .avg-score"},{value:".yotpo .sr-only"},{value:".yotpo-mandatory-mark"},{value:".price-list"}]},{type:"dynamics",payload:[{value:".shopify-payment-button button"},{value:"#isp_search_result_page_container"},{value:".snize-ac-results"},{value:"#snize_results"},{value:".snize-recommendation"},{value:".snize-modal"},{value:".snize-search-results-header"},{value:"div>span.cc-message"},{value:".hc-widget"},{value:".jdgm-rev-widg__header"},{value:".jdgm-rev__body"},{value:".jdgm-rev-title"},{value:".yotpo-main-widget"},{value:"#swell-popup"},{value:".swell-tab"},{value:".yotpo-widget-override-css"},{value:".cw-row"},{value:".mini-popup-container"},{value:"email-field cw-form-control"},{value:"phone-field cw-form-control"},{value:".sms-policy-text"},{value:".wlo-content-holder"},{value:".wlo-wheel-holder"},{value:".yotpo-smsbump-modal__content"},{value:".cw-compliance-text"},{value:"#saso-notifications"},{value:".saso-cross-sell-popup"},{value:".saso-cart-item-discount-notes"},{value:".saso-cart-item-upsell-notes"},{value:".saso-volume-discount-tiers"},{value:".opw-leading-normal"},{value:".opw-my-2.opw-leading-normal.opw-text-lg.opw-text-left"},{value:".opinew-navbar.opw-flex.opw-items-center.opw-justify-between.opw-flex-wrap.opw-py-4.opw-px-6"},{value:".main-content-container.opw--mx-1"},{value:".opw-text-center.opw-text-sm.opw-border-solid.opw-border-0.opw-mt-3"},{value:".summary-card-container.opw-mx-1"},{value:".opw-reviews-container.opw-mt-3.opw--mx-1"},{value:".opinew-reviews-title.opw-flex.opw-items-center.opw-flex-no-shrink.opw-mr-6"},{value:".opw-flex.opw-flex-row-reverse"},{value:"#opinew-app-container"},{value:".gem_dynamic-content"},{value:".pp_tracking_content"},{value:".pp_all_form_div"},{value:".pp_tracking_result_title"},{value:".progress-bar-style"},{value:".pp_tracking_left"},{value:".pp_num_status_show"},{value:".pp_tracking_status_tips"},{value:".pp_page_map_div"},{value:".pp_tracking_result_parent"},{value:".pp_tracking_right"},{value:".pp_recommend_product_parent"},{value:".currency-converter-cart-note"},{value:".cbb-shipping-rates-calculator"},{value:".cbb-frequently-bought-container"},{value:".cbb-frequently-bought-discount-applied-message"},{value:".cbb-also-bought-container"},{value:"#zonos"},{value:".buddha-menu-item"},{value:".R-GlobalModal"},{value:".ruk-rating-snippet-count"},{value:".R-ContentList-container"},{value:".R-ReviewsList-container"},{value:".R-SliderIndicator-group"},{value:".R-TextBody"},{value:".widgetId-reviewsio-carousel-widget"},{value:".REVIEWSIO-FloatingMinimised"},{value:".REVIEWSIO-FloatingMinimised__Container"},{value:".reviewsio-carousel-widget"},{value:".reviews-io-floating-widget"},{value:".reviews_container"},{value:".site-nav.style--sidebar .site-nav-container .subtitle"},{value:".search-more"},{value:".variant-quantity"},{value:".lion-claimed-rewards-list"},{value:".lion-header"},{value:".lion-header__join-buttons"},{value:".lion-header__join-today"},{value:".lion-history-table"},{value:".lion-integrated-page-section__heading-text"},{value:".lion-loyalty-panel"},{value:".lion-loyalty-splash"},{value:".lion-loyalty-widget"},{value:".lion-modal__content"},{value:".lion-modal__header"},{value:".lion-referral-widget"},{value:".lion-rewards-list"},{value:".lion-rules-list"},{value:".lion-tier-overview"},{value:".ccpops-popup__content__bottom-text"},{value:".ccpops-popup__content__top-text"},{value:".ccpops-trigger__text"},{value:".ks-table-row"},{value:".klaviyo-form"},{value:".sca-fg-today-offer-title"},{value:".sca-fg-today-offer-subtitle"},{value:".sca-offer-info .sca-offer-title"},{value:".sca-fg-tooltip"},{value:".fg-section-title"},{value:".bogos-gift-items .btn-add-to-cart"},{value:".bogos-gift-items .selected-variant"},{value:".sca-hidden-gift-popup .sca-disable-text"},{value:"#sca_fg_cart_message_items_section .content-promotion-message"},{value:".sca-gift-thumbnail-title"},{value:".bogos-bundles-quantity-break-widget-title"},{value:".bogos-bundles-quantity-break-widget-description"},{value:".bogos-bundle-quantity-break-title"},{value:".bogos-bundle-quantity-break-tag"},{value:".bogos-bundle-quantity-break-label"},{value:".bogos-bundles-quantity-break-button-add"},{value:".bogos-bundles-quantity-break-total-title"},{value:".bogos-bundle-quantity-break-item-select-button"},{value:".bogos-bundles-widget-title"},{value:".bogos-bundles-widget-description"},{value:".bogos-bundles-total-title"},{value:".bogos-bundle-item-select-button"},{value:".bogos-bundles-button-add"},{value:".bogos-bundle-item-title"},{value:".bogos-slider-info-title"},{value:".bogos-slider-info-subtitle"},{value:".bogos-slider-offer-title"},{value:".bogos-gift-select-variant option:first-child"},{value:".bogos-badge-title"},{value:".bogos-offer-switch-title"},{value:".fg-gift-thumbnail-offer-title"},{value:".fg-gift-thumbnail-offer-footer-quantity"},{value:".fg-gift-thumbnail-offer-time-title"}]}]},{name:"Shopify additionnal settings for javascript integration",condition:[{type:"INTEGRATION",payload:"javascript"}],value:[{type:"dynamics",payload:[{value:"form.cart.ajaxcart"},{value:"form.cart-drawer"},{value:"#cross-sell"},{value:".wheelio_holder"},{value:".mini-cart"},{value:"#shopify-product-reviews"},{value:"#esc-oos-form"},{value:".product__add-to-cart-button"},{value:"select.product-variants>option:not([value])"},{value:".ui-autocomplete"},{value:".shopify-payment-button__button"},{value:"#shopify-section-static-recently-viewed-products"},{value:"#recently-viewed-products"},{value:"#shopify-section-product-recommendations"},{value:".action_button.add_to_cart"}]}]},{name:"Mollie checkout exclusion",condition:[{type:"PATH_MATCH",payload:{type:"MATCH_REGEX",value:"/checkouts/([\\w]+)/forward"}}],value:[{type:"dynamics",payload:[{value:"#payment-form"}]}]},{name:"Shopify Checkout settings",condition:[{type:"PATH_MATCH",payload:{type:"MATCH_REGEX",value:"^/(d+/checkouts|checkouts/[a-z]{1,2})/(?:w{2}-)?w{32}"}}],value:[{type:"shopifyCheckout",payload:!0}]},{name:"When inside Loox Widget iframe",condition:[[{type:"HOST_MATCH",payload:{type:"IS_EXACTLY",value:"loox.io"}},{type:"IN_FRAME",payload:!0}]],value:[{type:"whitelist",payload:[{value:"main-text"}]}]},{name:"When inside Loox Widget iframe AND proxy integration",condition:[[{type:"HOST_MATCH",payload:{type:"IS_EXACTLY",value:"loox.io"}},{type:"IN_FRAME",payload:!0},{type:"INTEGRATION",payload:"proxy"}]],value:[{type:"dynamicPushState",payload:!0}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Square Online",technology_id:16,settings:[{name:"Square Online default settings for all pages",value:[{type:"dynamics",payload:[{value:".w-container"},{value:".w-wrapper"},{value:".product-header"},{value:".product-messages"},{value:".error"},{value:"button"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Squarespace",technology_id:5,proxyHosts:[],settings:[{name:"Squarespace default settings for all pages",value:[{type:"dynamics",payload:[{value:"#sqs-cart-container"},{value:"#checkout"},{value:".sqs-widgets-confirmation"},{value:".video-player"},{value:".jdgm-widget"},{value:".calendar-block"},{value:".opentable-v2-block"},{value:".blog-item-comments"}]},{type:"excluded_blocks",payload:[{value:"body.sqs-is-page-editing"}]},{type:"merged_selectors_remove",payload:[{value:".plyr__menu__container"},{value:".comment-btn-wrapper"},{value:".product-price .original-price"}]},{type:"extra_definitions",payload:[{type:1,selector:".variant-select-wrapper",attribute:"data-text"}]}]},{name:"Squarespace settings for proxy integration",condition:[{type:"INTEGRATION",payload:"proxy"}],value:[{type:"dynamics",payload:[{value:".sqs-add-to-cart-button.cart-adding"},{value:".sqs-add-to-cart-button.cart-added"}]},{type:"excluded_blocks",payload:[{value:".comment-body"}]}]},{name:"Squarespace settings for javascript integration",condition:[{type:"INTEGRATION",payload:"javascript"}],value:[{type:"dynamics",payload:[{value:"[data-dynamic-strings]"},{value:".sqs-add-to-cart-button"},{value:".variant-select-wrapper"}]},{type:"excluded_blocks",payload:[{value:".animation-segment-wrapper"},{value:".animation-segment-parent-hidden > *"}]}]},{name:"Squarespace settings for cart page",condition:[{type:"DOM_CONTAINS",payload:{selector:"#sqs-cart-root"}}],value:[{type:"excluded_blocks",payload:[{value:"#sqs-cart-container [class*=subtotalPrice]"},{value:"#sqs-cart-container .cart-container .item-price"}]},{type:"force_translation",payload:["#sqs-cart-container"]}]},{name:"Squarespace settings for checkout page",condition:[{type:"DOM_CONTAINS",payload:{selector:"#sqs-standard-checkout"}}],value:[{type:"excluded_blocks",payload:[{value:"#checkout span.money"},{value:"#checkout [data-test*=incomplete] [class^=PaymentCard-container]"},{value:"#checkout [data-test*=incomplete] [class^=CustomerAddress-container]"},{value:"#checkout [class^=CustomerInfoSection-email]"},{value:"#checkout [class^=GoogleResultsList]"}]},{type:"force_translation",payload:["#checkout"]}]},{name:"Squarespace settings for order status page",condition:[{type:"DOM_CONTAINS",payload:{selector:"#order-status-page-root"}}],value:[{type:"excluded_blocks",payload:[{value:"#order-status-page-root #order-number"},{value:"#order-status-page-root h2 + div > p"}]},{type:"force_translation",payload:["#order-status-page-root"]}]},{name:"Disable Connect on the Squarespace site admin area",condition:[{type:"HOST_MATCH",payload:{type:"END_WITH",value:"squarespace.com"}}],value:[{type:"forceDisableConnect",payload:!0}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Webflow",technology_id:13,settings:[{name:"Webflow default settings for all pages",value:[{type:"excluded_blocks",payload:[{value:".wg-element-wrapper"}]}]}]},{$schema:"../../schemas/technologies.schema.json",title:"Wix",technology_id:6,settings:[{name:"Wix default settings for all pages",value:[{type:"dynamicPushState",payload:!0}]},{name:"Wix settings for javascript integration",condition:[{type:"INTEGRATION",payload:"javascript"}],value:[{type:"dynamics",payload:[{value:"#SITE_CONTAINER"}]}]}]}];function Ht(e,t){var n=Ut.find((function(e){return e.technology_id===t}));return n&&n.settings&&n.settings.filter((function(e){return qt(e.condition)})).map((function(t){return t.value.map((function(t){!function(e,t,n){var r=e[t];if(void 0===r)return void(e[t]=n);if(Array.isArray(r)&&Array.isArray(n))return void(e[t]=r.concat(n));if(Array.isArray(n)&&"string"==typeof r)return void(e[t]=n.concat([r.split(",")]));if("object"==typeof r)return void(e[t]=Object.assign({},r,n));e[t]=n}(e,t.type,t.payload)}))})),e}function qt(e){if(!e||0===e.length)return!0;if(!Array.isArray(e))return!1;for(var t=ze(),n=0,r=e;n<r.length;n+=1){var a=r[n];if(Array.isArray(a)){if(a.every((function(e){return Bt(e,t)})))return!0}else if(Bt(a,t))return!0}return!1}function Bt(e,t){if(!e)return!0;var n=e.type,r=e.payload;if(!r&&"boolean"!=typeof r)return!0;if("URI_MATCH"===n&&zt(t.url,r))return!0;if("HOST_MATCH"===n&&zt(t.hostname,r))return!0;if("TRANSLATION_URL_MATCH"===n&&zt(t.url,r))return!0;if("PATH_MATCH"===n&&zt(t.pathname,r))return!0;if("INTEGRATION"===n)return"proxy"===r&&Lo.is_connect||"javascript"===r&&!Lo.is_connect;if("DOM_CONTAINS"===n){var a=r.selector;if(Oe(document,a))return!0}return"IN_FRAME"===n?r===("with-window-top"===Ae()):("object"==typeof r&&"selector"in r&&["XML_ATTRIBUTE_VALUE","XML_ROOT_ELEMENT_NAME"].includes(n),!1)}var zt=function(e,t){var n=t.type,r=t.value;return Et(e=e.toLocaleLowerCase())[n](r)};function Gt(){var e=function(){if(Lo.visual_editor)return new Se(ze().url);var e=Lo.technology_name,t=Lo.injectedData;if(e===me.wix.name)return new Se(window.location.href);if(t&&t.originalCanonicalUrl)try{return new Se(t.originalCanonicalUrl)}catch(e){}var n=document.querySelector("link[rel='canonical'][href]");if(n)try{return new Se(n.href)}catch(e){}return new Se(window.location.href)}();Lo.disable_remove_numeric_slugs||(e.pathname=e.pathname.split("/").filter((function(e){return!e||isNaN(Number(e))})).join("/"));for(var t=0,n=function(){var e;return(e=[]).concat.apply(e,Wt.filter((function(e){return!(t=e.technology_id)||t===Lo.technology_id;var t})).map((function(e){return e.urls})).concat([Lo.definitions&&Lo.definitions.urls||[]])).filter((function(e){return qt(e.condition)}))}();t<n.length;t+=1){var r=n[t].value;try{for(var a=0,o=r;a<o.length;a+=1){var i=o[a],l=i.original,s=i.formatted,u=e.pathname.replace(new RegExp(l),s);if(u!==e.pathname)return e.pathname=u,e.toString()}}catch(e){te.warn(e,{consoleOverride:"Invalid URL regex, "+e.stack})}}return e.toString()}function $t(e){if(400===e.status)throw Error("You reached Weglot limitation. Please upgrade your plan.");if(401===e.status)throw Error("Your Weglot API key seems wrong.");if(e.status>=402)throw Error(e.statusText);return e}function Vt(e,t){void 0===t&&(t={search:!1});var n,r,a=e.l_to,o=e.words,i=Lo.visual_editor||t.search,l=!i&&(n=jt[Lo.api_key],(r=n&&n===Lo.versions.translation)||(delete Rt[Lo.api_key],delete jt[Lo.api_key],delete Pt[Lo.api_key],Ft()),r);if(!o||!o.length)return Promise.resolve({to_words:[],from_words:[]});var s=l&&function(e,t){var n=[],r=[];return{isComplete:e.every((function(e){var a=Mt(e.w);if(a&&a[t])return n.push(a[t]),r.push(Ne(e.w)),!0})),words:{to_words:n,from_words:r}}}(o,a);return s&&s.isComplete?Promise.resolve(s.words):function(e){var t,n=Lo.versions&&Lo.versions.translation||1,r=["api_key="+Lo.api_key,"v="+n],a="https://"+(Lo.bypass_cdn_api?"api.weglot.com":"cdn-api-weglot.com")+"/translate?"+r.join("&"),o=Lo.previewHash?{"weglot-source":"preview"}:{};return fetch(a,{method:"POST",body:(t=JSON.stringify(e),t.replace(/[\u007F-\uFFFF]/g,(function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).substr(-4)}))),headers:o}).then($t).then((function(e){return e.json()})).then((function(e){if(!e||!e.to_words)throw te.warn(e,{sendToDatadog:!1}),Error("An error occurred, please try again later");return e}))}(Object.assign({},e,{request_url:Gt(),l_to:lt(a)})).then((function(e){return i||o.forEach((function(t,n){var r,o,i,l,s,u=e.to_words[n];r=t.w,t.t,o=u,i=a,(s=Mt(r))?s[i]=o:(Rt[Lo.api_key]||(Rt[Lo.api_key]={}),Rt[Lo.api_key][r]=((l={})[i]=o,l)),Pt[Lo.api_key]||(Pt[Lo.api_key]={}),Pt[Lo.api_key][i]||(Pt[Lo.api_key][i]={}),Pt[Lo.api_key][i][o]=r,jt[Lo.api_key]=Lo.versions.translation,Ie(Ft)()})),e}))}function Jt(e,t,n){void 0===n&&(n={});n=Object.assign({},{title:!0,cdn:!1,search:!1},n);var r={l_from:Lo.language_from,l_to:t,words:e};return n.title&&(r.title=document.title),Vt(r,n)}function Kt(e,t){if("string"!=typeof e||"function"!=typeof t)return!1;var n=Ze();return n===Lo.language_from?(t(e),!1):(Vt({l_from:lt(n),l_to:Lo.language_from,words:[{t:2,w:e}]},{search:!0}).then((function(e){return e.to_words[0].toLowerCase().trim()})).then(t),!0)}function Xt(e){return Ne(e).replace(/<[^>]*>/g,"").replace(/[\n\r]+/g,"")}var Yt=[],Zt=new Set,Qt=!1,en=function(e){return Zt.has(Xt(e))},tn=function(e){return Zt.add(Xt(e))};function nn(e,t){void 0===e&&(e=document.documentElement);var n=Ze();return j(e).filter((function(e){return(t||rn)(e)})).map(function(e){return function(t){var n=t.element,r=t.words,a=t.type,o=t.properties,i=t.attrSetter;n.weglot||(n.weglot={content:[]});var l=n.weglot,s={},u=Dt(r,e);if(u&&(s[e]=r,r=u),o){var c=l.content.find((function(e){return e.html}));c?Object.assign(c,{original:r,properties:o,translations:s}):l.content.push({html:!0,original:r,type:a,properties:o,translations:s})}if(i){var d=l.content.find((function(e){return e.attrSetter===i})),f={attrSetter:i,original:r,type:a,translations:s};d?Object.assign(d,f):l.content.push(f)}return n}}(n))}function rn(e){var t=e.element,n=e.words;return!t.weglot||!t.weglot.content||!t.weglot.content.some((function(e){var t,r=e.original,a=e.translations;return r===n||(t=a,Object.keys(t).map((function(e){return t[e]}))).includes(Ne(n))}))}function an(e){for(var t=[],n=0,r=e;n<r.length;n+=1){var a=r[n];-1===Yt.indexOf(a)&&t.push(a)}return[].push.apply(Yt,t),t}function on(e,t){void 0===e&&(e=Yt),void 0===t&&(t={});var n=Lo.prevent_retranslation,r=Lo.injectedData;void 0===r&&(r={});var a=Lo.is_connect;if(n&&a&&!Qt){var o=r.translatedWordsList;void 0===o&&(o=[]),o.forEach((function(e){return tn(e)})),Qt=!0}for(var i=[],l={},s=0,u=e;s<u.length;s+=1)for(var c=u[s],d=c.weglot,f=c.translationLabel,g=0,p=d.content;g<p.length;g+=1){var v=p[g],_=v.original,h=v.type;l[_]||(n&&en(_)||(l[_]=!0,i.push(Object.assign({},{t:h,w:_},t.label||f?{l:[t.label,f].filter(Boolean).map((function(e){return e.slice(0,255)}))}:{}))))}return i}function ln(e,t,n){if(void 0===t&&(t=Ze()),void 0===n&&(n=Yt),e&&e.to_words&&e.to_words.length)for(var r=e.from_words,a=e.to_words,o=0,i=n;o<i.length;o+=1)for(var l=0,s=i[o].weglot.content||{};l<s.length;l+=1){var u=s[l],c=u.original,d=u.translations,f=r.indexOf(Ne(c));if(-1!==f&&!d[t]){var g=(p=a[f])&&p.replace&&p.replace(/wg-(\d+)=""(\s*)\/(\s*)>/g,'wg-$1="">');Lo.prevent_retranslation&&tn(g),d[t]=g}}var p;try{q(n,t)}catch(e){te.error(e)}}function sn(e){var t=Lo.language_from,n=Lo.proxyFormat,r=Lo.api_key,a=Lo.subdirectory,o=Lo.host,i=Lo.languages;if(n===ve)return"https://"+ge+"/"+r+"/"+t+"/"+e+"/";if(a)return"https://"+o+"/"+e+"/"+"wg-cgi/";var l=i.find((function(e){return e.custom_code===Ze()||e.language_to===Ze()}));return l?"https://"+l.connect_host_destination.host+"/"+"wg-cgi/":null}var un=[];function cn(e){var t=e.langTo;void 0===t&&(t=Ze());var n=e.node;void 0===n&&(n=document.documentElement);var r=Lo.proxify_iframes,a=Lo.api_key,o=Lo.language_from;if(r&&r.length&&Array.isArray(r))for(var i=0,l=Ce(n,r.join(","));i<l.length;i+=1){var s=l[i],u=s.src;if(u)if(s.weglot||(s.weglot={}),u.includes(ge+"/")||u.includes("/wg-cgi/"))if(t&&t!==o){var c=new RegExp(a+"/"+o+"/[^/]+/");s.src=u.replace(c,a+"/"+o+"/"+t+"/")}else{var d=(s.weglot||{}).originalFrameSrc;d&&(s.src=d)}else{if(s.weglot.originalFrameSrc||(s.weglot.originalFrameSrc=u),t===o)continue;try{var f=sn(t);f&&(s.src=u.replace(/^https?:\/\//,f))}catch(e){}}}}function dn(e){void 0===e&&(e=Ze()),function(e){void 0===e&&(e=Ze());for(var t={message:"Weglot.setLanguage",payload:e},n=0,r=un;n<r.length;n+=1){var a=r[n];try{a.postMessage(t,"*")}catch(e){te.warn(e)}}}(e),cn({langTo:e})}function fn(e){if(e.data&&"null"!==e.origin){var t=e.data,n=t.message,r=t.payload;if(n){if("Weglot.iframe"===n){var a={message:"Weglot.setLanguage",payload:Ze()};return e.source.postMessage(a,e.origin),void un.push(e.source)}"Weglot.setLanguage"!==n||vo(r)}}}var gn,pn,vn,_n,hn,mn,yn,wn,bn,kn,xn,Sn={},En=[],Cn=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,On=Array.isArray;function Tn(e,t){for(var n in t)e[n]=t[n];return e}function Nn(e){var t=e.parentNode;t&&t.removeChild(e)}function Ln(e,t,n){var r,a,o,i={};for(o in t)"key"==o?r=t[o]:"ref"==o?a=t[o]:i[o]=t[o];if(arguments.length>2&&(i.children=arguments.length>3?gn.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===i[o]&&(i[o]=e.defaultProps[o]);return An(e,i,r,a,null)}function An(e,t,n,r,a){var o={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++vn:a,__i:-1,__u:0};return null==a&&null!=pn.vnode&&pn.vnode(o),o}function jn(e){return e.children}function Rn(e,t){this.props=e,this.context=t}function Pn(e,t){if(null==t)return e.__?Pn(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?Pn(e):null}function In(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return In(e)}}function Dn(e){(!e.__d&&(e.__d=!0)&&_n.push(e)&&!Mn.__r++||hn!==pn.debounceRendering)&&((hn=pn.debounceRendering)||mn)(Mn)}function Mn(){var e,t,n,r,a,o,i,l;for(_n.sort(yn);e=_n.shift();)e.__d&&(t=_n.length,r=void 0,o=(a=(n=e).__v).__e,i=[],l=[],n.__P&&((r=Tn({},a)).__v=a.__v+1,pn.vnode&&pn.vnode(r),Gn(n.__P,r,a,n.__n,n.__P.namespaceURI,32&a.__u?[o]:null,i,null==o?Pn(a):o,!!(32&a.__u),l),r.__v=a.__v,r.__.__k[r.__i]=r,$n(i,r,l),r.__e!=o&&In(r)),_n.length>t&&_n.sort(yn));Mn.__r=0}function Fn(e,t,n,r,a,o,i,l,s,u,c){var d,f,g,p,v,_=r&&r.__k||En,h=t.length;for(n.__d=s,function(e,t,n){var r,a,o,i,l,s=t.length,u=n.length,c=u,d=0;for(e.__k=[],r=0;r<s;r++)i=r+d,null!=(a=e.__k[r]=null==(a=t[r])||"boolean"==typeof a||"function"==typeof a?null:"string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?An(null,a,null,null,null):On(a)?An(jn,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?An(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a)?(a.__=e,a.__b=e.__b+1,l=Hn(a,n,i,c),a.__i=l,o=null,-1!==l&&(c--,(o=n[l])&&(o.__u|=131072)),null==o||null===o.__v?(-1==l&&d--,"function"!=typeof a.type&&(a.__u|=65536)):l!==i&&(l==i-1?d=l-i:l==i+1?d++:l>i?c>s-i?d+=l-i:d--:l<i&&d++,l!==r+d&&(a.__u|=65536))):(o=n[i])&&null==o.key&&o.__e&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=Pn(o)),Kn(o,o,!1),n[i]=null,c--);if(c)for(r=0;r<u;r++)null!=(o=n[r])&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=Pn(o)),Kn(o,o))}(n,t,_),s=n.__d,d=0;d<h;d++)null!=(g=n.__k[d])&&"boolean"!=typeof g&&"function"!=typeof g&&(f=-1===g.__i?Sn:_[g.__i]||Sn,g.__i=d,Gn(e,g,f,a,o,i,l,s,u,c),p=g.__e,g.ref&&f.ref!=g.ref&&(f.ref&&Jn(f.ref,null,g),c.push(g.ref,g.__c||p,g)),null==v&&null!=p&&(v=p),65536&g.__u||f.__k===g.__k?(s&&"string"==typeof g.type&&!e.contains(s)&&(s=Pn(f)),s=Wn(g,s,e)):"function"==typeof g.type&&void 0!==g.__d?s=g.__d:p&&(s=p.nextSibling),g.__d=void 0,g.__u&=-196609);n.__d=s,n.__e=v}function Wn(e,t,n){var r,a;if("function"==typeof e.type){for(r=e.__k,a=0;r&&a<r.length;a++)r[a]&&(r[a].__=e,t=Wn(r[a],t,n));return t}e.__e!=t&&(n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8===t.nodeType);return t}function Un(e,t){return t=t||[],null==e||"boolean"==typeof e||(On(e)?e.some((function(e){Un(e,t)})):t.push(e)),t}function Hn(e,t,n,r){var a=e.key,o=e.type,i=n-1,l=n+1,s=t[n];if(null===s||s&&a==s.key&&o===s.type&&0==(131072&s.__u))return n;if(r>(null!=s&&0==(131072&s.__u)?1:0))for(;i>=0||l<t.length;){if(i>=0){if((s=t[i])&&0==(131072&s.__u)&&a==s.key&&o===s.type)return i;i--}if(l<t.length){if((s=t[l])&&0==(131072&s.__u)&&a==s.key&&o===s.type)return l;l++}}return-1}function qn(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||Cn.test(t)?n:n+"px"}function Bn(e,t,n,r,a){var o;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||qn(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||qn(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r?n.u=r.u:(n.u=wn,e.addEventListener(t,o?kn:bn,o)):e.removeEventListener(t,o?kn:bn,o);else{if("http://www.w3.org/2000/svg"==a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function zn(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=wn++;else if(t.t<n.u)return;return n(pn.event?pn.event(t):t)}}}function Gn(e,t,n,r,a,o,i,l,s,u){var c,d,f,g,p,v,_,h,m,y,w,b,k,x,S,E,C=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(s=!!(32&n.__u),o=[l=t.__e=n.__e]),(c=pn.__b)&&c(t);e:if("function"==typeof C)try{if(h=t.props,m="prototype"in C&&C.prototype.render,y=(c=C.contextType)&&r[c.__c],w=c?y?y.props.value:c.__:r,n.__c?_=(d=t.__c=n.__c).__=d.__E:(m?t.__c=d=new C(h,w):(t.__c=d=new Rn(h,w),d.constructor=C,d.render=Xn),y&&y.sub(d),d.props=h,d.state||(d.state={}),d.context=w,d.__n=r,f=d.__d=!0,d.__h=[],d._sb=[]),m&&null==d.__s&&(d.__s=d.state),m&&null!=C.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=Tn({},d.__s)),Tn(d.__s,C.getDerivedStateFromProps(h,d.__s))),g=d.props,p=d.state,d.__v=t,f)m&&null==C.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),m&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(m&&null==C.getDerivedStateFromProps&&h!==g&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(h,w),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(h,d.__s,w)||t.__v===n.__v)){for(t.__v!==n.__v&&(d.props=h,d.state=d.__s,d.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),b=0;b<d._sb.length;b++)d.__h.push(d._sb[b]);d._sb=[],d.__h.length&&i.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(h,d.__s,w),m&&null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(g,p,v)}))}if(d.context=w,d.props=h,d.__P=e,d.__e=!1,k=pn.__r,x=0,m){for(d.state=d.__s,d.__d=!1,k&&k(t),c=d.render(d.props,d.state,d.context),S=0;S<d._sb.length;S++)d.__h.push(d._sb[S]);d._sb=[]}else do{d.__d=!1,k&&k(t),c=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++x<25);d.state=d.__s,null!=d.getChildContext&&(r=Tn(Tn({},r),d.getChildContext())),m&&!f&&null!=d.getSnapshotBeforeUpdate&&(v=d.getSnapshotBeforeUpdate(g,p)),Fn(e,On(E=null!=c&&c.type===jn&&null==c.key?c.props.children:c)?E:[E],t,n,r,a,o,i,l,s,u),d.base=t.__e,t.__u&=-161,d.__h.length&&i.push(d),_&&(d.__E=d.__=null)}catch(e){t.__v=null,s||null!=o?(t.__e=l,t.__u|=s?160:32,o[o.indexOf(l)]=null):(t.__e=n.__e,t.__k=n.__k),pn.__e(e,t,n)}else null==o&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=Vn(n.__e,t,n,r,a,o,i,s,u);(c=pn.diffed)&&c(t)}function $n(e,t,n){t.__d=void 0;for(var r=0;r<n.length;r++)Jn(n[r],n[++r],n[++r]);pn.__c&&pn.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){pn.__e(e,t.__v)}}))}function Vn(e,t,n,r,a,o,i,l,s){var u,c,d,f,g,p,v,_=n.props,h=t.props,m=t.type;if("svg"===m?a="http://www.w3.org/2000/svg":"math"===m?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=o)for(u=0;u<o.length;u++)if((g=o[u])&&"setAttribute"in g==!!m&&(m?g.localName===m:3===g.nodeType)){e=g,o[u]=null;break}if(null==e){if(null===m)return document.createTextNode(h);e=document.createElementNS(a,m,h.is&&h),o=null,l=!1}if(null===m)_===h||l&&e.data===h||(e.data=h);else{if(o=o&&gn.call(e.childNodes),_=n.props||Sn,!l&&null!=o)for(_={},u=0;u<e.attributes.length;u++)_[(g=e.attributes[u]).name]=g.value;for(u in _)if(g=_[u],"children"==u);else if("dangerouslySetInnerHTML"==u)d=g;else if("key"!==u&&!(u in h)){if("value"==u&&"defaultValue"in h||"checked"==u&&"defaultChecked"in h)continue;Bn(e,u,null,g,a)}for(u in h)g=h[u],"children"==u?f=g:"dangerouslySetInnerHTML"==u?c=g:"value"==u?p=g:"checked"==u?v=g:"key"===u||l&&"function"!=typeof g||_[u]===g||Bn(e,u,g,_[u],a);if(c)l||d&&(c.__html===d.__html||c.__html===e.innerHTML)||(e.innerHTML=c.__html),t.__k=[];else if(d&&(e.innerHTML=""),Fn(e,On(f)?f:[f],t,n,r,"foreignObject"===m?"http://www.w3.org/1999/xhtml":a,o,i,o?o[0]:n.__k&&Pn(n,0),l,s),null!=o)for(u=o.length;u--;)null!=o[u]&&Nn(o[u]);l||(u="value",void 0!==p&&(p!==e[u]||"progress"===m&&!p||"option"===m&&p!==_[u])&&Bn(e,u,p,_[u],a),u="checked",void 0!==v&&v!==e[u]&&Bn(e,u,v,_[u],a))}return e}function Jn(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){pn.__e(e,n)}}function Kn(e,t,n){var r,a;if(pn.unmount&&pn.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||Jn(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){pn.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(a=0;a<r.length;a++)r[a]&&Kn(r[a],t,n||"function"!=typeof e.type);n||null==e.__e||Nn(e.__e),e.__c=e.__=e.__e=e.__d=void 0}function Xn(e,t,n){return this.constructor(e,n)}function Yn(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=Yn(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}gn=En.slice,pn={__e:function(e,t,n,r){for(var a,o,i;t=t.__;)if((a=t.__c)&&!a.__)try{if((o=a.constructor)&&null!=o.getDerivedStateFromError&&(a.setState(o.getDerivedStateFromError(e)),i=a.__d),null!=a.componentDidCatch&&(a.componentDidCatch(e,r||{}),i=a.__d),i)return a.__E=a}catch(t){e=t}throw e}},vn=0,Rn.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Tn({},this.state),"function"==typeof e&&(e=e(Tn({},n),this.props)),e&&Tn(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),Dn(this))},Rn.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Dn(this))},Rn.prototype.render=jn,_n=[],mn="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,yn=function(e,t){return e.__v.__b-t.__v.__b},Mn.__r=0,wn=0,bn=zn(!1),kn=zn(!0),xn=0;var Zn,Qn,er,tr,nr=0,rr=[],ar=pn,or=ar.__b,ir=ar.__r,lr=ar.diffed,sr=ar.__c,ur=ar.unmount,cr=ar.__;function dr(e,t){ar.__h&&ar.__h(Qn,e,nr||t),nr=0;var n=Qn.__H||(Qn.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function fr(e){return nr=1,function(e,t,n){var r=dr(Zn++,2);if(r.t=e,!r.__c&&(r.__=[n?n(t):br(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=Qn,!Qn.u)){var a=function(e,t,n){if(!r.__c.__H)return!0;var a=r.__c.__H.__.filter((function(e){return!!e.__c}));if(a.every((function(e){return!e.__N})))return!o||o.call(this,e,t,n);var i=!1;return a.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(i=!0)}})),!(!i&&r.__c.props===e)&&(!o||o.call(this,e,t,n))};Qn.u=!0;var o=Qn.shouldComponentUpdate,i=Qn.componentWillUpdate;Qn.componentWillUpdate=function(e,t,n){if(this.__e){var r=o;o=void 0,a(e,t,n),o=r}i&&i.call(this,e,t,n)},Qn.shouldComponentUpdate=a}return r.__N||r.__}(br,e)}function gr(e,t){var n=dr(Zn++,3);!ar.__s&&wr(n.__H,t)&&(n.__=e,n.i=t,Qn.__H.__h.push(n))}function pr(e){return nr=5,function(e,t){var n=dr(Zn++,7);return wr(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}((function(){return{current:e}}),[])}function vr(){for(var e;e=rr.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(mr),e.__H.__h.forEach(yr),e.__H.__h=[]}catch(t){e.__H.__h=[],ar.__e(t,e.__v)}}ar.__b=function(e){Qn=null,or&&or(e)},ar.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),cr&&cr(e,t)},ar.__r=function(e){ir&&ir(e),Zn=0;var t=(Qn=e.__c).__H;t&&(er===Qn?(t.__h=[],Qn.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0}))):(t.__h.forEach(mr),t.__h.forEach(yr),t.__h=[],Zn=0)),er=Qn},ar.diffed=function(e){lr&&lr(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==rr.push(t)&&tr===ar.requestAnimationFrame||((tr=ar.requestAnimationFrame)||hr)(vr)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.i=void 0}))),er=Qn=null},ar.__c=function(e,t){t.some((function(e){try{e.__h.forEach(mr),e.__h=e.__h.filter((function(e){return!e.__||yr(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],ar.__e(n,e.__v)}})),sr&&sr(e,t)},ar.unmount=function(e){ur&&ur(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{mr(e)}catch(e){t=e}})),n.__H=void 0,t&&ar.__e(t,n.__v))};var _r="function"==typeof requestAnimationFrame;function hr(e){var t,n=function(){clearTimeout(r),_r&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);_r&&(t=requestAnimationFrame(n))}function mr(e){var t=Qn,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),Qn=t}function yr(e){var t=Qn;e.__c=e.__(),Qn=t}function wr(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function br(e,t){return"function"==typeof t?t(e):t}function kr(e,t){for(var n in t)e[n]=t[n];return e}function xr(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function Sr(e,t){this.props=e,this.context=t}(Sr.prototype=new Rn).isPureReactComponent=!0,Sr.prototype.shouldComponentUpdate=function(e,t){return xr(this.props,e)||xr(this.state,t)};var Er=pn.__b;pn.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),Er&&Er(e)};var Cr="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;var Or=pn.__e;pn.__e=function(e,t,n,r){if(e.then)for(var a,o=t;o=o.__;)if((a=o.__c)&&a.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),a.__c(e,t);Or(e,t,n,r)};var Tr=pn.unmount;function Nr(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=kr({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return Nr(e,t,n)}))),e}function Lr(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return Lr(e,t,n)})),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function Ar(){this.__u=0,this.t=null,this.__b=null}function jr(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Rr(){this.u=null,this.o=null}pn.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Tr&&Tr(e)},(Ar.prototype=new Rn).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var a=jr(r.__v),o=!1,i=function(){o||(o=!0,n.__R=null,a?a(l):l())};n.__R=i;var l=function(){if(!--r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=Lr(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.t.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(i,i)},Ar.prototype.componentWillUnmount=function(){this.t=[]},Ar.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Nr(this.__b,n,r.__O=r.__P)}this.__b=null}var a=t.__a&&Ln(jn,null,e.fallback);return a&&(a.__u&=-33),[Ln(jn,null,t.__a?null:e.children),a]};var Pr=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};(Rr.prototype=new Rn).__a=function(e){var t=this,n=jr(t.__v),r=t.o.get(e);return r[0]++,function(a){var o=function(){t.props.revealOrder?(r.push(a),Pr(t,e,r)):a()};n?n(o):o()}},Rr.prototype.render=function(e){this.u=null,this.o=new Map;var t=Un(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},Rr.prototype.componentDidUpdate=Rr.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){Pr(e,n,t)}))};var Ir="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Dr=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Mr=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Fr=/[A-Z0-9]/g,Wr="undefined"!=typeof document,Ur=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};Rn.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(Rn.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var Hr=pn.event;function qr(){}function Br(){return this.cancelBubble}function zr(){return this.defaultPrevented}pn.event=function(e){return Hr&&(e=Hr(e)),e.persist=qr,e.isPropagationStopped=Br,e.isDefaultPrevented=zr,e.nativeEvent=e};var Gr={enumerable:!1,configurable:!0,get:function(){return this.class}},$r=pn.vnode;pn.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,r={};for(var a in t){var o=t[a];if(!("value"===a&&"defaultValue"in t&&null==o||Wr&&"children"===a&&"noscript"===n||"class"===a||"className"===a)){var i=a.toLowerCase();"defaultValue"===a&&"value"in t&&null==t.value?a="value":"download"===a&&!0===o?o="":"translate"===i&&"no"===o?o=!1:"ondoubleclick"===i?a="ondblclick":"onchange"!==i||"input"!==n&&"textarea"!==n||Ur(t.type)?"onfocus"===i?a="onfocusin":"onblur"===i?a="onfocusout":Mr.test(a)?a=i:-1===n.indexOf("-")&&Dr.test(a)?a=a.replace(Fr,"-$&").toLowerCase():null===o&&(o=void 0):i=a="oninput","oninput"===i&&r[a=i]&&(a="oninputCapture"),r[a]=o}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=Un(t.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==n&&null!=r.defaultValue&&(r.value=Un(t.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),t.class&&!t.className?(r.class=t.class,Object.defineProperty(r,"className",Gr)):(t.className&&!t.class||t.class&&t.className)&&(r.class=r.className=t.className),e.props=r}(e),e.$$typeof=Ir,$r&&$r(e)};var Vr=pn.__r;pn.__r=function(e){Vr&&Vr(e),e.__c};var Jr=pn.diffed;pn.diffed=function(e){Jr&&Jr(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value)};var Kr=function(e,t){var n={__c:t="__cC"+xn++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,Dn(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}({}),Xr=function(e,t){return function(t){var n={},r=n.shouldForwardProp,a=n.label;var o=function(e,t){function n(e){var n=this.props.ref,r=n==e.ref;return!r&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!r:xr(this.props,e)}function r(t){return this.shouldComponentUpdate=n,Ln(e,t)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(function(e){function t(t){var n=kr({},t);return delete n.ref,e(n,t.ref||null)}return t.$$typeof=Cr,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}((function(n,a){var o=n||{},i=o.children,l=o.as;void 0===l&&(l=e);var s=o.style;void 0===s&&(s={});var u=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&-1===t.indexOf(r)&&(n[r]=e[r]);return n}(o,["children","as","style"]),c=u,d=function(e){var t=Qn.context[e.__c],n=dr(Zn++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(Qn)),t.props.value):e.__}(Kr);return Ln(l,Object.assign({},{ref:a,style:Object.assign({},t(Object.assign({},c,{theme:d})),"function"==typeof s?s(Object.assign({},c,{theme:d})):s)},r?function(e,t){return Object.keys(e).filter(t).reduce((function(t,n){return t[n]=e[n],t}),{})}(c,r):c),i)})));return o.displayName=(a||e)+"💅",o}},Yr=Xr("div")((function(){return{display:"flex",alignItems:"center",justifyContent:"center",height:"100vh"}}));function Zr(e){var t=e.displayError;void 0===t&&(t=!0);var n=e.logger;void 0===n&&(n=function(){});var r=e.children,a=function(e){var t=dr(Zn++,10),n=fr();return t.__=e,Qn.componentDidCatch||(Qn.componentDidCatch=function(e,r){t.__&&t.__(e,r),n[1](e)}),[n[0],function(){n[1](void 0)}]}((function(e){return n(e)}));return a[0]&&t?Ln(Yr,null,Ln("p",null,"An error has occurred, we apologise for the inconvenience. ",Ln("br",null),Ln("br",null),"We have been notified and will rectify the situation as soon as possible. ",Ln("br",null),Ln("br",null),"Please try again later <NAME_EMAIL> directly.")):r}var Qr=["text_active","text_inactive","text_hover"],ea=["bg_active","bg_inactive","bg_hover"],ta=[{name:"default",editorDropdown:!0,editableProps:["flag_type","with_flags","is_dropdown","with_name","full_name","invert_flags","open_hover","close_outside_click"],defaultValues:{style:{with_name:!0,with_flags:!0,full_name:!0,is_dropdown:!0,invert_flags:!0,flag_type:"rectangle_mat"},opts:{open_hover:!1,close_outside_click:!1}}},{name:"toggle",editableProps:Qr.concat(ea),defaultValues:{style:{full_name:!1},colors:{bg_active:"#3D46FB",bg_inactive:"transparent",bg_hover:"transparent",text_active:"#FFFFFF",text_inactive:"#000000",text_hover:"#000000"}}},{name:"bubble",editorDropdown:!0,editableProps:["flag_type","with_flags","full_name","color","open_hover","close_outside_click"].concat(Qr),defaultValues:{style:{with_name:!0,with_flags:!0,full_name:!0,flag_type:"rectangle_mat"},opts:{open_hover:!1,close_outside_click:!0},colors:{text_inactive:"#333333",text_active:"#555555",text_hover:"#555555"}}},{name:"vertical_expand",editorDropdown:!0,editableProps:["with_flags","full_name","color","open_hover","close_outside_click"].concat(Qr,ea),defaultValues:{style:{with_name:!0,with_flags:!0,full_name:!1,flag_type:"square"},opts:{open_hover:!0,close_outside_click:!1},colors:{text_active:"#000000",text_inactive:"#000000",text_hover:"#FFFFFF",bg_inactive:"#FFFFFF",bg_active:"#FFFFFF",bg_hover:"#3D46FB"}}},{name:"horizontal_expand",editorDropdown:!1,editableProps:["open_hover","close_outside_click"].concat(Qr,ea),defaultValues:{style:{with_name:!0,with_flags:!1,full_name:!1,flag_type:"square"},opts:{open_hover:!0,close_outside_click:!1},colors:{text_inactive:"#000000",text_active:"#FFFFFF",text_hover:"#FFFFFF",bg_inactive:"#FFFFFF",bg_active:"#3D46FB",bg_hover:"#3D46FB"}}},{name:"underline_edge",editableProps:["full_name"].concat(Qr),maxLanguages:10,minLanguages:null,defaultValues:{style:{full_name:!1},colors:{text_active:"#FA8072",text_inactive:"#333333",text_hover:"#FA8072"}}},{name:"skewed",editorDropdown:!0,editableProps:["with_flags","full_name","open_hover","close_outside_click","bg_active","bg_inactive"].concat(Qr),defaultValues:{style:{with_name:!0,with_flags:!0,full_name:!1,flag_type:"square"},opts:{open_hover:!0,close_outside_click:!1},colors:{text_active:"#000000",text_inactive:"#000000",text_hover:"#3D46FB",bg_inactive:"#FFFFFF",bg_active:"transparent",bg_hover:"#FFFFFF"}}},{name:"underline_full",maxLanguages:10,minLanguages:null,editableProps:["with_flags","flag_type"].concat(Qr),defaultValues:{style:{full_name:!0,with_flags:!0,flag_type:"rectangle_mat"},colors:{text_active:"#333333",text_inactive:"#333333",text_hover:"#3D46FB"}}}].map((function(e){return Object.assign({},e,{defaultValues:Object.assign({},e.defaultValues,{opts:Object.assign({},e.defaultValues.opts,{is_responsive:!1,display_device:"mobile",pixel_cutoff:768}),style:Object.assign({},e.defaultValues.style,{size_scale:1})}),editableProps:e.editableProps.concat(["is_responsive","display_device","pixel_cutoff","size_scale"])})}));function na(e){var t=function(e){return ta.find((function(t){return t.name===e}))}(e),n=t.defaultValues;void 0===n&&(n={});var r=n,a=r.style;void 0===a&&(a={});var o=r.opts;void 0===o&&(o={});var i=r.colors;return void 0===i&&(i={}),{style:a,opts:o,colors:i}}var ra=_({service:"switcher-templates"});var aa={af:{name:"Afrikaans",flag:"za",rtl:!1},am:{name:"አማርኛ",flag:"et",rtl:!1},ar:{name:"العربية‏",flag:"sa",rtl:!0},az:{name:"Azərbaycan dili",flag:"az",rtl:!1},ba:{name:"башҡорт теле",flag:"ru",rtl:!1},be:{name:"Беларуская",flag:"by",rtl:!1},bg:{name:"Български",flag:"bg",rtl:!1},bn:{name:"বাংলা",flag:"bd",rtl:!1},"pt-br":{name:"Português Brasileiro",flag:"br",rtl:!1},bs:{name:"Bosanski",flag:"ba",rtl:!1},ca:{name:"Català",flag:"es-ca",rtl:!1},co:{name:"Corsu",flag:"fr-co",rtl:!1},cs:{name:"Čeština",flag:"cz",rtl:!1},cy:{name:"Cymraeg",flag:"gb-wls",rtl:!1},da:{name:"Dansk",flag:"dk",rtl:!1},de:{name:"Deutsch",flag:"de",rtl:!1},el:{name:"Ελληνικά",flag:"gr",rtl:!1},en:{name:"English",flag:"gb",rtl:!1},eo:{name:"Esperanto",flag:"eo",rtl:!1},es:{name:"Español",flag:"es",rtl:!1},et:{name:"Eesti",flag:"ee",rtl:!1},eu:{name:"Euskara",flag:"eus",rtl:!1},fa:{name:"فارسی",flag:"ir",rtl:!0},fi:{name:"Suomi",flag:"fi",rtl:!1},fj:{name:"Vosa Vakaviti",flag:"fj",rtl:!1},fl:{name:"Filipino",flag:"ph",rtl:!1},fr:{name:"Français",flag:"fr",rtl:!1},fy:{name:"frysk",flag:"nl",rtl:!1},ga:{name:"Gaeilge",flag:"ie",rtl:!1},gd:{name:"Gàidhlig",flag:"gb-sct",rtl:!1},gl:{name:"Galego",flag:"es-ga",rtl:!1},gu:{name:"ગુજરાતી",flag:"in",rtl:!1},ha:{name:"هَوُسَ",flag:"ne",rtl:!1},he:{name:"עברית",flag:"il",rtl:!0},hi:{name:"हिंदी",flag:"in",rtl:!1},hr:{name:"Hrvatski",flag:"hr",rtl:!1},ht:{name:"Kreyòl ayisyen",flag:"ht",rtl:!1},hu:{name:"Magyar",flag:"hu",rtl:!1},hw:{name:"‘Ōlelo Hawai‘i",flag:"hw",rtl:!1},hy:{name:"հայերեն",flag:"am",rtl:!1},id:{name:"Bahasa Indonesia",flag:"id",rtl:!1},ig:{name:"Igbo",flag:"ne",rtl:!1},is:{name:"Íslenska",flag:"is",rtl:!1},it:{name:"Italiano",flag:"it",rtl:!1},ja:{name:"日本語",flag:"jp",rtl:!1},jv:{name:"Wong Jawa",flag:"id",rtl:!1},ka:{name:"ქართული",flag:"ge",rtl:!1},kk:{name:"Қазақша",flag:"kz",rtl:!1},km:{name:"ភាសាខ្មែរ",flag:"kh",rtl:!1},kn:{name:"ಕನ್ನಡ",flag:"in",rtl:!1},ko:{name:"한국어",flag:"kr",rtl:!1},ku:{name:"كوردی",flag:"iq",rtl:!0},ky:{name:"кыргызча",flag:"kg",rtl:!1},la:{name:"Latine",flag:"it",rtl:!1},lb:{name:"Lëtzebuergesch",flag:"lu",rtl:!1},lo:{name:"ພາສາລາວ",flag:"la",rtl:!1},lt:{name:"Lietuvių",flag:"lt",rtl:!1},lv:{name:"Latviešu",flag:"lv",rtl:!1},lg:{name:"Oluganda",flag:"ug",rtl:!1},mg:{name:"Malagasy",flag:"mg",rtl:!1},mi:{name:"te reo Māori",flag:"nz",rtl:!1},mk:{name:"Македонски",flag:"mk",rtl:!1},ml:{name:"മലയാളം",flag:"in",rtl:!1},mn:{name:"Монгол",flag:"mn",rtl:!1},mr:{name:"मराठी",flag:"in",rtl:!1},ms:{name:"Bahasa Melayu",flag:"my",rtl:!1},mt:{name:"Malti",flag:"mt",rtl:!1},my:{name:"မျန္မာစာ",flag:"mm",rtl:!1},ne:{name:"नेपाली",flag:"np",rtl:!1},nl:{name:"Nederlands",flag:"nl",rtl:!1},no:{name:"Norsk",flag:"no",rtl:!1},ny:{name:"chiCheŵa",flag:"mw",rtl:!1},pa:{name:"ਪੰਜਾਬੀ",flag:"in",rtl:!1},pl:{name:"Polski",flag:"pl",rtl:!1},ps:{name:"پښتو",flag:"pk",rtl:!0},pt:{name:"Português",flag:"pt",rtl:!1},ro:{name:"Română",flag:"ro",rtl:!1},ru:{name:"Русский",flag:"ru",rtl:!1},sd:{name:'"سنڌي، سندھی, सिन्धी"',flag:"pk",rtl:!1},si:{name:"සිංහල",flag:"lk",rtl:!1},sk:{name:"Slovenčina",flag:"sk",rtl:!1},sl:{name:"Slovenščina",flag:"si",rtl:!1},sm:{name:'"gagana fa\'a Samoa"',flag:"ws",rtl:!1},sn:{name:"chiShona",flag:"zw",rtl:!1},so:{name:"Soomaaliga",flag:"so",rtl:!1},sq:{name:"Shqip",flag:"al",rtl:!1},sr:{name:"Српски",flag:"rs",rtl:!1},st:{name:"seSotho",flag:"ng",rtl:!1},su:{name:"Sundanese",flag:"sd",rtl:!1},sv:{name:"Svenska",flag:"se",rtl:!1},sw:{name:"Kiswahili",flag:"ke",rtl:!1},ta:{name:"தமிழ்",flag:"in",rtl:!1},te:{name:"తెలుగు",flag:"in",rtl:!1},tg:{name:"Тоҷикӣ",flag:"tj",rtl:!1},th:{name:"ภาษาไทย",flag:"th",rtl:!1},tl:{name:"Tagalog",flag:"ph",rtl:!1},to:{name:"faka-Tonga",flag:"to",rtl:!1},tr:{name:"Türkçe",flag:"tr",rtl:!1},tt:{name:"Tatar",flag:"tr",rtl:!1},"zh-tw":{name:"中文 (繁體)",flag:"tw",rtl:!1},ty:{name:'"te reo Tahiti, te reo Māʼohi"',flag:"pf",rtl:!1},uk:{name:"Українська",flag:"ua",rtl:!1},ur:{name:"اردو",flag:"pk",rtl:!0},uz:{name:'"O\'zbek"',flag:"uz",rtl:!1},vi:{name:"Tiếng Việt",flag:"vn",rtl:!1},xh:{name:"isiXhosa",flag:"za",rtl:!1},yi:{name:"ייִדיש",flag:"il",rtl:!1},yo:{name:"Yorùbá",flag:"ng",rtl:!1},zh:{name:"中文 (简体)",flag:"cn",rtl:!1},zu:{name:"isiZulu",flag:"za",rtl:!1},hmn:{name:"Hmoob",flag:"hmn",rtl:!1},ceb:{name:"Sugbuanon",flag:"ph",rtl:!1},or:{name:"ଓଡ଼ିଆ",flag:"in",rtl:!1},tk:{name:"Türkmen",flag:"tr",rtl:!1},ug:{name:"ئۇيغۇر",flag:"uig",rtl:!0},"fr-ca":{name:"Français (Canada)",flag:"ca",rtl:!1},as:{name:"অসমীয়া",flag:"in",rtl:!1},"sr-latn":{name:"Srpski",flag:"rs",rtl:!1},om:{name:"Afaan Oromoo",flag:"et",rtl:!1},iu:{name:"ᐃᓄᒃᑎᑐᑦ",flag:"ca",rtl:!1},ti:{name:"ቲግሪንያ",flag:"er",rtl:!1},bm:{name:"Bamanankan",flag:"ml",rtl:!1},bo:{name:"བོད་ཡིག",flag:"cn",rtl:!1},ak:{name:"Baoulé",flag:"gh",rtl:!1},rw:{name:"Kinyarwanda",flag:"rw",rtl:!1},kb:{name:"سۆرانی",flag:"iq",rtl:!0},fo:{name:"Føroyskt",flag:"fo",rtl:!1},il:{name:"Ilokano",flag:"ph",rtl:!1},ay:{name:"Aymara",flag:"bo",rtl:!1},dv:{name:"ދިވެހި",flag:"mv",rtl:!0},ee:{name:"Eʋegbe",flag:"gh",rtl:!1},gn:{name:'"Avañe\'ẽ"',flag:"py",rtl:!1},ln:{name:"Lingála",flag:"cd",rtl:!1},qu:{name:"Runa Simi",flag:"pe",rtl:!1},ts:{name:"Xitsonga",flag:"za",rtl:!1},oc:{name:"Occitan",flag:"fr",rtl:!1},nb:{name:"Norsk Bokmål",flag:"no",rtl:!1},yue:{name:"粵語",flag:"hk",rtl:!1},mh:{name:"Kajin M̧ajeļ",flag:"mh",rtl:!1},chk:{name:"Chuuk",flag:"fm",rtl:!1}},oa=[{previous:"cb",new:"ceb"},{previous:"hm",new:"hmn"},{previous:"sa",new:"sr-latn"}];function ia(e){if(!e||!e.toLowerCase)return"Unknown";var t=oa.find((function(t){return t.previous===e}));t&&(e=t.new);var n=e.toLowerCase(),r=Lo.languages.find((function(e){var t=e.language_to,r=e.custom_code;return t===n||(r?r.toLowerCase()===n:void 0)}));return r&&r.custom_local_name?r.custom_local_name:r&&r.custom_name?r.custom_name:n===Lo.language_from&&Lo.language_from_custom_name?Lo.language_from_custom_name:aa[n]?aa[n].name:"Unknown"}function la(e,t){var n=oa.find((function(t){return t.previous===e}));return n&&(e=n.new),t[e]?t[e].flag:""}function sa(e){return function(e,t,n){if(!e||!e.toLowerCase)return"";if(t.language_from===e)return t.language_from_custom_flag||la(e,n);var r=e.toLowerCase(),a=t.languages.find((function(e){var t=e.language_to,n=e.custom_code;return t===r||n&&n.toLowerCase()===r}));return a?a.custom_flag||la(a.language_to,n):""}(e,Lo,aa)}function ua(e,t,n){return t<e?e:t>n?n:t}function ca(e,t){return t&&1!==t?Math.round(e*t*100)/100:e}var da=13,fa=27,ga=38,pa=40,va=32;var _a=["none","shiny","square","circle","rectangle_mat"];function ha(e){return e?e.getBoundingClientRect():{bottom:0,height:0,left:0,right:0,top:0,width:0}}function ma(){var e=fr(function(){if("WordPress"===Lo.technology_name&&Lo.injectedData&&!Lo.is_connect){var e=Lo.injectedData.current_language,t=Lo.languages.find((function(t){return t.language_to===e}));return t&&t.custom_code||e}return Lo.switcher_editor?Lo.language_from:window.Weglot.getCurrentLang()}()),t=e[0],n=e[1];return gr((function(){Lo.is_connect||"WordPress"===Lo.technology_name||Lo.switcher_editor||window.Weglot.on("languageChanged",(function(e){n(e)}))}),[]),[t,n]}function ya(e,t){var n=window.innerWidth>0?window.innerWidth:screen.width,r=t||768;return"mobile"===e?n<=r:n>r}function wa(e,t,n){var r=fr(!1),a=r[0],o=r[1],i=e.style;void 0===i&&(i={});var l=e.colors;return void 0===l&&(l={}),gr((function(){var e=i.size_scale;if(e&&1!==e){var r,a,s,u,c=(r=t({style:i,colors:l}),a=n,s=Lo.button_style&&Lo.button_style.custom_css,u=r.map((function(e){var t=e.selector,n=e.declarations;return["aside.country-selector.weglot_switcher."+a+t+" {",Object.keys(n).map((function(e){return"\t"+e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase()+": "+n[e]+";"})).join("\n"),"}"].join("\n")})).join("\n\n"),!s||Lo.switcher_gallery?u:u+"\n\n"+s);!function(e,t){if(e){var n=document.querySelector("style#weglot-switcher-"+t);if(n)n.textContent=e;else if(document.head){var r=document.createElement("style");r.id="weglot-switcher-"+t,r.textContent=e,document.head.appendChild(r)}}}(c,n),o(!0)}}),[]),a}function ba(e){var t=e.close_outside_click;void 0===t&&(t=!1);var n=e.open_hover;void 0===n&&(n=!1);var r=ma(),a=r[0],o=r[1],i=fr(!1),l=i[0],s=i[1],u=function(e){var t=pr(null);return gr((function(){if(e)return document.addEventListener("mousedown",n),function(){document.removeEventListener("mousedown",n)};function n(n){t.current&&!t.current.contains(n.target)&&e()}}),[t]),t}(t&&!n&&x),c=pr(null),d=Ot().filter((function(e){return e!==a})),g=fr(null),p=g[0],v=g[1],_=fr(!1),h=_[0],m=_[1],y=fr(!1),w=y[0],b=y[1];function k(){var e=ha(u.current),t=e.bottom;void 0===t&&(t=0);var n=e.left;void 0===n&&(n=0),b(t>window.innerHeight/2),m(n>window.innerWidth/2),s(!0)}function x(){s(!1),v(null)}function S(){return l?x():k()}function E(e){if(Lo.switcher_editor)return s(!1);var t;o(e),t=e,"WordPress"===Lo.technology_name&&Lo.injectedData&&!Lo.is_connect?ft(t,(function(e){f(window.location.hostname)?window.dispatchEvent(new CustomEvent("veLanguageChangeUrl",{detail:{targetUrl:e}})):window.location.replace(e)})):window.Weglot.switchTo(t),s(!1)}return gr((function(){p&&c.current.scrollIntoView({block:"center"})}),[p]),{open:l,opensUpward:w,opensLeftward:h,language:a,otherLanguages:d,focusedLanguage:p,switcherContainerRef:u,focusedLanguageRef:c,handleMouseEnter:function(){n&&k()},handleMouseLeave:function(){n&&x()},handleKeyDown:function(e){return e.keyCode===va||e.keyCode===da?(e.preventDefault(),p&&E(p),S()):e.keyCode===pa||e.keyCode===ga?(e.preventDefault(),void function(e){var t=e===pa?"down":"up",n=d.slice(-1).pop(),r=d[0],a=ha(u.current).bottom;void 0===a&&(a=0);var o=a>window.innerHeight/2;if(!p||!l){return v("down"===t?r:n),void(!l&&("up"===t&&o||"down"===t&&!o)&&k())}if(!o&&"up"===t&&p===r||o&&"down"===t&&p===n)return void S();var i="up"===t?-1:1,s=d.indexOf(p)+i;if(s===d.length||-1===s)return;v(d[s])}(e.keyCode)):void(e.keyCode===fa&&l&&(e.preventDefault(),S()))},switchLanguage:E,toggleOpen:S}}var ka=Xr("div")((function(){return{display:"flex",justifyContent:"center"}})),xa=Xr("a")((function(){return{padding:0,height:"auto",zIndex:2,display:"block",textDecoration:"none"}})),Sa=function(e){var t=e.style,n=t&&!t.full_name&&!t.with_flags&&!t.with_name;if(!(1===Lo.category&&5!==Lo.technology_id&&"WordPress"!==Lo.technology_name&&!n&&!Lo.hide_switcher_watermark))return null;var r=Lo.switcher_editor||Lo.visual_editor?void 0:"https://www.weglot.com/?utm_source=free_switcher";return Ln(ka,{className:"wg-switcher-watermark"},Ln(xa,{href:r,rel:"noreferrer",target:"_blank"},"by ",Ln("strong",null,"Weglot")))},Ea={rectangle_mat:{width:30,height:20},shiny:{width:30,height:20},circle:{width:24,height:24},square:{width:24,height:24}},Ca=function(e){var t=e.language,n=e.flagType;void 0===n&&(n="circle");var r=e.size_scale,a=sa(t),o=Ea[n]||{},i=o.width,l=o.height;if(a)return Ln("img",{src:a.indexOf("http")>-1?a:"https://cdn.weglot.com/flags/"+n+"/"+a+".svg",width:ca(i,r),height:ca(l,r),className:"wg-flag",role:"presentation",alt:ia(t)+" flag"})},Oa=function(e){var t=e.styleOpts,n=e.language,r=e.onClick,a=e.legacyFlags,o=e.open;void 0===o&&(o=!1);var i=e.url,l=e.focusedLanguage,s=e.isSelected;void 0===s&&(s=!1);var u=e.focusRef;void 0===u&&(u=null);var c=t.with_name;void 0===c&&(c=!0);var d=t.full_name;void 0===d&&(d=!0);var f=t.with_flags,g=t.size_scale,p=t.flag_type,v=!!l&&n===l,_=d?ia(n):n.toUpperCase(),h=s?"div":"li",m=_a.indexOf(p||"rectangle_mat"),y=f?" wg-flags"+(a?" flag-"+m+" legacy":""):"",w=v&&!s?" focus":"",b=s?" wgcurrent":"";return Ln(h,Object.assign({},{"data-l":n,onClick:function(e){return function(e,t){e.preventDefault(),r(t)}(e,n)},tabIndex:0,className:"wg-li "+n+b+y+w},s?{role:"combobox","aria-activedescendant":l?"weglot-language-"+l:"","aria-label":"Language","aria-expanded":o,"aria-controls":"weglot-listbox"}:{role:"none",id:"wg-"+n}),Ln("a",Object.assign({},s?{target:"_self"}:{role:"option"},{href:i},!c&&{"aria-label":_},v&&!s&&{ref:u},{id:"weglot-language-"+n,tabIndex:-1}),f&&!a&&Ln(Ca,{language:n,flagType:p,size_scale:g}),c&&_))};function Ta(e){var t=e.style.size_scale,n=function(e){return ca(e,t)};return[{selector:".wg-drop ul",declarations:{top:n(38)+"px",bottom:"auto"}},{selector:" .wg-switcher-watermark a",declarations:{fontSize:n(14)+"px",color:"black"}},{selector:" .wg-switcher-watermark a:hover",declarations:{color:"#0077b3"}},{selector:".wg-drop.weg-openup ul",declarations:{bottom:n(38)+"px",top:"auto"}},{selector:" a",declarations:{fontSize:n(13)+"px"}},{selector:".wg-drop a img.wg-flag",declarations:{height:n(30)+"px"}},{selector:".wg-drop .wg-li.wgcurrent",declarations:{height:n(38)+"px",display:"flex",alignItems:"center"}},{selector:".wg-drop a",declarations:{height:n(38)+"px"}},{selector:" .wgcurrent:after",declarations:{height:n(38)+"px",backgroundSize:n(9)+"px"}},{selector:".wg-drop .wgcurrent a",declarations:{paddingRight:ua(22,n(40),40)+"px",paddingLeft:ua(5,n(10),10)+"px"}}]}var Na,La,Aa,ja="default",Ra=function(e,t){return function(n){var r=n||{},a=r.style;void 0===a&&(a={});var o=r.opts;void 0===o&&(o={});var i=r.colors;void 0===i&&(i={});var l=na(t),s=l.style,u=l.opts,c=l.colors,d=document.createElement("div");!function(e,t,n){var r,a,o;pn.__&&pn.__(e,t),r=t.__k,a=[],o=[],Gn(t,e=t.__k=Ln(jn,null,[e]),r||Sn,Sn,t.namespaceURI,r?null:t.firstChild?gn.call(t.childNodes):null,a,r?r.__e:t.firstChild,!1,o),$n(a,e,o)}(Ln(Zr,{logger:ra.error,displayError:!1},Ln(e,{style:Object.assign({},s,a),opts:Object.assign({},u,o),colors:Object.assign({},c,i)})),d),d.classList.add("weglot-container"),window.Weglot||(window.Weglot={}),window.Weglot.switcherId||(window.Weglot.switcherId=1);var f=String(window.Weglot.switcherId++);return d.setAttribute("data-switcher-id",f),d.id="weglot-switcher-"+f,d}}((function(e){var t=e.style,n=e.opts,r=ba(n),a=r.open,o=r.opensUpward,i=r.opensLeftward,l=r.language,s=r.focusedLanguage,u=r.switcherContainerRef,c=r.focusedLanguageRef,d=r.handleMouseEnter,f=r.handleMouseLeave,g=r.handleKeyDown,p=r.switchLanguage,v=r.toggleOpen,_=function(){var e=Ot(),t=fr(e.reduce((function(e,t){var n;return Object.assign({},e,((n={})[t]="",n))}),{})),n=t[0],r=t[1];return gr((function(){Promise.all(e.map((function(e){return new Promise((function(t){return ft(e,(function(n){return t({l:e,url:n})}))}))}))).then((function(e){return r(e.reduce((function(e,t){var n,r=t.l,a=t.url;return Object.assign({},e,((n={})[r]=a,n))}),{}))}))}),[]),n}(),h=function(e){var t=e.is_responsive,n=e.display_device,r=e.pixel_cutoff,a=fr(!t||ya(n,r)),o=a[0],i=a[1],l=function(){return i(ya(n,r))};return gr((function(){if(t)return window.addEventListener("resize",l),function(){window.removeEventListener("resize",l)}}),[t,n,r]),o}(n);wa({style:t},Ta,ja);var m=pr(null),y=fr(0),w=y[0],b=y[1];gr((function(){m&&a&&b(m.current.offsetWidth)}),[a,m]);var k=t.is_dropdown,x=t.invert_flags,S=k||x,E=Ot().filter((function(e){return!S||e!==l})),C=Lo.button_style&&/background-position/i.test(Lo.button_style.custom_css)&&!Lo.languages.some((function(e){return e.custom_flag})),O=function(){for(var e,t,n=arguments,r=0,a="",o=arguments.length;r<o;r++)(e=n[r])&&(t=Yn(e))&&(a&&(a+=" "),a+=t);return a}({open:a,closed:!a,"wg-drop":k,"wg-list":!k,"weg-openup":o&&a,"weg-openleft":i&&a});return h?Ln("aside",{ref:u,"data-wg-notranslate":!0,style:a&&u&&u.current&&u.current.offsetWidth<w&&{width:w},onKeyDown:g,onMouseEnter:d,onMouseLeave:f,className:"weglot_switcher country-selector default "+O,"aria-label":"Language selected: "+ia(l)},S&&Ln(Oa,{styleOpts:t,open:a,focusedLanguage:s,language:l,isSelected:!0,onClick:v,legacyFlags:C,url:"#"}),Ln("ul",{ref:m,role:"listbox",id:"weglot-listbox","aria-label":"Language list",style:!a&&t.is_dropdown&&{display:"none"}},E.map((function(e){return Ln(Oa,{language:e,url:e===l?"#":_[e],onClick:p,isSelected:e===l,focusedLanguage:s,key:"wg-"+e,focusRef:c,styleOpts:t,legacyFlags:C})})),Ln(Sa,{style:t}))):Ln(jn,null)}),ja);function Pa(e,t){var n=e.name,r=e.hash;if(void 0===r&&(r=null),function(e,t){try{return e.querySelector(t)}catch(e){return null}}(document.documentElement,"script#weglot-switcher-"+n))return!1;var a=r?n+"."+r:n,o=document.getElementsByTagName("head")[0]||document.documentElement,i=document.createElement("script");return i.type="text/javascript",i.src="https://cdn.weglot.com/switchers/"+a+".min.js",i.id="weglot-switcher-"+n,o.insertBefore(i,o.firstChild),!0}var Ia=[];function Da(e,t){if(void 0===t&&(t=document.documentElement),e&&!e.ready){var n=e.style||Lo.button_style,r=e.location;void 0===r&&(r={});var a=function(e,t){void 0===e&&(e={});var n=e.target,r=e.sibling;if(!n)return{defaultPosition:!0};var a=Ce(t,n);if(!a.length)return{error:Re(n)?"The provided target is not on this page.":"The provided target is not a valid CSS selector."};var o=Ce(t,r);if(!r||!o.length)return{targetNode:a[0],siblingNode:null};var i=Array.from(a),l=Array.from(o),s=null,u=l.find((function(e){return s=i.find((function(t){return e.parentNode===t})),!!s}));return u&&s?{targetNode:s,siblingNode:u}:{error:"The provided sibling selector does not belong to target element."}}(r,t),o=a.error,i=a.targetNode,s=a.siblingNode,u=a.defaultPosition;if(!o){var c=Ra(Object.assign({},e,{style:n}));if(c.weglotSwitcher=e,Ia.push(c),u)return c.classList.add("wg-default"),document.body.appendChild(c),e.ready=!0,c;c.setAttribute("data-switcher-style-opt",JSON.stringify(n)),i.insertBefore(c,s),e.ready=!0;for(var d=0,f=t.querySelectorAll(".weglot-container:empty");d<f.length;d+=1){l(f[d])}return c}te.warn(o,{sendToDatadog:!1})}}function Ma(){Na||Be("switchersReady",Ze()),Na=!0,clearTimeout(Aa),La&&La.parentNode.removeChild(La)}function Fa(e){if(void 0===e&&(e=document),!(Ot().length<2||Lo.hide_switcher||Lo.switcher_editor)){var t=e.isConnected?e:document;wo(t)&&Ma();var n=t.querySelectorAll("#weglot_here:not(.weglot-container),.weglot_here:not(.weglot-container)");if(n.length){for(var r=0,a=n;r<a.length;r+=1){var o=a[r],i=Ra({style:Lo.button_style});i.classList.add("weglot_here"),o.parentNode.insertBefore(i,o),l(o)}Ma()}for(var s=0,u=Lo.switchers;s<u.length;s+=1){var c=u[s];if(!c.default){var d=c.template;if(d&&"default"!==d.name){if(d.name){if(!Pa(d)){var f=window.Weglot.switchers&&window.Weglot.switchers[d.name];f&&f.addSwitchers(t)}Ma()}}else Da(c,t)&&Ma()}}if(!Na&&!La){var g=Lo.switchers.find((function(e){return e.default}))||{style:Lo.button_style};Aa=setTimeout((function(){La=Da(g),Be("switchersReady",Ze())}))}}}qe("onCurrentLocationChanged",(function(){Ia.forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),Ia.splice(0),function(){for(var e=window.Weglot.switchers||{},t=0,n=Object.keys(e);t<n.length;t+=1)e[n[t]].removeSwitchers()}(),Na=null,La=null,Lo.button_style.ready=!1,Lo.switchers.map((function(e){return e.ready=!1})),Fa()}),!0);var Wa=0;function Ua(){var e=["name","value"];Lo.translate_event.forEach((function(t){for(var n=Ce(document.body,t.selector),r=function(){var n=o[a];if(n.alreadyListeningEventInput)return!n.alreadyListeningEventInput.isConnected&&Wa<10&&(Wa++,n.parentNode.insertBefore(n.alreadyListeningEventInput,n.nextSibling)),{};var r=n.cloneNode(!0);if(!r)return{};r.name="",n.alreadyListeningEventInput=r,n.parentNode.insertBefore(r,n.nextSibling),n.style.display="none",new MutationObserver((function(t){for(var a=0,o=t;a<o.length;a+=1){var i=o[a],l=n.getAttribute(i.attributeName);e.includes(i.attributeName)&&r.setAttribute(i.attributeName,l)}})).observe(n,{attributes:!0});var i=Ie((function(e){13===e.keyCode&&e.target.form?e.target.form.dispatchEvent(new Event("submit")):Kt(e.target.value,(function(e){Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set.call(n,e);var r=t.eventName||n.getAttribute("data-wg-translate-event"),a=document.createEvent("HTMLEvents");a.initEvent("focus",!0,!1),n.dispatchEvent(a),a.initEvent(r,!0,!1),n.dispatchEvent(a)}))}),400);r.addEventListener("keydown",i)},a=0,o=n;a<o.length;a+=1){var i=r();if(i)return i.v}}))}try{var Ha=Element.prototype.attachShadow;Element.prototype.attachShadow=function(e){var t=Ha.call(this,e);return qa([this]),t}}catch(Ln){}function qa(e){if(Lo.translate_shadow_roots&&e)for(var t=0,n=e;t<n.length;t+=1){var r=n[t];if(r.shadowRoot&&!r.shadowRoot.wgTranslated){r.shadowRoot.wgTranslated=!0,Za(r.shadowRoot);var a=nn(r.shadowRoot);a.length&&(an(a),to(a.filter((function(e){return Qa(e)}))))}}}var Ba,za=null,Ga=[],$a=[fe,"class","id"],Va=[],Ja=[];function Ka(e,t){Ba&&clearTimeout(Ba);for(var n=0,r=t;n<r.length;n+=1){var a=r[n];1===a.nodeType&&Ga.push(a)}Ga.length&&(Ba=setTimeout((function(){Fa(e),Ua(),Lo.is_connect&&function(e){if(Ze()===Lo.language_from)return;for(var t=0,n=e;t<n.length;t+=1)for(var r=n[t],a=Ce(r,"[href]"),o=function(){var e=l[i];if(!E(e)){var t=e.getAttribute("href");if(t&&t.includes("//"+Lo.host)){var n=t.split("//"+Lo.host)[1];void 0===n&&(n="/"),Ct(void 0,n)||it((function(n){e.setAttribute("href",dt(Ze(),n,t))}))}}},i=0,l=a;i<l.length;i+=1)o()}(Ga),Lo.proxify_iframes&&Lo.proxify_iframes.length&&Ga.forEach((function(e){return cn({node:e})})),qa(Ga),We("onDynamicDetected"),Ga=[]}),100))}function Xa(e,t){var n=Lo.dynamics,r=Qa;t!==document&&!s(t)?r=function(){return!0}:n&&0!==n.length||(r=function(){return!1});try{if(Ct())return;if(e=function(e,t){var n=[],r=e.filter((function(e){var r=e.addedNodes,a=e.type,o=e.target;W(o),"attributes"===a&&(wo(o),function(e){"IMG"===e.nodeName&&e.srcset&&e.dataset.wgtranslated&&(e.setAttribute("wgsrcset",e.srcset),e.srcset="")}(o));var i=function(e){do{if(e.weglot&&e.weglot.setted)return e;e=e.parentElement||e.parentNode}while(e)}(o);return i?(n.push(i),!1):r.length?(setTimeout((function(){return Ka(o,r)})),!za||!o||!Te(o,za)):!$a.includes(e.attributeName)&&t(o)&&("characterData"===a||"attributes"===a)}));if(n.length)for(var a=0,o=n;a<o.length;a+=1){o[a].weglot.setted=!1}return r}(e,r),!n||0===n.length)return;if(e.length)try{!function(e,t,n){void 0===n&&(n=!0);for(var r=[],a=function(e){var n=e.outerHTML||e.textContent;if(e.wgParsedHTML!==n){e.wgParsedHTML=n;for(var a=nn(e,(function(e){var n=e.element;return!function(e){if(e.weglot&&e.weglot.dynamic>20){if(e.wgBypassDynamicLimit)return!1;if(Lo.dangerously_bypass_dynamic_limit){var t=e.closest?e:e.parentNode;if(Te(t,Lo.dangerously_bypass_dynamic_limit))return e.wgBypassDynamicLimit=!0,!1}return!0}return!1}(n)&&t(n)})),o=0,i=a;o<i.length;o+=1){var l=i[o];Lo.ignoreDynamicFragments&&!document.body.contains(l)||(l.weglot.dynamic||(l.weglot.dynamic=0),l.weglot.dynamic++,r.push(l))}return null}},o=[],i=0,l=e;i<l.length;i+=1){var s=l[i],u=s.type,c=s.target,d=s.addedNodes;switch(u){case"attributes":case"characterData":if(o.includes(c))break;o.push(c),a(c);break;case"childList":var f=d.length>1?c:d[0];if(o.includes(f))break;if(a(f),o.push(f),!n)break;for(var g=0,p=d;g<p.length;g+=1){var v=p[g],_=[];"IFRAME"===v.tagName?_=[v]:v.querySelectorAll&&(_=v.querySelectorAll("iframe"));for(var h=0;h<_.length;h++){var m=_[h];t(m)&&De(m)&&!E(m)&&(a(m.contentWindow.document),Za(m.contentWindow.document))}}}}r.length&&(an(r),to(r))}(e,r)}catch(e){te.warn(e)}}catch(e){te.warn(e,{consoleOverride:"Error in MutationObserver"})}}var Ya=!1;function Za(e){var t=e!==document?e:e.body||e,n=new MutationObserver((function(t){var n;if(Ya)Xa(t,e);else{var r=Va.find((function(t){return t.observedDocument===e}));r?(n=r.mutations).push.apply(n,t):Va.push({observedDocument:e,mutations:[].concat(t)})}}));n.observe(t,{childList:!0,subtree:!0,characterData:!0,attributes:!0}),Ja.push(n)}function Qa(e){if(!Lo.dynamics||0===Lo.dynamics.length||!e)return!1;if(!e.closest)return Qa(e.parentNode);var t=!!Te(e,Lo.dynamics.map((function(e){return e.value})).join(", "));return!t&&e.getRootNode&&e.getRootNode().host?Qa(e.getRootNode().host):t}var eo={times:[],timeout:null,nodes:[]};function to(e){void 0===e&&(e=[]),clearTimeout(eo.timeout);var t=function(e){if(e&&!e.closest&&(e=e.parentNode),!e)return"unknown";for(var n=0,r=Lo.dynamics;n<r.length;n+=1){var a=r[n];if(Te(e,a.value))return a.value}return e.getRootNode&&e.getRootNode()&&e.getRootNode().host?t(e.getRootNode().host):"unknown"},n=Ze();if(n!==Lo.language_from){if(eo.times=eo.times.filter((function(e){return e>Date.now()-1e3})),eo.times.length&&(eo.timeout||eo.times.length>=10))return eo.nodes=eo.nodes.concat(e),void(eo.timeout=setTimeout((function(){return to()}),1e3));e.forEach((function(e){e.translationLabel="dynamic-selector: "+t(e)})),eo.timeout=null,eo.times.push(Date.now());var r=eo.nodes.concat(e);return eo.nodes=[],Jt(on(r,{label:"Dynamic"}),n,{title:!1,cdn:!0,nodes:r}).then((function(e){return ln(e,n,r)}))}}var no=[{codes:["no"],pattern:/^(nn|nb)(-[a-z]+)?$/i},{codes:["zh"],pattern:/^zh(-hans(-\w{2})?)?(-(cn|sg))?$/i},{codes:["tw","zh-TW"],pattern:/^zh-(hant)?-?(tw|hk|mo)?$/i},{codes:["br"],pattern:/^pt-br$/i},{codes:["fl"],pattern:/^fil$/i}];function ro(e){void 0===e&&(e=Ot());for(var t={},n={},r=0,a=e;r<a.length;r+=1){var o=a[r],i=o.toLowerCase(),l=i.substring(0,2);t[l]||(t[l]=[]),t[l].push(i),n[i]=o}for(var s=0,u=navigator.languages||[navigator.language];s<u.length;s+=1){var c=u[s],d=c.toLowerCase(),f=d.substring(0,2);if(n[d])return n[d];for(var g=0,p=no;g<p.length;g+=1){var v=p[g],_=v.codes,h=v.pattern,m=_.find((function(t){return e.includes(t)}));if(m&&h.test(c))return m}if(t[f]){if("zh"===f)continue;var y=t[f].indexOf(f);return y>=0?n[t[f][y]]:n[t[f].shift()]}}}function ao(){if(window.location.search.indexOf("no_redirect=true")>-1)oo(Ze());else if(!(!Lo.auto_switch||Lo.subdirectory&&Lo.injectedData||nt({type:"cookie"}).getItem("WG_CHOOSE_ORIGINAL")||je()||Lo.visual_editor)){var e=ro();return e&&!Ct(e)?e:Lo.auto_switch_fallback&&!Ct(Lo.auto_switch_fallback)?Lo.auto_switch_fallback:void 0}}function oo(e){if(e===Lo.language_from){var t=new Date;t.setTime(t.getTime()+2592e6),nt({type:"cookie"}).setItem("WG_CHOOSE_ORIGINAL","1",{expires:t.toUTCString()})}else nt({type:"cookie"}).removeItem("WG_CHOOSE_ORIGINAL")}function io(){l(a(de))}function lo(){l(a("wg_progress"))}var so=!1;function uo(e){window.postMessage({name:ne,data:e,target:le},"*")}qe("languageChanged",(function(e){so&&uo({type:oe,payload:e})}));var co=function(){var e=function(e,t){return Array.prototype.slice.call(e,t)},t=null;"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?t=self:"undefined"!=typeof global?t=global:window&&(t=window);var n=t,r=t.document,a=["load","loadend","loadstart"],o=["progress","abort","error","timeout"],i=function(e){return["returnValue","totalSize","position"].includes(e)},l=function(e,t){for(var n in e)if(!i(n)){var r=e[n];try{t[n]=r}catch(e){}}return t},s=function(e,t,n){for(var r=function(e){return function(r){var a={};for(var o in r)if(!i(o)){var l=r[o];a[o]=l===t?n:l}return n.dispatchEvent(e,a)}},a=0,o=Array.from(e);a<o.length;a+=1){var l=o[a];n._has(l)&&(t["on"+l]=r(l))}},u=function(e){if(r&&null!=r.createEventObject){var t=r.createEventObject();return t.type=e,t}try{return new Event(e)}catch(t){return{type:e}}},c=function(t){var n={},r=function(e){return n[e]||[]},a={addEventListener:function(e,t,a){n[e]=r(e),n[e].indexOf(t)>=0||(a=void 0===a?n[e].length:a,n[e].splice(a,0,t))},removeEventListener:function(e,t){if(void 0!==e){void 0===t&&(n[e]=[]);var a=r(e).indexOf(t);-1!==a&&r(e).splice(a,1)}else n={}},dispatchEvent:function(){var n=e(arguments),o=n.shift();t||(n[0]=l(n[0],u(o)),Object.defineProperty(n[0],"target",{writable:!1,value:this}));var i=a["on"+o];i&&i.apply(a,n);for(var s=r(o).concat(r("*")),c=0;c<s.length;c++)s[c].apply(a,n)},_has:function(e){return!(!n[e]&&!a["on"+e])}};return t&&(a.listeners=function(t){return e(r(t))},a.on=a.addEventListener,a.off=a.removeEventListener,a.fire=a.dispatchEvent,a.once=function(e,t){var n=function(){return a.off(e,n),t.apply(null,arguments)};return a.on(e,n)},a.destroy=function(){return n={}}),a},d=function(e,t){switch(typeof e){case"object":return n=e,Object.entries(n).map((function(e){var t=e[0],n=e[1];return t.toLowerCase()+": "+n})).join("\r\n");case"string":return function(e,t){null==t&&(t={});for(var n=0,r=e.split("\r\n");n<r.length;n+=1){var a=r[n];if(/([^:]+):\s*(.+)/.test(a)){var o=null!=RegExp.$1?RegExp.$1.toLowerCase():void 0,i=RegExp.$2;null==t[o]&&(t[o]=i)}}return t}(e,t)}var n;return[]},f=c(!0),g=function(e){return void 0===e?null:e},p=n.XMLHttpRequest,v=function(){var e,t,n,r=new p,i={},u=null,v=0,_=function(){if(n.status=u||r.status,-1!==u&&(n.statusText=r.statusText),-1!==u);else{var e=d(r.getAllResponseHeaders());for(var t in e){var a=e[t];if(!n.headers[t]){var o=t.toLowerCase();n.headers[o]=a}}}},h=function(){b.status=n.status,b.statusText=n.statusText},m=function(){e||b.dispatchEvent("load",{}),b.dispatchEvent("loadend",{}),e&&(b.readyState=0)},y=function(e){for(;e>v&&v<4;)b.readyState=++v,1===v&&b.dispatchEvent("loadstart",{}),2===v&&h(),4===v&&(h(),"text"in n&&(b.responseText=n.text),"xml"in n&&(b.responseXML=n.xml),"data"in n&&(b.response=n.data),"finalUrl"in n&&(b.responseURL=n.finalUrl)),b.dispatchEvent("readystatechange",{}),4===v&&(!1===i.async?m():setTimeout(m,0))},w=function(e){if(4===e){var t=f.listeners("after"),r=function(){if(t.length>0){var e=t.shift();2===e.length?(e(i,n),r()):3===e.length&&i.async?e(i,n,r):r()}else y(4)};r()}else y(e)},b=c();i.xhr=b,r.onreadystatechange=function(){try{2===r.readyState&&_()}catch(e){}4===r.readyState&&(t=!1,_(),function(){if(r.responseType&&"text"!==r.responseType)"document"===r.responseType?(n.xml=r.responseXML,n.data=r.responseXML):n.data=r.response;else{n.text=r.responseText,n.data=r.responseText;try{n.xml=r.responseXML}catch(e){}}"responseURL"in r&&(n.finalUrl=r.responseURL)}()),w(r.readyState)};var k=function(){e=!0};b.addEventListener("error",k),b.addEventListener("timeout",k),b.addEventListener("abort",k),b.addEventListener("progress",(function(){v<3?w(3):r.readyState<=3&&b.dispatchEvent("readystatechange",{})})),"withCredentials"in r&&(b.withCredentials=!1),b.status=0;for(var x=0,S=Array.from(o.concat(a));x<S.length;x+=1){var E=S[x];b["on"+E]=null}if(b.open=function(r,a,o,l,s){v=0,e=!1,t=!1,i.headers={},i.headerNames={},i.status=0,i.method=r,i.url=a,i.async=!1!==o,i.user=l,i.pass=s,(n={}).headers={},w(1)},b.send=function(e){for(var u,c,d=0,g=["type","timeout","withCredentials"];d<g.length;d+=1)(c="type"===(u=g[d])?"responseType":u)in b&&(i[u]=b[c]);i.body=e;var p=f.listeners("before"),v=function(){if(!p.length)return function(){for(var e=0,n=(s(o,r,b),b.upload&&s(o.concat(a),r.upload,b.upload),t=!0,r.open(i.method,i.url,i.async,i.user,i.pass),["type","timeout","withCredentials"]);e<n.length;e+=1)c="type"===(u=n[e])?"responseType":u,u in i&&(r[c]=i[u]);for(var l in i.headers){var d=i.headers[l];l&&r.setRequestHeader(l,d)}r.send(i.body)}();var e=function(e){if("object"==typeof e&&("number"==typeof e.status||"number"==typeof n.status))return l(e,n),"data"in e||(e.data=e.response||e.text),void w(4);v()};e.head=function(e){l(e,n),w(2)},e.progress=function(e){l(e,n),w(3)};var d=p.shift();1===d.length?e(d(i)):2===d.length&&i.async?d(i,e):e()};v()},b.abort=function(){u=-1,t?r.abort():b.dispatchEvent("abort",{})},b.setRequestHeader=function(e,t){var n=null!=e?e.toLowerCase():void 0,r=i.headerNames[n]=i.headerNames[n]||e;i.headers[r]&&(t=i.headers[r]+", "+t),i.headers[r]=t},b.getResponseHeader=function(e){return g(n.headers[e?e.toLowerCase():void 0])},b.getAllResponseHeaders=function(){return g(d(n.headers))},r.overrideMimeType&&(b.overrideMimeType=function(){r.overrideMimeType.apply(r,arguments)}),r.upload){var C=c();b.upload=C,i.upload=C}return b.UNSENT=0,b.OPENED=1,b.HEADERS_RECEIVED=2,b.LOADING=3,b.DONE=4,b.response="",b.responseText="",b.responseXML=null,b.readyState=0,b.statusText="",b};v.UNSENT=0,v.OPENED=1,v.HEADERS_RECEIVED=2,v.LOADING=3,v.DONE=4;var _={patch:function(){p&&(n.XMLHttpRequest=v)},unpatch:function(){p&&(n.XMLHttpRequest=p)},Native:p,Xhook:v};var h=n.fetch;function m(e){return e instanceof Headers?y([].concat(e.entries())):Array.isArray(e)?y(e):e}function y(e){return e.reduce((function(e,t){var n=t[0],r=t[1];return e[n]=r,e}),{})}var w=function(e,t){void 0===t&&(t={headers:{}});var n=Object.assign(Object.assign({},t),{isFetch:!0});if(e instanceof Request){var r=function(e){var t={};return["method","headers","body","mode","credentials","cache","redirect","referrer","referrerPolicy","integrity","keepalive","signal","url"].forEach((function(n){return t[n]=e[n]})),t}(e),a=Object.assign(Object.assign({},m(r.headers)),m(n.headers));n=Object.assign(Object.assign(Object.assign({},r),t),{headers:a,acceptedRequest:!0})}else n.url=e;var o=f.listeners("before"),i=f.listeners("after");return new Promise((function(t,r){var a=this,l=t,s=function(e){if(!i.length)return l(e);var t=i.shift();return 2===t.length?(t(n,e),s(e)):3===t.length?t(n,e,s):s(e)},u=function(e){if(void 0!==e){var n=new Response(e.body||e.text,e);return t(n),void s(n)}c()},c=function(){if(o.length){var e=o.shift();return 1===e.length?u(e(n)):2===e.length?e(n,u):void 0}d()},d=function(){return function(e,t,n,r){return new(n||(n=Promise))((function(t,a){function o(e){try{l(r.next(e))}catch(e){a(e)}}function i(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(e){e(r)}))).then(o,i)}l((r=r.apply(e,[])).next())}))}(a,0,void 0,(function*(){var t=n.url,a=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n}(n,["url","isFetch","acceptedRequest"]);return e instanceof Request&&a.body instanceof ReadableStream&&(a.body=yield new Response(a.body).text()),h(t,a).then((function(e){return s(e)})).catch((function(e){return l=r,s(e),r(e)}))}))};c()}))},b={patch:function(){h&&(n.fetch=w)},unpatch:function(){h&&(n.fetch=h)},Native:h,Xhook:w},k=f;return k.EventEmitter=c,k.before=function(e,t){if(e.length<1||e.length>2)throw"invalid hook";return k.on("before",e,t)},k.after=function(e,t){if(e.length<2||e.length>3)throw"invalid hook";return k.on("after",e,t)},k.enable=function(){_.patch(),b.patch()},k.disable=function(){_.unpatch(),b.unpatch()},k.XMLHttpRequest=_.Native,k.fetch=b.Native,k.headers=d,k}();function fo(e,t){var n=Lo.xhr_hooks,r=Lo.language_from,a=Ze();if(r===a||Ct())t();else{var o=e.url,i=n.filter(go).find((function(e){return t=o,!(!(n=e.url_wildcard)||"*"===n)&&new RegExp("^"+n.replace(/\?/g,".").replace(/\*/g,".*")+"$").test(t);var t,n}));if(i)return"proxify"===i.action?(e.url=function(e,t){var n=("/"===e.slice(0,1)&&"//"!==e.slice(0,2)?""+window.location.hostname+e:e).replace(/^(https?:)?\/\//,""),r=sn(t);return r?""+r+n:e}(o,a),void t()):function(e,t){var n=t.target_source,r=t.target_key,a=t.action,o={url_query:function(t,n){return void 0===n&&(n=e.url),{word:new Se(n,"https://"+window.location.hostname).searchParams.get(t),setter:function(r){var a=new Se(n,"https://"+window.location.hostname);a.searchParams.set(t,r);var o=a.toString();return n===e.url&&(e.url=o),o}}},url:function(t,n){void 0===n&&(n=e.url);var r=new RegExp(t),a=n.match(r),o=a&&a[1];if(!o)return{};var i=!1,l=o;try{(l=decodeURIComponent(o))!==o&&(i=!0)}catch(e){}return{word:l,setter:function(t){var r=i?encodeURIComponent(t):t,a=n.replace(o,r);return n===e.url&&(e.url=a),a}}},url_encoded_form_payload:function(t,n){void 0===n&&(n=e.body);var r=new Se("https://unused.com?"+n).searchParams;return{word:r.get(t),setter:function(a){r.set(t,a);var o=r.toString();return n===e.body&&(e.body=o),o}}},form_data_payload:function(t,n){return void 0===n&&(n=e.body),{word:n.get(t),setter:function(r){n.set(t,r);var a=n;return n===e.body&&(e.body=a),a}}},json_payload:function(t,n){return void 0===n&&(n=e.body),{word:JSON.parse(n)[t],setter:function(r){var a,o=JSON.stringify(Object.assign(Object.assign({},JSON.parse(n)),((a={})[t]=r,a)));return n===e.body&&(e.body=o),o}}}};return function e(t,n,r){void 0===r&&(r=void 0);var i=t[0],l=t.slice(1),s=n[0],u=n.slice(1);if(!o[s]||t.length!==n.length)return Promise.resolve(r);var c=o[s](i,r),d=c.word,f=c.setter;return d?l.length&&u.length?e(l,u,d).then(f):"reverse_handle_path"===a?Promise.resolve(f(Je().convertLocale(Lo.language_from,d))):"reverse_translate"===a?new Promise((function(e){return Kt(d,(function(t){return e(f(t))}))})):Promise.resolve(r):Promise.resolve(r)}(Array.isArray(r)?r:[r],Array.isArray(n)?n:[n])}(e,i).then((function(){t()}));t()}}function go(e){if(!e)return!1;var t=e.url_wildcard,n=e.action,r=e.target_source,a=e.target_key;return!!t&&("proxify"===n?"url"===r:!!["reverse_translate","reverse_handle_path"].some((function(e){return e===n}))&&(r&&a))}function po(e,t,n){if(n||!e||window.top!==window||!ho(e)){var r=[];try{an(r=nn())}catch(e){te.warn(e)}var a,o,i=Ct();if(e&&t&&!i&&Nt(e),!Lo.is_connect||Lo.dynamicPushState||!i&&e!==Lo.language_from?function(e){void 0===e&&(e=!0);var t=Lo.excluded_blocks,n=Lo.is_connect;if(Ya=e)if(za=t&&t.length&&t.map((function(e){return e.value})).join(","),n&&Va.length>0)for(var r=function(){var e=o[a],t=e.mutations,n=e.observedDocument,r=function(){var e=t.splice(0,100);e.length>0&&(Xa(e,n),setTimeout(r,0))};r()},a=0,o=Va;a<o.length;a+=1)r();else Va=[]}():function(){if(0!==Ja.length){for(var e=0,t=Ja;e<t.length;e+=1)t[e].disconnect();Va=[]}}(),a=Lo.xhr_hooks,o=Lo.is_connect,!(a&&Array.isArray(a)&&a.some(go))||o&&Ct()||(co.enable(),co.before(fo)),window.addEventListener("message",(function(e){if(e.data){var t=e.data,n=t.name,r=t.target,a=t.data;n===ne&&r===ie&&"library"===r&&(so=!0,a.type===re&&uo({type:ae,payload:Ze()}))}})),n||i)_o(e);else if(Lo.is_connect&&!i&&We("onConnectPageLoad",e),Lo.force_translation){for(var s=[],u=0,c=r;u<c.length;u+=1){var d=c[u],f=d.closest?d:d.parentNode;Te(f,Lo.force_translation)&&s.push(d)}to(s)}i&&!i.language_button_displayed&&i.allExcluded||Fa(),i||(Lo.remove_unused_link_hooks&&function(){var e=Ot(),t=Lo.languages.map((function(e){return e.custom_code||e.language_to})).filter((function(t){return!e.includes(t)}));1===e.length&&t.push(Lo.language_from);for(var n=t.map((function(e){return yo(e)})).join(","),r=Ce(document,n),a=0,o=r;a<o.length;a+=1){l(o[a])}}(),qa(Ce(document,Lo.dynamics.map((function(e){return e.value})).join(","))),Ua(),function(){window.addEventListener("message",fn,!1);var e=Lo.translate_iframes;if(e)for(var t=0,n=Ce(document.body,e);t<n.length;t+=1){var r=n[t];r.contentWindow&&un.push(r.contentWindow)}cn({}),Fe("onPageLanguageSet",dn),"with-window-top"===Ae()&&window.top.postMessage({message:"Weglot.iframe"},"*")}(),["alert"].forEach((function(e){var t=window[e];window[e]=function(){var e=arguments;if("string"==typeof arguments[0]){var n=Ze();return Lo.language_from===n?t.apply(window,arguments):Jt([{t:2,w:arguments[0]}],n,{title:!1,cdn:!0}).then((function(n){return e[0]=n.to_words[0],t.apply(window,e)}))}}}))),Be("initialized",e)}}function vo(e){var t=Ze();e!==t&&(Lo.visual_editor?ft(e,(function(n){if("#"===n)return _o(e,t);window.dispatchEvent(new CustomEvent("veLanguageChangeUrl",{detail:{targetUrl:n}}))})):_o(e,t))}function _o(e,t){if(!Ot().includes(e))return io(),void te.warn(e+" isn't a language you have added",{sendToDatadog:!1});(Lo.auto_switch||Lo.geo_auto_switch)&&oo(e);var n=Ct();if(Lo.is_connect||n||Nt(e),!ho(e)){if(Lo.loading_bar&&function(){try{var e=document.createElement("div");e.className="wg-progress",e.id="wg_progress",e.innerHTML='<div class="wg-progress-bar" role="progressbar" aria-label="Page is loading"></div>',document.body.appendChild(e)}catch(e){}}(),function(e){var t=Le("lang");if(t&&t!==e){var n=window.location.search.replace("lang="+t,"lang="+e);try{window.history.replaceState(null,"",window.location.pathname+n)}catch(e){}}Ge=e}(e),Ct())return io(),void lo();if(e===Lo.language_from)return We("onPageLanguageSet",e),io(),ln(null,e),Lo.loading_bar&&lo(),document.documentElement.setAttribute("lang",e),void Be("languageChanged",e,t||"");Jt(on(),e).then((function(n){io(),ln(n,e),document.documentElement.setAttribute("lang",e),Be("languageChanged",e,t||""),Lo.loading_bar&&lo()})).catch((function(e){throw Lo.loading_bar&&lo(),io(),nt().removeItem(ce),e})),We("onPageLanguageSet",e)}}function ho(e){return!(!Lo.is_connect&&"WordPress"!==Lo.technology_name||Ze()===e)&&(!Lo.host||Lo.previewHash&&window.location.hostname.includes(ue)||function(){if(Lo.subdirectory)return[Lo.host].concat(d);return Lo.languages.map((function(e){return e.connect_host_destination&&e.connect_host_destination.host})).concat([Lo.host].concat(d))}().includes(window.location.hostname)?(ft(e,(function(e){return window.location.replace(e)})),!0):(Ae()||te.warn('"'+window.location.hostname+'" is not configured with Weglot. <NAME_EMAIL>',{sendToDatadog:!1}),!1))}qe("initialized",(function(){Lo.translate_search&&!Lo.switcher_editor&&function(){var e=Lo.search_forms,t=Lo.search_parameter;if(t){for(var n=0,r=Ce(document,e);n<r.length;n+=1){var a=r[n];a.addEventListener("submit",(function(e){e.preventDefault();var n=e.target.elements[t].value;Kt(n,(function(e){ee.set({name:"wg-search-form",value:n,options:Lo}),a.elements[t].value=e,a.submit()}))}));var o=void 0;-1!==window.location.search.indexOf(t+"=")&&a.elements&&a.elements[t]&&(o=ee.get("wg-search-form"))&&(a.elements[t].value=o)}ee.erase({name:"wg-search-form",options:Lo})}else te.warn("Search parameter name required for search translation.",{sendToDatadog:!1})}()}),!0);var mo=[];function yo(e){var t=Lo.linkHooksConfig&&Lo.linkHooksConfig.buildAdditionalSelectors;return["a[href='#Weglot-"+e+"']","a[href*='change-language.weglot.com/"+e+"']"].concat(t?t(e):[]).join(",")}function wo(e){void 0===e&&(e=document.body);var t=Lo.linkHooksConfig&&Lo.linkHooksConfig.additionalCheckSelectors||[];if(0!==Ce(e,['a[href^="#Weglot-"]','a[href*="change-language.weglot.com/"]'].concat(t).join(",")).length){for(var n=!1,r=0,a=Ot();r<a.length;r+=1){var o=a[r],i=Ce(e,yo(o));if(0!==i.length){n=!0;for(var l=0,s=i;l<s.length;l+=1){ko(o,s[l])}}}return qe("languageChanged",(function(e){return bo(e)}),!0),qe("initialized",(function(){var e=Ze();e!==Lo.language_from&&bo(e)})),n}}function bo(e){mo.forEach((function(t){var n=t.language,r=t.links,a=n===e,o=a?"add":"remove",i=a?"onLinkActive":"offLinkActive";r.forEach((function(e){e.classList[o]("weglot-link--active"),Lo.linkHooksConfig&&Lo.linkHooksConfig[i]&&Lo.linkHooksConfig[i](e)}))}))}function ko(e,t){var n=mo.find((function(t){return t.language===e}));n&&-1!==n.links.indexOf(t)||(n||(n={language:e,links:[],onLinkClick:function(t){t.preventDefault(),vo(e)}},mo.push(n)),t.setAttribute(fe,""),t.classList.add("weglot-link","weglot-link-"+e),e===Ze()&&(t.classList.add("weglot-link--active"),Lo.linkHooksConfig&&Lo.linkHooksConfig.onLinkActive&&Lo.linkHooksConfig.onLinkActive(t)),ft(e,(function(e){return t.setAttribute("href",e)})),t.addEventListener("click",n.onLinkClick),n.links.push(t))}var xo={};function So(e){return e?"string"!=typeof e?e:e.split(",").map((function(e){return{value:e}})):[]}function Eo(e,t){if(void 0===t&&(t=""),!e)return be.button_style;var n=e.classF||"",r=n.match(/flag-(\d)/),a={with_name:e.withname,full_name:!!e.fullname,is_dropdown:!!e.is_dropdown,with_flags:-1!==n.indexOf("wg-flags"),flag_type:r&&r[1]?ke[r[1]]:"",invert_flags:!0};return t&&(a.custom_css=t),a}function Co(e){var t=e.styleOpt,n=e.containerCss,r=e.target,a=e.sibling;return{style:Eo(t,n),location:{target:r,sibling:a}}}xo[me.shopify.id]=function(){return Fe("onWeglotSetup",(function(){!1!==Lo.original_shopify_checkout&&Lo.is_connect&&!Lo.subdirectory&&Lo.language_from===Ze()&&vt()})),qe("initialized",(function(){var e=nt({type:"cookie"}).getItem("wg_checkout_redirect");e&&(nt({type:"cookie"}).removeItem("wg_checkout_redirect"),nt({type:"cookie"}).setItem("wg_checkout_language",e),_t(e)),window.langify&&te.log("%c Please, uninstall langify to properly use Weglot",{sendToDatadog:!1}),!window.Shopify||je()||!Lo.order_tag||Lo.is_connect&&Lo.language_from!==Ze()||bt(),ht();var t,n=document.querySelectorAll("[data-wg-only-display]");n.length&&Qe(n),Lo.customer_tag&&yt(),document.getElementsByClassName("shopify-payment-button").length&&(t=window.fetch,window.fetch=function(){if("/wallets/checkouts.json"===arguments[0])try{var e=JSON.parse(arguments[1].body),n=mt(Ze());e.checkout.attributes={},Lo.cart_attributes.forEach((function(t){return e.checkout.attributes[t]=n})),arguments[1].body=JSON.stringify(e)}catch(e){}return t.apply(window,arguments)})}),!0),Fe("onConnectPageLoad",(function(e){return kt(e)})),Fe("onPageLanguageSet",(function(e){return kt(e)})),Fe("onDynamicDetected",(function(){ht(Ze())})),Fe("startWhen",(function(){return a("admin-bar-iframe")||a("preview-bar-iframe")||Lo.private_mode||function(){for(var e=0,t=document.scripts;e<t.length;e+=1)if(-1!==t[e].src.indexOf("preview_bar_injector"))return!0;return!1}()})),{}},xo[me.bigcommerce.id]=function(){return Fe("onPageLanguageSet",(function(e){!function(e){for(var t=0,n=document.querySelectorAll('[href*="/checkout.php"],[href*="/cart.php"]');t<n.length;t+=1){var r=n[t];r.setAttribute("href",Pe(r.getAttribute("href"),"lang",e))}}(e)})),{}},xo[me.squarespace.id]=function(){if(qe("initialized",(function(){try{var e=window.ExtensionScriptsSDK;if(!e)return;e["1.0"].page.events.dispatchScriptLoadEvent("Weglot")}catch(e){}})),!Lo.is_connect)try{document.querySelectorAll(".animation-segment-wrapper").forEach((function(e){e.parentNode.dataset.dynamicStrings=e.textContent})),document.querySelectorAll(".animation-segment-parent-hidden").forEach((function(e){e.dataset.dynamicStrings=""}))}catch(e){}return{}},xo[me.wix.id]=function(){var e={};if(window.wixBiSession&&"bolt"!==window.wixBiSession.renderType&&!Lo.visual_editor&&(document.addEventListener("DOMContentLoaded",(function(){new MutationObserver((function(e){for(var t=0;t<e.length;t++){"SUCCESS"===e[t].target.getAttribute("data-santa-render-status")&&(this.disconnect(),Be("start"))}})).observe(document.getElementById("SITE_CONTAINER"),{attributes:!0,attributeFilter:["data-santa-render-status"]})})),e.delayStart=!0,e.wait_transition=!1),window.wixBiSession&&"bolt"===window.wixBiSession.renderType){var t=0,n=setInterval((function(){(document.body&&window.sssr||40===t)&&(Be("start"),clearInterval(n)),t++}),100);e.delayStart=!0,e.wait_transition=!1}return e},xo[me.webflow.id]=function(){return qe("switchersReady",(function(){var e=document.querySelector(".weglot-container");e&&e.classList.add("weglot-container--left")})),{forceDisableConnect:window.Webflow&&window.Webflow.env&&!!window.Webflow.env("editor"),linkHooksConfig:{additionalCheckSelectors:[".weglot-switcher-component a[lang]"],buildAdditionalSelectors:function(e){return['.weglot-switcher-component a[lang="'+e+'"]']},onLinkActive:function(e){if(e){e.classList.add("w--current"),e.setAttribute("aria-current","lang");var t=e.closest(".weglot-switcher-component.w-dropdown");if(t){var n=t.querySelector(".w-dropdown-toggle");if(n){var r=n.querySelector("div"),a=n.querySelector("img"),o=r&&r.textContent,i=n.getAttribute("lang"),l=e.querySelector("div"),s=e.querySelector("img");if(a&&s){var u=a.src,c=a.srcset;a.src=s.src,a.srcset=s.srcset,s.src=u,s.srcset=c}if(r&&l)r.textContent=l.textContent,l.textContent=o;else{var d=n.textContent;n.textContent=e.textContent,e.textContent=d}!function(e,t){var n=mo.find((function(t){return t.language===e}));if(n){var r=n.links.indexOf(t);-1!==r&&(n.links.splice(r,1),t.removeEventListener("click",n.onLinkClick))}}(e.getAttribute("lang"),e),ko(i,e),n.setAttribute("lang",e.getAttribute("lang")),e.setAttribute("lang",i)}}}},offLinkActive:function(e){e.classList.remove("w--current"),e.removeAttribute("aria-current")}}}};var Oo=[{from:"originalLanguage",to:"language_from"},{from:"autoSwitch",to:"auto_switch"},{from:"autoSwitchFallback",to:"auto_switch_fallback"},{from:"waitTransition",to:"wait_transition"},{from:"translateSearch",to:"translate_search"},{from:"searchsForms",to:"search_forms"},{from:"searchParameter",to:"search_parameter"},{from:"hideSwitcher",to:"hide_switcher"},{from:"dangerouslyForceDynamic",to:"dangerously_force_dynamic"},{from:"loadingBar",to:"loading_bar"},{from:"customerTag",to:"customer_tag"},{from:"orderTag",to:"order_tag"},{from:"translateImages",to:"media_enabled"},{from:"extraDefinitions",to:"extra_definitions"},{from:"excludePaths",to:"excluded_paths",func:function(e){return e?"string"!=typeof e?e:e.split(",").filter((function(e){return!!e})).map((function(e){return{value:e,type:"CONTAIN"}})):[]}},{from:"exceptions",to:"excluded_blocks",func:So},{from:"whiteList",to:"whitelist",func:So},{from:"styleOpt",to:"button_style",func:Eo},{from:"destinationLanguages",to:"languages",func:function(e){return"string"!=typeof e?e:e.split(",").map((function(e){return{language_to:e,provider:null,automatic_translation_enabled:!0}}))}}];function To(e){var t=Object.assign({},e);return t.switchers&&("string"==typeof t.switchers&&(t.switchers=JSON.parse(t.switchers)),t.switchers.length&&t.switchers[0].styleOpt&&(t.switchers=t.switchers.map(Co)),t.scriptParamSwitcher=!0),Array.isArray(t.dynamic)&&(t.dynamic=t.dynamic.join(",")),Array.isArray(t.translate_iframes)&&(t.translate_iframes=t.translate_iframes.join(",")),t.translate_images&&(t.media_enabled=!0),Oo.forEach((function(e){var n=e.from,r=e.to,a=e.func;void 0!==t[n]&&(t[r]=a?a(t[n]):t[n],delete t[n])})),t}function No(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&-1===t.indexOf(r)&&(n[r]=e[r]);return n}var Lo={};function Ao(e){if(!e||!e.api_key)throw Error("You have to provide at least a key");var t=e.api_key.split("wg_").pop(),n=To(e);return function(e){if(f(window.location.hostname))return fetch("https://api.weglot.com/projects/settings?api_key=wg_"+e).then((function(e){return e.json()}));var t=o();if(t&&t.settings){var n=t.settings,r=No(t,["settings"]);return n.injectedData=r,Promise.resolve(n)}return fetch(""+se+e+".json").then((function(e){return e.json()}))}(t).then((function(e){var t=e.custom_settings,r=No(e,["custom_settings"]);n.button_style=Object.assign(t?t.button_style:{},n.button_style);var a=r.language_from,o=r.languages;a&&(n.language_from=a),o&&(n.languages=o),t&&t.localeRules&&(n.localeRules=t.localeRules);var i=function(e,t,n){for(var r=Object.assign({},e,t,n),a=0,o=["excluded_blocks","dynamics"];a<o.length;a+=1){var i=o[a];r[i]=Array.from(new Set((e[i]||[]).concat(t[i]||[],n[i]||[])))}return r.dynamic&&(r.dynamics=r.dynamics.concat(r.dynamic.split(",").map((function(e){return{value:e.trim()}}))),delete r.dynamic),r}(r,t,n),l=Ro(i);return Be("onOptionsReady"),l})).catch((function(e){te.error(e,{consoleOverride:e&&e.wgErrMsg||"Cannot fetch Weglot options. Is your key correct?",sendToDatadog:!1})}))}function jo(){var e=o();e&&(delete e.settings,Lo.injectedData=e)}function Ro(e){if(e.deleted_at)throw{wgErrMsg:"Cannot load Weglot because the project has been deleted"};e.injectedData||Z(jo),"SUBDIRECTORY"===e.url_type&&e.is_dns_set&&(e.subdirectory=!0);var t=function(e){if(!e.includes(ue))return null;var t=e.split(".")[0];return t.includes("-")?t.split("-").reverse()[0]:t}(window.location.hostname);if(t&&(e.previewHash=t,e.is_dns_set=!0,e.subdirectory=window.location.hostname.startsWith("subdir-")),e.languages.length||(e.languages=[]),Lo.is_connect=e.subdirectory||e.languages.some((function(e){return e.connect_host_destination&&e.connect_host_destination.is_dns_set&&e.connect_host_destination.created_on_aws})),f(window.location.hostname)&&(window.location.hostname.includes("switchers")&&(Lo.switcher_editor=!0),Lo.visual_editor=!0),Lo.private_mode=function(){-1!==location.search.indexOf("weglot-private=0")&&nt().removeItem("wg-private-mode");var e=document.getElementById("admin-bar-iframe")||document.getElementById("preview-bar-iframe")||-1!==location.search.indexOf("weglot-private=1")||f(window.location.hostname)||"1"===nt({type:"cookie"}).getItem("wg-private-mode");return e&&nt({type:"cookie"}).setItem("wg-private-mode","1"),e}(),!Lo.technology_id){var n=e.technology_name||Lo.technology_name;n&&me[n]&&(e.technology_id=me[n].id,e.technology_name=me[n].name)}var r,a,o,l=(r=e.technology_id)?Ht(xo[r]?xo[r]():{},r):{},s=Object.assign({},be,l);if(Object.assign(Lo,s,e),Lo.whitelist&&!Array.isArray(Lo.whitelist)&&(Lo.whitelist=String(Lo.whitelist).split(",").map((function(e){return{value:e}}))),ye.forEach((function(e){var t,n;Lo[e]!==s[e]&&(Lo[e]=(t=Lo[e],(n=s[e])?Array.isArray(t)?[].concat(t,n):"object"==typeof t?Object.assign({},t,n):(t=t.split(",").filter((function(e){return e})).join(","))&&t.length>0&&t!==n?t+=","+n:t=n:t))})),a="https://cdn.weglot.com/weglot.min.css?v=9",(o=document.createElement("link")).rel="stylesheet",o.type="text/css",o.href=a,document.head.appendChild(o),Lo.button_style&&Lo.button_style.custom_css&&i(Lo.button_style.custom_css,"weglot-custom-style"),Lo.switchers&&0!==Lo.switchers.length?Lo.switchers=Lo.switchers.map((function(e){var t=e.button_style,n=No(e,["button_style"]);return Object.assign({},{style:n.style||t},n)})):Lo.switchers=[{style:Lo.button_style,location:{},default:!0}],Lo.api_key.length<36&&(Lo.translation_engine=1),Lo.excluded_blocks_remove&&(Lo.excluded_blocks=Lo.excluded_blocks.filter((function(e){return!Lo.excluded_blocks_remove.includes(e.value)}))),Lo.dangerously_force_dynamic&&(Lo.dynamics=Lo.dynamics.concat(Lo.dangerously_force_dynamic.split(",").map((function(e){return{value:e.trim()}})))),Array.isArray(Lo.dangerously_bypass_dynamic_limit)&&(Lo.dangerously_bypass_dynamic_limit=Lo.dangerously_bypass_dynamic_limit.map((function(e){return e.value?e.value:e})).join(",")),Lo.excluded_blocks=Lo.excluded_blocks.filter((function(e){return Re(e.value)})),Lo.dynamics=Lo.dynamics.filter((function(e){return Re(e.value)})),Lo.dynamics_remove&&(Lo.dynamics=Lo.dynamics.filter((function(e){return!Lo.dynamics_remove.includes(e.value)}))),Lo.is_tld=!1,l.forceDisableConnect&&(Lo.is_connect=!1),Lo.is_connect&&!Lo.previewHash){var u=Lo.host.split("www.").pop();Lo.is_tld=Lo.languages.some((function(e){if(e.connect_host_destination&&e.connect_host_destination.host)return-1===e.connect_host_destination.host.indexOf(u)}))}return Lo.prevent_retranslation||(Lo.prevent_retranslation=Lo.dynamics.some((function(e){return"body"===e.value}))),Lo.proxyFormat=ve,Lo.is_connect&&!Lo.disable_internal_proxy&&window.location.hostname!==ge&&(Lo.proxyFormat=pe),Array.isArray(Lo.force_translation)&&(Lo.force_translation=Lo.force_translation.join(",")),Lo}var Po={"Node.prototype.contains":document.contains,"Element.prototype.cloneNode":"document"in self&&"cloneNode"in document.documentElement,"location.origin":"location"in self&&"origin"in self.location,MutationObserver:"MutationObserver"in self,Promise:"Promise"in self&&"resolve"in Promise,"Element.prototype.matches":"document"in self&&"matches"in document.documentElement,"Element.prototype.closest":"document"in self&&"closest"in document.documentElement,"Element.prototype.classList":"document"in self&&"classList"in document.documentElement&&function(){var e=document.createElement("div");if(!(e.classList&&e.classList.add&&e.classList.remove&&e.classList.contains&&e.classList.toggle))return!1;var t=!0;e.classList.add("add1","add2"),t=t&&e.className.indexOf("add1")>=0&&e.className.indexOf("add2")>=0,e.className="remove1 remove2 remove3",e.classList.remove("remove1","remove3"),t=t&&-1===e.className.indexOf("remove1")&&e.className.indexOf("remove2")>=0&&-1===e.className.indexOf("remove3");try{e.remove()}catch(t){e=null}return t}(),"String.prototype.includes":"includes"in String.prototype,fetch:"fetch"in self,"Array.prototype.find":"find"in Array.prototype,"Array.prototype.findIndex":"findIndex"in Array.prototype,"Object.assign":"assign"in Object,"Array.prototype.includes":"includes"in Array.prototype,URL:function(e){try{var t=new e.URL("http://weglot.com");if("href"in t&&"searchParams"in t){var n=new URL("http://weglot.com");if(n.search="a=1&b=2","http://weglot.com/?a=1&b=2"===n.href&&(n.search="","http://weglot.com/"===n.href)){var r=new e.URLSearchParams("a=1"),a=new e.URLSearchParams(r);if("a=1"===String(a))return!0}}return!1}catch(e){return!1}}(self)},Io=!1;function Do(){Io=!0,Be("polyfillReady")}function Mo(){return Io}function Fo(e){var t="https://api.weglot.com/pageviews?api_key="+Lo.api_key,n=JSON.stringify({url:e||ze().url,language:Ze(),browser_language:navigator.language});if(navigator.sendBeacon)try{return void navigator.sendBeacon(t,n)}catch(e){}try{fetch(t,{method:"POST",body:n})}catch(e){}}!function(e){window.Prototype&&(delete Object.prototype.toJSON,delete Array.prototype.toJSON);var t=Object.keys(Po).filter((function(e){return!Po[e]}));if(t.length){!function(e,t,n){var r=!1;function a(){r||(r=!0,setTimeout((function(){return t(n)}),20))}var o=document.getElementsByTagName("head")[0]||document.documentElement,i=document.createElement("script");i.type="text/javascript",i.src=e,i.addEventListener?(i.addEventListener("load",a,!1),i.addEventListener("error",a,!1)):i.readyState&&(i.onreadystatechange=a),o.insertBefore(i,o.firstChild)}("https://cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?version=4.8.0&callback=Weglot.polyReady&features="+t.join(","),(function(){}))}else e()}(Do);var Wo,Uo,Ho,qo=[{label:"Afrikaans",value:"af"},{label:"Amharic",value:"am",countries:["ET"]},{label:"Arabic",value:"ar",countries:["AE","BH","DZ","EG","JO","KW","LY","OM","QA","SA","SY","YE","SD","EH","LB","MR","TN","KM","PS","IQ","MU","ER"]},{label:"Azerbaijani",value:"az",countries:["AZ"]},{label:"Bashkir",value:"ba"},{label:"Belarusian",value:"be",countries:["BY"]},{label:"Bulgarian",value:"bg",countries:["BG"]},{label:"Bengali",value:"bn",countries:["BD"]},{label:"Bosnian",value:"bs",countries:["BA"]},{label:"Catalan",value:"ca",countries:["AD"]},{label:"Corsican",value:"co"},{label:"Czech",value:"cs",countries:["CZ"]},{label:"Welsh",value:"cy"},{label:"Danish",value:"da",countries:["DA"]},{label:"German",value:"de",countries:["AT","DE","LI","CH"]},{label:"Greek",value:"el",countries:["GR","CY"]},{label:"English",value:"en",countries:["VU","MV","BT","AG","AI","AU","BB","BM","BS","BZ","CC","CX","DM","FJ","FK","FM","GD","GG","GH","GI","GM","GS","GY","HM","IM","IO","JE","JM","KI","KN","KY","LC","LR","MS","NG","SB","SH","SL","SS","TC","TO","TT","TV","UM","US","VC","VG","VI","ZM","ZA","GU","MP","AQ","CA","IE","GB","MH","MW","PW","NF","PN","CK","NA","AS","ZW","SZ","LS","UG","PH","BW","PG","PK","NR","NU","TM","TK"]},{label:"Esperanto",value:"eo"},{label:"Spanish",value:"es",countries:["ES","AR","CL","CO","CR","CU","DO","EC","GT","HN","MX","NI","PA","PE","SV","UY","VE","PR","GQ","PY","BO"]},{label:"Estonian",value:"et",countries:["EE"]},{label:"Basque",value:"eu"},{label:"Persian",value:"fa",countries:["IR","AF"]},{label:"Finnish",value:"fi",countries:["FI"]},{label:"Fijian",value:"fj"},{label:"Filipino",value:"fl"},{label:"French",value:"fr",countries:["BF","BI","BJ","BL","CD","CG","CI","FR","GA","GF","GN","GP","MC","MF","ML","MQ","NC","NE","PF","PM","RE","SN","TF","TG","WF","YT","TD","DJ","CM","SC","HT","CF","MA","RW"]},{label:"Western Frisian",value:"fy"},{label:"Irish",value:"ga"},{label:"Scottish Gaelic",value:"gd"},{label:"Galician",value:"gl"},{label:"Gujarati",value:"gu"},{label:"Hausa",value:"ha"},{label:"Hebrew",value:"he",countries:["IL"]},{label:"Hindi",value:"hi",countries:["IN"]},{label:"Croatian",value:"hr",countries:["HR"]},{label:"Haitian",value:"ht"},{label:"Hungarian",value:"hu",countries:["HU"]},{label:"Hawaiian",value:"hw"},{label:"Armenian",value:"hy",countries:["AM"]},{label:"Indonesian",value:"id",countries:["ID"]},{label:"Igbo",value:"ig"},{label:"Icelandic",value:"is",countries:["IS"]},{label:"Italian",value:"it",countries:["SM","VA","IT"]},{label:"Japanese",value:"ja",countries:["JP"]},{label:"Javanese",value:"jv"},{label:"Georgian",value:"ka",countries:["GE"]},{label:"Kazakh",value:"kk",countries:["KZ"]},{label:"Central Khmer",value:"km",countries:["KH"]},{label:"Kannada",value:"kn"},{label:"Korean",value:"ko",countries:["KP","KR"]},{label:"Kurdish",value:"ku"},{label:"Kyrgyz",value:"ky",countries:["KG"]},{label:"Latin",value:"la"},{label:"Luxembourgish",value:"lb",countries:["LU"]},{label:"Lao",value:"lo"},{label:"Lithuanian",value:"lt",countries:["LT"]},{label:"Latvian",value:"lv",countries:["LV"]},{label:"Malagasy",value:"mg",countries:["MG"]},{label:"Māori",value:"mi",countries:["NZ"]},{label:"Macedonian",value:"mk",countries:["MK"]},{label:"Malayalam",value:"ml"},{label:"Mongolian",value:"mn",countries:["MN"]},{label:"Marathi",value:"mr"},{label:"Malay",value:"ms",countries:["BN","MY"]},{label:"Maltese",value:"mt",countries:["MT"]},{label:"Burmese",value:"my",countries:["MM"]},{label:"Nepali",value:"ne",countries:["NP"]},{label:"Dutch",value:"nl",countries:["BQ","NL","SR","CW","SX","BE","AW"]},{label:"Norwegian",value:"no",countries:["NO","BV","SJ"]},{label:"Chichewa",value:"ny"},{label:"Punjabi",value:"pa"},{label:"Polish",value:"pl",countries:["PL"]},{label:"Pashto",value:"ps",countries:["AF"]},{label:"Portuguese",value:"pt",countries:["AO","BR","CV","GW","MZ","PT","ST","TL"]},{label:"Romanian",value:"ro",countries:["RO","MD"]},{label:"Russian",value:"ru",countries:["RU"]},{label:"Sindhi",value:"sd"},{label:"Sinhalese",value:"si",countries:["LK"]},{label:"Slovak",value:"sk",countries:["SK"]},{label:"Slovenian",value:"sl",countries:["SI"]},{label:"Samoan",value:"sm",countries:["WS"]},{label:"Shona",value:"sn"},{label:"Somali",value:"so",countries:["SO"]},{label:"Albanian",value:"sq",countries:["AL"]},{label:"Serbian",value:"sr",countries:["RS","ME"]},{label:"Southern Sotho",value:"st"},{label:"Sundanese",value:"su"},{label:"Swedish",value:"sv",countries:["AX","SE"]},{label:"Swahili",value:"sw",countries:["TZ"]},{label:"Tamil",value:"ta"},{label:"Telugu",value:"te"},{label:"Tajik",value:"tg",countries:["TJ"]},{label:"Thai",value:"th",countries:["TH"]},{label:"Tagalog",value:"tl"},{label:"Tongan",value:"to"},{label:"Turkish",value:"tr",countries:["TR"]},{label:"Tatar",value:"tt"},{label:"Traditional Chinese",value:"tw"},{label:"Tahitian",value:"ty"},{label:"Ukrainian",value:"uk",countries:["UA"]},{label:"Urdu",value:"ur"},{label:"Uzbek",value:"uz",countries:["UZ"]},{label:"Vietnamese",value:"vi",countries:["VN"]},{label:"Xhosa",value:"xh"},{label:"Yiddish",value:"yi"},{label:"Yoruba",value:"yo"},{label:"Simplified Chinese",value:"zh",countries:["CN","SG","TW","HK","MO"]},{label:"Zulu",value:"zu",countries:["ZA"]}],Bo=!1;function zo(){window.addEventListener("message",Vo,!1);var e=document.createElement("meta");e.name="google",e.content="notranslate",document.head&&document.head.appendChild(e),document.documentElement&&-1===["cms.e.jimdo.com",ge].indexOf(window.location.host)&&-1===window.location.pathname.indexOf("/wg-cgi/")&&document.documentElement.setAttribute("translate","no");var t=document.head.querySelector("link[href*=weglot_shopify]");t&&document.head.removeChild(t)}function Go(){if(Lo.api_key){qe("initialized",(function(){Lo.page_views_enabled&&(Lo.is_connect?ft(Lo.language_from,(function(e){return Fo(e)})):Fo())}),!0);try{Y(document,Lo)}catch(e){te.error(e)}if(We("onWeglotSetup"),!Ko.initialized||window.Turbolinks){Uo=function(){var e=Ot();if(Lo.is_connect){var t=document.documentElement.dataset.wgTranslated||(Lo.subdirectory?Xe():Ke());if(t!==Lo.language_from)return t;if(Lo.technology_name===me.shopify.name){if(ee.get("wg_checkout_redirect"))return Lo.language_from;var n=ee.get("wg_checkout_language");if(n&&!Lo.shopifyCheckout&&!Ae()&&e.includes(n))return ee.erase({name:"wg_checkout_language",options:Lo}),n}var r=ao();return t===Lo.language_from&&r&&e.includes(r)?r:Lo.language_from}var a=Le("lang");if(a&&e.includes(a))return Bo=!0,a;if(Lo.language_to&&e.includes(Lo.language_to))return Lo.language_to;if(Lo.technology_name===me.wordpress.name){var o=Lo.injectedData&&Lo.injectedData.current_language;if(o&&e.includes(o)||(o=Ye(qo)),o===Lo.language_from){var i=ao();if(i&&e.includes(i))return Bo=!0,i}return o||Lo.language_from}var l=Tt();if(l&&e.includes(l))return l;var s=ao();if(s&&e.includes(s))return Bo=!0,s;return Lo.language_from}(),Ze();var e=Lo.injectedData&&Lo.injectedData.partial_translation,t=Ct();if((Ho=Uo&&Uo!==Lo.language_from&&(document.documentElement.dataset.wgTranslated!==Uo||e)&&!t&&!document.documentElement.dataset.wgExcludedUrl&&!Lo.switcher_editor)&&Lo.wait_transition&&!e?i("@keyframes wg{from{color:transparent;}to{color:transparent;}}body *{color:transparent!important;animation:1s linear infinite wg!important;}",de):io(),Lo.delayStart)return qe("start",(function(){return $o()}),!0);Z($o)}}}function $o(){if(!document.querySelector("#has-script-tags")||document.querySelector("#has-script-tags")&&(document.head.innerHTML.indexOf("weglot_script_tag")>0||document.documentElement.innerHTML.indexOf("weglot_script_tag")>0))try{po(Uo,Bo,Ho)}catch(e){io(),te.error(e,{consoleOverride:"There has been an error initializing, "+e.stack})}else io();Wo=!1,Ko.initialized=!0}function Vo(e){if(e.data)try{var t=JSON.parse(e.data);switch(t.message){case"Weglot.detect":e.source.postMessage(JSON.stringify({message:"Weglot.ready",data:{initialized:Ko.initialized,options:Lo}}),e.origin);break;case"Weglot.switchTo":vo(t.language)}}catch(e){}}function Jo(e){try{for(var t=null,n=0,r=[/cdn\.weglot\.(?:com|us|dev)\/weglot\.min\.js\?([^#]+)/,/cdn\.weglot\.(?:com|us|dev)\/weglot_squarespace-[0-9.]+\.+min\.js\?([^#]+)/];n<r.length;n+=1){if(t=r[n].exec(e))break}if(!t)return null;var a=t[1].split("&").map((function(e){var t=e.split("="),n=t[0],r=t[1];try{return[n,decodeURIComponent(r)]}catch(e){return[n,r]}})).reduce((function(e,t){var n,r=t[0],a=t[1];return Object.assign({},e,((n={})[r]="true"===a||"false"!==a&&a,n))}),{api_key:""});return a.api_key?a:null}catch(e){te.warn(e)}}var Ko=window.Weglot||{initialized:!1,options:Lo,dynamic:"",switchTo:vo,setup:function(e){zo(),Wo||(Wo=!0,He(Mo(),"polyfillReady",(function(){Ao(e).then((function(){return Go()})).catch((function(){te.warn("Your setup is deprecated, please save settings in your dashboard to hide this message.",{sendToDatadog:!1});var t=e.api_key;e.translation_engine=t&&t.length>=36?2:1,function(e){try{var t=["api_key","originalLanguage","destinationLanguages"];if(!e||t.some((function(t){return!e[t]})))throw{wgErrMsg:"You have to provide at least: "+t.join(", ")};Ro(To(e))}catch(e){throw new Error(e&&e.wgErrMsg||"Error while reading Weglot options")}}(e),Go()}))})))},initialize:function(e){zo(),Wo||(Wo=!0,He(Mo(),"polyfillReady",(function(){Ao(e).then((function(){return Go()}))})))},on:function(e,t){return qe(e,t,!1)},off:function(e,t){var n,r=!1,a=function(t){return Ue[t].name===e&&!Ue[t].internal};n="function"==typeof t?function(e){return a(e)&&Ue[e].callback===t}:function(e){return a(e)};for(var o=Ue.length-1;o>=0;o--)n(o)&&(Ue.splice(o,1),r=!0);return r},getStoredLang:Tt,getLanguageName:ia,getCurrentLang:Ze,polyReady:Do,getCache:function(){return Rt[Lo.api_key]||{}},addNodes:function(e){var t=nn(e);return an(t),to(t)},search:Kt,translate:function(e,t){void 0===e&&(e={});var n=e.words,r=e.languageTo;if(void 0===r&&(r=Ze()),!Array.isArray(n)||"object"!=typeof n[0]){var a="Weglot.translate: 1st arg must be an array of objects";return te.error(a,{sendToDatadog:!1}),t&&t(null,a),Promise.reject()}return r===Lo.language_from?(t&&t(n.map((function(e){return e.w}))),Promise.resolve(n.map((function(e){return e.w})))):new Promise((function(e,a){Jt(n,r,{title:!1,cdn:!0}).then((function(n){if(!n||!n.to_words)throw n;t&&t(n.to_words),e(n.to_words)})).catch((function(e){a(e),t&&t(null,e)}))}))},getBestAvailableLanguage:ro,getAvailableLanguages:Ot};return He(Mo(),"polyfillReady",(function(){Za(document);for(var e=0,t=[document.currentScript].concat(Array.from(document.scripts)).filter(Boolean);e<t.length;e+=1){var n=t[e],r=n.src||n.getAttribute&&n.getAttribute("data-src");if(r){var a=Jo(r);if(a)return void Ko.initialize(a)}}})),Ko}();
//# sourceMappingURL=weglot.min.js.map
