<div>
    <div class="overflow-visible bg-neutral py-10 xl:py-20" x-data="sliders()" x-init="
        timelineSliderHash = '572737199';
        mounted();
        ">
        <div class="fluid-container max-w-none hidden lg:block">
            <div class="border-t border-black border-opacity-10"></div>
        </div>
        <div class="lg:flex justify-between">
            <div class="lg:w-1/3 flex-shrink-0 mb-6 lg:mb-0 fluid-container lg:pr-0 lg:mr-0 lg:max-w-none lg:border-r border-black border-opacity-10 relative">
                <div class="lg:pr-8 lg:mt-14">
                    <h2 class="text-primary-600 font-sans leading-none font-semibold text-4.5xl sm:text-5.5xl">
                        Our history
                    </h2>
                    <p class="text-primary-800 mt-4 text-base leading-relaxed font-normal">
                        Our story is built from the ground up, with 60+ years of remarkable results.
                    </p>
                    <div class="block md:hidden">
                        <div class="pt-6 pb-4">
                            <div class="flex justify-between items-center">
                                <button class="border border-primary border-opacity-40 w-9 h-9 grid lg:hidden items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" @click="prevTimelineSlider()" title="Previous">
                                    <span class="text-primary w-4 h-4">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </span>
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary bg-secondary text-primary" @click="timelineSliderPointer.slideToLoop(0)" :class="
                                    timelineActiveIndex >= 0 && timelineActiveIndex <= 2 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 1">
                                1998
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(3)" :class="
                                    timelineActiveIndex >= 3 && timelineActiveIndex <= 4 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 1">
                                2004
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(5)" :class="
                                    timelineActiveIndex >= 5 && timelineActiveIndex <= 6 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 1">
                                2015
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(7)" :class="
                                    timelineActiveIndex >= 7 && timelineActiveIndex <= 11 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 2">
                                2018
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(12)" :class="
                                    timelineActiveIndex >= 12 && timelineActiveIndex <= 15 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 2">
                                2019
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(16)" :class="
                                    timelineActiveIndex >= 16 && timelineActiveIndex <= 19 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 2">
                                2020
                                </button>
                                <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(20)" :class="
                                    timelineActiveIndex >= 20 && timelineActiveIndex <= 23 ? 'bg-secondary text-primary' : 'text-primary-600'
                                    " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 3">
                                2021
                                </button>
                                <button class="border border-primary border-opacity-40 w-9 h-9 grid lg:hidden items-center place-content-center transition hover:scale-110 hover:bg-secondary" @click="nextTimelineSlider()" title="Next">
                                    <span class="text-primary w-4 h-4">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="hidden lg:flex justify-start gap-4 mt-14">
                        <button class="border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" @click="prevTimelineSlider()" title="Previous">
                            <span class="text-primary w-4 h-4">
                                <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                </svg>
                            </span>
                        </button>
                        <button class="border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary" @click="nextTimelineSlider()" title="Next">
                            <span class="text-primary w-4 h-4">
                                <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t lg:border-t-0 border-black border-opacity-10">
                <div class="swiper timeline-slider-572737199 w-[calc(100vw-2rem)] lg:max-w-[72.6vw] !mr-0 swiper-initialized swiper-horizontal swiper-pointer-events">
                    <div class="swiper-wrapper" x-init="
                        $nextTick(() => {
                        let maxHeight = 0;
                        Array.from($el.children).forEach((e) => {
                        const height = e.getBoundingClientRect().height;
                        maxHeight = height > maxHeight ? height : maxHeight;
                        });
                        $el.style.height = `${maxHeight}px`;
                        });
                        " style="transform: translate3d(-862.909px, 0px, 0px); transition-duration: 0ms; height: 740.896px;">
                        
                        <div class="swiper-slide swiper-slide-active" data-swiper-slide-index="0" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-transparent" :class="timelineActiveIndex == 0 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 0 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        1998
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 0 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/peterkim.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 0 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Established Eastern corporate office in North Carolina</li>
                                                    <li>Peter Kim announced as founder and CEO</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide swiper-slide-next" data-swiper-slide-index="1" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 1 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2004
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 1 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/easternoffice.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Historical photograph of a brick hospital building with a curved facade on a city street, accompanied by parked cars and a clear sky.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 1 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Established Eastern office in Georgia</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="2" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 2 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2013
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 2 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/metal.jpg" class="absolute inset-0 w-full h-full object-cover" alt="A vintage photo of a man standing in front of a sign.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 2 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Named Top U.S. Metal Roofer (Metal Construction)</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="3" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 3 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 3 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2014
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 3 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/awards.jpg" class="absolute inset-0 w-full h-full object-cover" aria-hidden="true">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 3 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Named Top U.S. Metal Roofer (Metal Construction)</li>
                                                    <li>Established Eastern Glass & Aluminum Inc.</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="4" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 4 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 4 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2015
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 4 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/truist.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 4 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Augusta Regional Airport project</li>
                                                    <li>Truist Park – Home of the Atlanta Braves</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="5" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 5 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 5 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                       2017
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 5 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/awards2.jpg" class="absolute inset-0 w-full h-full object-cover" alt="A vintage photo of a condo building on the beach.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 5 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Continental Tire MS Manufacturing Facility</li>
                                                    <li>Disney Theme Park “Guardians of The Galaxy”</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="6" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 6 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 6 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2018
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 6 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/ground.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 6 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>NCG Social Circle Facebook Data Center</li>
                                                    <li>The Highline in Washington D.C.</li>
                                                    <li>NASA Research Development Facility</li>
                                                    <li>Old Dominion VA Power</li>
                                                    <li>Valley Health Warren Memorial Hospital</li>
                                                    <li>Eastern established in Seoul, South Korea</li>
                                                    <li>Hyundai Motor Manufacturing Alabama Plant 3</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="7" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 7 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 7 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2019
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 7 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/battery.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Construction of a grand stadium with multiple levels of seating is in progress.">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 7 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>SK Battery America Phase I (First EV battery plant in Georgia)</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="8" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 8 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 8 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2020
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 8 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/office2.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 8 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>ECC established corporate office in Suwanee, GA</li>
                                                    <li>Established iugis Construction Corporation</li>
                                                </ul> 
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide" data-swiper-slide-index="9" style="width: 287.636px;">
                            <div class="flex flex-col gap-8 relative overflow-hidden transition-all px-6 border-l border-opacity-10 py-14 sm:hover:bg-white h-full border-black" :class="timelineActiveIndex == 9 ? 'border-transparent' : 'border-black'">
                                <div class="relative flex flex-col gap-5">
                                    <p :class="getTimelineGrayedIndex() == 9 ? `text-pseudo-opaque` : `text-primary-600`" class="text-current font-medium transition text-2xl text-primary-600">
                                        2021
                                    </p>
                                    <div class="w-full border-t border-black border-opacity-10"></div>
                                    <div class="relative w-full aspect-100/70 overflow-hidden">
                                        <div class="w-full h-full inset-0 absolute z-10 transition bg-transparent" :class="getTimelineGrayedIndex() == 9 ? 'bg-pseudo-opaque' : 'bg-transparent'">
                                        </div>
                                        <div class="absolute inset-0 small">
                                            <picture>
                                                <img src="/images/history/office3.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-4">
                                        <div class="flex gap-[10px]">
                                            <p :class="getTimelineGrayedIndex() == 9 ? `text-pseudo-opaque bullet-emphasis-opaque` : `text-current bullet-emphasis`" class="text-current transition text-sm leading-relaxed bullet-emphasis">
                                                <ul class="myul">
                                                    <li>Eastern Contractors Corporation announces Don Davis as President</li>
                                                    <li>Recognized among Top 50 Contract Glaziers</li>
                                                    <li>EPS Established</li>
                                                </ul>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="hidden md:block md:fluid-container md:max-w-none px-0">
            <div class="py-6 border-t md:border-y border-black border-opacity-10">
                <div class="flex justify-between items-center fluid-container md:px-0 md:mx-0">
                    <button class="border border-primary border-opacity-40 w-9 h-9 grid lg:hidden items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" @click="prevTimelineSlider()" title="Previous">
                        <span class="text-primary w-4 h-4">
                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                            </svg>
                        </span>
                    </button>
                    <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary bg-secondary text-primary" @click="timelineSliderPointer.slideToLoop(0)" :class="
                        timelineActiveIndex >= 0 && timelineActiveIndex <= 0 ? 'bg-secondary text-primary' : 'text-primary-600'
                        " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 0">
                    1990s
                    </button>
                    <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(1)" :class="
                        timelineActiveIndex >= 1 && timelineActiveIndex <= 1 ? 'bg-secondary text-primary' : 'text-primary-600'
                        " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 1">
                    2000s
                    </button>
                    <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(2)" :class="
                        timelineActiveIndex == 2 && timelineActiveIndex <= 3 ? 'bg-secondary text-primary' : 'text-primary-600'
                        " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 3">
                    2010s
                    </button>
                    <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(4)" :class="
                        timelineActiveIndex >= 4 && timelineActiveIndex <= 7 ? 'bg-secondary text-primary' : 'text-primary-600'
                        " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 3">
                    2015s
                    </button>
                    <button class="text-base font-medium p-1 sm:p-3 transition sm:hover:bg-secondary sm:hover:text-primary text-primary-600" @click="timelineSliderPointer.slideToLoop(8)" :class="
                        timelineActiveIndex >= 8 && timelineActiveIndex <= 9 ? 'bg-secondary text-primary' : 'text-primary-600'
                        " x-show="$store.getBreakpoint.isDesktop || timelinePeriodRow == 4">
                    2020s
                    </button>
                    <button class="border border-primary border-opacity-40 w-9 h-9 grid lg:hidden items-center place-content-center transition hover:scale-110 hover:bg-secondary" @click="nextTimelineSlider()" title="Next">
                        <span class="text-primary w-4 h-4">
                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                            </svg>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>