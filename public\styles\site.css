@charset "UTF-8";

@keyframes plyr-progress {
    to {
        background-position: 25px 0;
        background-position: var(--plyr-progress-loading-size, 25px) 0
    }
}

@keyframes plyr-popup {
    0% {
        opacity: .5;
        transform: translateY(10px)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

@keyframes plyr-fade-in {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.plyr {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    align-items: center;
    direction: ltr;
    display: flex;
    flex-direction: column;
    font-family: inherit;
    font-family: var(--plyr-font-family, inherit);
    font-variant-numeric: tabular-nums;
    font-weight: 400;
    font-weight: var(--plyr-font-weight-regular, 400);
    line-height: 1.7;
    line-height: var(--plyr-line-height, 1.7);
    max-width: 100%;
    min-width: 200px;
    position: relative;
    text-shadow: none;
    transition: box-shadow .3s ease;
    z-index: 0
}

.plyr audio,
.plyr iframe,
.plyr video {
    display: block;
    height: 100%;
    width: 100%
}

.plyr button {
    font: inherit;
    line-height: inherit;
    width: auto
}

.plyr:focus {
    outline: 0
}

.plyr--full-ui {
    box-sizing: border-box
}

.plyr--full-ui *,
.plyr--full-ui :after,
.plyr--full-ui :before {
    box-sizing: inherit
}

.plyr--full-ui a,
.plyr--full-ui button,
.plyr--full-ui input,
.plyr--full-ui label {
    touch-action: manipulation
}

.plyr__badge {
    background: #4a5464;
    background: var(--plyr-badge-background, #4a5464);
    border-radius: 2px;
    border-radius: var(--plyr-badge-border-radius, 2px);
    color: #fff;
    color: var(--plyr-badge-text-color, #fff);
    font-size: 9px;
    font-size: var(--plyr-font-size-badge, 9px);
    line-height: 1;
    padding: 3px 4px
}

.plyr--full-ui ::-webkit-media-text-track-container {
    display: none
}

.plyr__captions {
    animation: plyr-fade-in .3s ease;
    bottom: 0;
    display: none;
    font-size: 13px;
    font-size: var(--plyr-font-size-small, 13px);
    left: 0;
    padding: 10px;
    padding: var(--plyr-control-spacing, 10px);
    position: absolute;
    text-align: center;
    transition: transform .4s ease-in-out;
    width: 100%
}

.plyr__captions span:empty {
    display: none
}

@media (min-width:480px) {
    .plyr__captions {
        font-size: 15px;
        font-size: var(--plyr-font-size-base, 15px);
        padding: 20px;
        padding: calc(var(--plyr-control-spacing, 10px)*2)
    }
}

@media (min-width:768px) {
    .plyr__captions {
        font-size: 18px;
        font-size: var(--plyr-font-size-large, 18px)
    }
}

.plyr--captions-active .plyr__captions {
    display: block
}

.plyr:not(.plyr--hide-controls) .plyr__controls:not(:empty)~.plyr__captions {
    transform: translateY(-40px);
    transform: translateY(calc(var(--plyr-control-spacing, 10px)*-4))
}

.plyr__caption {
    background: #000c;
    background: var(--plyr-captions-background, #000c);
    border-radius: 2px;
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
    color: #fff;
    color: var(--plyr-captions-text-color, #fff);
    line-height: 185%;
    padding: .2em .5em;
    white-space: pre-wrap
}

.plyr__caption div {
    display: inline
}

.plyr__control {
    background: #0000;
    border: 0;
    border-radius: 4px;
    border-radius: var(--plyr-control-radius, 4px);
    color: inherit;
    cursor: pointer;
    flex-shrink: 0;
    overflow: visible;
    padding: 7px;
    padding: calc(var(--plyr-control-spacing, 10px)*.7);
    position: relative;
    transition: all .3s ease
}

.plyr__control svg {
    fill: currentColor;
    display: block;
    height: 18px;
    height: var(--plyr-control-icon-size, 18px);
    pointer-events: none;
    width: 18px;
    width: var(--plyr-control-icon-size, 18px)
}

.plyr__control:focus {
    outline: 0
}

.js-focus-visible .plyr__control.focus-visible,
.plyr__control.focus-visible.js-focus-visible {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.plyr__control:focus-visible {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

a.plyr__control {
    text-decoration: none
}

.plyr__control.plyr__control--pressed .icon--not-pressed,
.plyr__control.plyr__control--pressed .label--not-pressed,
.plyr__control:not(.plyr__control--pressed) .icon--pressed,
.plyr__control:not(.plyr__control--pressed) .label--pressed,
a.plyr__control:after,
a.plyr__control:before {
    display: none
}

.plyr--full-ui ::-webkit-media-controls {
    display: none
}

.plyr__controls {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    text-align: center
}

.plyr__controls .plyr__progress__container {
    flex: 1;
    min-width: 0
}

.plyr__controls .plyr__controls__item {
    margin-left: 2.5px;
    margin-left: calc(var(--plyr-control-spacing, 10px)/4)
}

.plyr__controls .plyr__controls__item:first-child {
    margin-left: 0;
    margin-right: auto
}

.plyr__controls .plyr__controls__item.plyr__progress__container {
    padding-left: 2.5px;
    padding-left: calc(var(--plyr-control-spacing, 10px)/4)
}

.plyr__controls .plyr__controls__item.plyr__time {
    padding: 0 5px;
    padding: 0 calc(var(--plyr-control-spacing, 10px)/2)
}

.plyr__controls .plyr__controls__item.plyr__progress__container:first-child,
.plyr__controls .plyr__controls__item.plyr__time+.plyr__time,
.plyr__controls .plyr__controls__item.plyr__time:first-child {
    padding-left: 0
}

.plyr [data-plyr=airplay],
.plyr [data-plyr=captions],
.plyr [data-plyr=fullscreen],
.plyr [data-plyr=pip],
.plyr__controls:empty {
    display: none
}

.plyr--airplay-supported [data-plyr=airplay],
.plyr--captions-enabled [data-plyr=captions],
.plyr--fullscreen-enabled [data-plyr=fullscreen],
.plyr--pip-supported [data-plyr=pip] {
    display: inline-block
}

.plyr__menu {
    display: flex;
    position: relative
}

.plyr__menu .plyr__control svg {
    transition: transform .3s ease
}

.plyr__menu .plyr__control[aria-expanded=true] svg {
    transform: rotate(90deg)
}

.plyr__menu .plyr__control[aria-expanded=true] .plyr__tooltip {
    display: none
}

.plyr__menu__container {
    animation: plyr-popup .2s ease;
    background: #ffffffe6;
    background: var(--plyr-menu-background, #ffffffe6);
    border-radius: 8px;
    border-radius: var(--plyr-menu-radius, 8px);
    bottom: 100%;
    box-shadow: 0 1px 2px #00000026;
    box-shadow: var(--plyr-menu-shadow, 0 1px 2px #00000026);
    color: #4a5464;
    color: var(--plyr-menu-color, #4a5464);
    font-size: 15px;
    font-size: var(--plyr-font-size-base, 15px);
    margin-bottom: 10px;
    position: absolute;
    right: -3px;
    text-align: left;
    white-space: nowrap;
    z-index: 3
}

.plyr__menu__container>div {
    overflow: hidden;
    transition: height .35s cubic-bezier(.4, 0, .2, 1), width .35s cubic-bezier(.4, 0, .2, 1)
}

.plyr__menu__container:after {
    border: 4px solid #0000;
    border: var(--plyr-menu-arrow-size, 4px) solid #0000;
    border-top-color: var(--plyr-menu-background, #ffffffe6);
    content: "";
    height: 0;
    position: absolute;
    right: 14px;
    right: calc(var(--plyr-control-icon-size, 18px)/2 + var(--plyr-control-spacing, 10px)*.7 - var(--plyr-menu-arrow-size, 4px)/2);
    top: 100%;
    width: 0
}

.plyr__menu__container [role=menu] {
    padding: 7px;
    padding: calc(var(--plyr-control-spacing, 10px)*.7)
}

.plyr__menu__container [role=menuitem],
.plyr__menu__container [role=menuitemradio] {
    margin-top: 2px
}

.plyr__menu__container [role=menuitem]:first-child,
.plyr__menu__container [role=menuitemradio]:first-child {
    margin-top: 0
}

.plyr__menu__container .plyr__control {
    align-items: center;
    color: #4a5464;
    color: var(--plyr-menu-color, #4a5464);
    display: flex;
    font-size: 13px;
    font-size: var(--plyr-font-size-menu, var(--plyr-font-size-small, 13px));
    padding: 4.66667px 10.5px;
    padding: calc(var(--plyr-control-spacing, 10px)*.7/1.5) calc(var(--plyr-control-spacing, 10px)*.7*1.5);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%
}

.plyr__menu__container .plyr__control>span {
    align-items: inherit;
    display: flex;
    width: 100%
}

.plyr__menu__container .plyr__control:after {
    border: 4px solid #0000;
    border: var(--plyr-menu-item-arrow-size, 4px) solid #0000;
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%)
}

.plyr__menu__container .plyr__control--forward {
    padding-right: 28px;
    padding-right: calc(var(--plyr-control-spacing, 10px)*.7*4)
}

.plyr__menu__container .plyr__control--forward:after {
    border-left-color: #728197;
    border-left-color: var(--plyr-menu-arrow-color, #728197);
    right: 6.5px;
    right: calc(var(--plyr-control-spacing, 10px)*.7*1.5 - var(--plyr-menu-item-arrow-size, 4px))
}

.js-focus-visible .plyr__menu__container .plyr__control--forward.focus-visible:after,
.plyr__menu__container .plyr__control--forward:hover:after,
.plyr__menu__container.js-focus-visible .plyr__control--forward.focus-visible:after {
    border-left-color: initial
}

.plyr__menu__container .plyr__control--forward:focus-visible:after,
.plyr__menu__container .plyr__control--forward:hover:after {
    border-left-color: initial
}

.plyr__menu__container .plyr__control--back {
    font-weight: 400;
    font-weight: var(--plyr-font-weight-regular, 400);
    margin: 7px;
    margin: calc(var(--plyr-control-spacing, 10px)*.7);
    margin-bottom: 3.5px;
    margin-bottom: calc(var(--plyr-control-spacing, 10px)*.7/2);
    padding-left: 28px;
    padding-left: calc(var(--plyr-control-spacing, 10px)*.7*4);
    position: relative;
    width: calc(100% - 14px);
    width: calc(100% - var(--plyr-control-spacing, 10px)*.7*2)
}

.plyr__menu__container .plyr__control--back:after {
    border-right-color: #728197;
    border-right-color: var(--plyr-menu-arrow-color, #728197);
    left: 6.5px;
    left: calc(var(--plyr-control-spacing, 10px)*.7*1.5 - var(--plyr-menu-item-arrow-size, 4px))
}

.plyr__menu__container .plyr__control--back:before {
    background: #dcdfe5;
    background: var(--plyr-menu-back-border-color, #dcdfe5);
    box-shadow: 0 1px 0 #fff;
    box-shadow: 0 1px 0 var(--plyr-menu-back-border-shadow-color, #fff);
    content: "";
    height: 1px;
    left: 0;
    margin-top: 3.5px;
    margin-top: calc(var(--plyr-control-spacing, 10px)*.7/2);
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 100%
}

.js-focus-visible .plyr__menu__container .plyr__control--back.focus-visible:after,
.plyr__menu__container .plyr__control--back:hover:after,
.plyr__menu__container.js-focus-visible .plyr__control--back.focus-visible:after {
    border-right-color: initial
}

.plyr__menu__container .plyr__control--back:focus-visible:after,
.plyr__menu__container .plyr__control--back:hover:after {
    border-right-color: initial
}

.plyr__menu__container .plyr__control[role=menuitemradio] {
    padding-left: 7px;
    padding-left: calc(var(--plyr-control-spacing, 10px)*.7)
}

.plyr__menu__container .plyr__control[role=menuitemradio]:after,
.plyr__menu__container .plyr__control[role=menuitemradio]:before {
    border-radius: 100%
}

.plyr__menu__container .plyr__control[role=menuitemradio]:before {
    background: #0000001a;
    content: "";
    display: block;
    flex-shrink: 0;
    height: 16px;
    margin-right: 10px;
    margin-right: var(--plyr-control-spacing, 10px);
    transition: all .3s ease;
    width: 16px
}

.plyr__menu__container .plyr__control[role=menuitemradio]:after {
    background: #fff;
    border: 0;
    height: 6px;
    left: 12px;
    opacity: 0;
    top: 50%;
    transform: translateY(-50%) scale(0);
    transition: transform .3s ease, opacity .3s ease;
    width: 6px
}

.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]:before {
    background: #00b2ff;
    background: var(--plyr-control-toggle-checked-background, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)))
}

.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]:after {
    opacity: 1;
    transform: translateY(-50%) scale(1)
}

.js-focus-visible .plyr__menu__container .plyr__control[role=menuitemradio].focus-visible:before,
.plyr__menu__container .plyr__control[role=menuitemradio]:hover:before,
.plyr__menu__container.js-focus-visible .plyr__control[role=menuitemradio].focus-visible:before {
    background: #23282f1a
}

.plyr__menu__container .plyr__control[role=menuitemradio]:focus-visible:before,
.plyr__menu__container .plyr__control[role=menuitemradio]:hover:before {
    background: #23282f1a
}

.plyr__menu__container .plyr__menu__value {
    align-items: center;
    display: flex;
    margin-left: auto;
    margin-right: -5px;
    margin-right: calc(var(--plyr-control-spacing, 10px)*.7*-1 + 2px);
    overflow: hidden;
    padding-left: 24.5px;
    padding-left: calc(var(--plyr-control-spacing, 10px)*.7*3.5);
    pointer-events: none
}

.plyr--full-ui input[type=range] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: #0000;
    border: 0;
    border-radius: 26px;
    border-radius: calc(var(--plyr-range-thumb-height, 13px)*2);
    color: #00b2ff;
    color: var(--plyr-range-fill-background, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    display: block;
    height: 19px;
    height: calc(var(--plyr-range-thumb-active-shadow-width, 3px)*2 + var(--plyr-range-thumb-height, 13px));
    margin: 0;
    min-width: 0;
    padding: 0;
    transition: box-shadow .3s ease;
    width: 100%
}

.plyr--full-ui input[type=range]::-webkit-slider-runnable-track {
    background: #0000;
    background-image: linear-gradient(90deg, currentColor, #0000 0);
    background-image: linear-gradient(to right, currentColor var(--value, 0), #0000 var(--value, 0));
    border: 0;
    border-radius: 2.5px;
    border-radius: calc(var(--plyr-range-track-height, 5px)/2);
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    -webkit-transition: box-shadow .3s ease;
    transition: box-shadow .3s ease;
    -webkit-user-select: none;
    user-select: none
}

.plyr--full-ui input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #fff;
    background: var(--plyr-range-thumb-background, #fff);
    border: 0;
    border-radius: 100%;
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33);
    height: 13px;
    height: var(--plyr-range-thumb-height, 13px);
    margin-top: -4px;
    margin-top: calc((var(--plyr-range-thumb-height, 13px) - var(--plyr-range-track-height, 5px))/2*-1);
    position: relative;
    -webkit-transition: all .2s ease;
    transition: all .2s ease;
    width: 13px;
    width: var(--plyr-range-thumb-height, 13px)
}

.plyr--full-ui input[type=range]::-moz-range-track {
    background: #0000;
    border: 0;
    border-radius: 2.5px;
    border-radius: calc(var(--plyr-range-track-height, 5px)/2);
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    -moz-transition: box-shadow .3s ease;
    transition: box-shadow .3s ease;
    -moz-user-select: none;
    user-select: none
}

.plyr--full-ui input[type=range]::-moz-range-thumb {
    background: #fff;
    background: var(--plyr-range-thumb-background, #fff);
    border: 0;
    border-radius: 100%;
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33);
    height: 13px;
    height: var(--plyr-range-thumb-height, 13px);
    position: relative;
    -moz-transition: all .2s ease;
    transition: all .2s ease;
    width: 13px;
    width: var(--plyr-range-thumb-height, 13px)
}

.plyr--full-ui input[type=range]::-moz-range-progress {
    background: currentColor;
    border-radius: 2.5px;
    border-radius: calc(var(--plyr-range-track-height, 5px)/2);
    height: 5px;
    height: var(--plyr-range-track-height, 5px)
}

.plyr--full-ui input[type=range]::-ms-track {
    color: #0000
}

.plyr--full-ui input[type=range]::-ms-fill-upper,
.plyr--full-ui input[type=range]::-ms-track {
    background: #0000;
    border: 0;
    border-radius: 2.5px;
    border-radius: calc(var(--plyr-range-track-height, 5px)/2);
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    -ms-transition: box-shadow .3s ease;
    transition: box-shadow .3s ease;
    user-select: none
}

.plyr--full-ui input[type=range]::-ms-fill-lower {
    background: #0000;
    background: currentColor;
    border: 0;
    border-radius: 2.5px;
    border-radius: calc(var(--plyr-range-track-height, 5px)/2);
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    -ms-transition: box-shadow .3s ease;
    transition: box-shadow .3s ease;
    user-select: none
}

.plyr--full-ui input[type=range]::-ms-thumb {
    background: #fff;
    background: var(--plyr-range-thumb-background, #fff);
    border: 0;
    border-radius: 100%;
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33);
    height: 13px;
    height: var(--plyr-range-thumb-height, 13px);
    margin-top: 0;
    position: relative;
    -ms-transition: all .2s ease;
    transition: all .2s ease;
    width: 13px;
    width: var(--plyr-range-thumb-height, 13px)
}

.plyr--full-ui input[type=range]::-ms-tooltip {
    display: none
}

.plyr--full-ui input[type=range]::-moz-focus-outer {
    border: 0
}

.plyr--full-ui input[type=range]:focus {
    outline: 0
}

.js-focus-visible .plyr--full-ui input[type=range].focus-visible::-webkit-slider-runnable-track,
.plyr--full-ui.js-focus-visible input[type=range].focus-visible::-webkit-slider-runnable-track {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.plyr--full-ui input[type=range]:focus-visible::-webkit-slider-runnable-track {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.js-focus-visible .plyr--full-ui input[type=range].focus-visible::-moz-range-track,
.plyr--full-ui.js-focus-visible input[type=range].focus-visible::-moz-range-track {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.plyr--full-ui input[type=range]:focus-visible::-moz-range-track {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.js-focus-visible .plyr--full-ui input[type=range].focus-visible::-ms-track,
.plyr--full-ui.js-focus-visible input[type=range].focus-visible::-ms-track {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.plyr--full-ui input[type=range]:focus-visible::-ms-track {
    outline: 2px dashed #00b2ff;
    outline: 2px dashed var(--plyr-focus-visible-color, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    outline-offset: 2px
}

.plyr__poster {
    background-color: #000;
    background-color: var(--plyr-video-background, var(--plyr-video-background, #000));
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: contain;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    transition: opacity .2s ease;
    width: 100%;
    z-index: 1
}

.plyr--stopped.plyr__poster-enabled .plyr__poster {
    opacity: 1
}

.plyr--youtube.plyr--paused.plyr__poster-enabled:not(.plyr--stopped) .plyr__poster {
    display: none
}

.plyr__time {
    font-size: 13px;
    font-size: var(--plyr-font-size-time, var(--plyr-font-size-small, 13px))
}

.plyr__time+.plyr__time:before {
    content: "⁄";
    margin-right: 10px;
    margin-right: var(--plyr-control-spacing, 10px)
}

@media (max-width:767px) {
    .plyr__time+.plyr__time {
        display: none
    }
}

.plyr__tooltip {
    background: #fff;
    background: var(--plyr-tooltip-background, #fff);
    border-radius: 5px;
    border-radius: var(--plyr-tooltip-radius, 5px);
    bottom: 100%;
    box-shadow: 0 1px 2px #00000026;
    box-shadow: var(--plyr-tooltip-shadow, 0 1px 2px #00000026);
    color: #4a5464;
    color: var(--plyr-tooltip-color, #4a5464);
    font-size: 13px;
    font-size: var(--plyr-font-size-small, 13px);
    font-weight: 400;
    font-weight: var(--plyr-font-weight-regular, 400);
    left: 50%;
    line-height: 1.3;
    margin-bottom: 10px;
    margin-bottom: calc(var(--plyr-control-spacing, 10px)/2*2);
    opacity: 0;
    padding: 5px 7.5px;
    padding: calc(var(--plyr-control-spacing, 10px)/2) calc(var(--plyr-control-spacing, 10px)/2*1.5);
    pointer-events: none;
    position: absolute;
    transform: translate(-50%, 10px) scale(.8);
    transform-origin: 50% 100%;
    transition: transform .2s ease .1s, opacity .2s ease .1s;
    white-space: nowrap;
    z-index: 2
}

.plyr__tooltip:before {
    border-left: 4px solid #0000;
    border-left: var(--plyr-tooltip-arrow-size, 4px) solid #0000;
    border-right: 4px solid #0000;
    border-right: var(--plyr-tooltip-arrow-size, 4px) solid #0000;
    border-top: 4px solid #fff;
    border-top: var(--plyr-tooltip-arrow-size, 4px) solid var(--plyr-tooltip-background, #fff);
    bottom: -4px;
    bottom: calc(var(--plyr-tooltip-arrow-size, 4px)*-1);
    content: "";
    height: 0;
    left: 50%;
    position: absolute;
    transform: translateX(-50%);
    width: 0;
    z-index: 2
}

.js-focus-visible .plyr .plyr__control.focus-visible .plyr__tooltip,
.plyr .plyr__control:hover .plyr__tooltip,
.plyr.js-focus-visible .plyr__control.focus-visible .plyr__tooltip,
.plyr__tooltip--visible {
    opacity: 1;
    transform: translate(-50%) scale(1)
}

.plyr .plyr__control:focus-visible .plyr__tooltip,
.plyr .plyr__control:hover .plyr__tooltip,
.plyr__tooltip--visible {
    opacity: 1;
    transform: translate(-50%) scale(1)
}

.plyr .plyr__control:hover .plyr__tooltip {
    z-index: 3
}

.plyr__controls>.plyr__control:first-child .plyr__tooltip,
.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip {
    left: 0;
    transform: translateY(10px) scale(.8);
    transform-origin: 0 100%
}

.plyr__controls>.plyr__control:first-child .plyr__tooltip:before,
.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip:before {
    left: 16px;
    left: calc(var(--plyr-control-icon-size, 18px)/2 + var(--plyr-control-spacing, 10px)*.7)
}

.plyr__controls>.plyr__control:last-child .plyr__tooltip {
    left: auto;
    right: 0;
    transform: translateY(10px) scale(.8);
    transform-origin: 100% 100%
}

.plyr__controls>.plyr__control:last-child .plyr__tooltip:before {
    left: auto;
    right: 16px;
    right: calc(var(--plyr-control-icon-size, 18px)/2 + var(--plyr-control-spacing, 10px)*.7);
    transform: translateX(50%)
}

.js-focus-visible .plyr__controls>.plyr__control:first-child+.plyr__control.focus-visible .plyr__tooltip,
.js-focus-visible .plyr__controls>.plyr__control:first-child.focus-visible .plyr__tooltip,
.js-focus-visible .plyr__controls>.plyr__control:last-child.focus-visible .plyr__tooltip,
.plyr__controls.js-focus-visible>.plyr__control:first-child+.plyr__control.focus-visible .plyr__tooltip,
.plyr__controls.js-focus-visible>.plyr__control:first-child.focus-visible .plyr__tooltip,
.plyr__controls.js-focus-visible>.plyr__control:last-child.focus-visible .plyr__tooltip,
.plyr__controls>.plyr__control:first-child .plyr__tooltip--visible,
.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip--visible,
.plyr__controls>.plyr__control:first-child+.plyr__control:hover .plyr__tooltip,
.plyr__controls>.plyr__control:first-child:hover .plyr__tooltip,
.plyr__controls>.plyr__control:last-child .plyr__tooltip--visible,
.plyr__controls>.plyr__control:last-child:hover .plyr__tooltip {
    transform: translate(0) scale(1)
}

.plyr__controls>.plyr__control:first-child .plyr__tooltip--visible,
.plyr__controls>.plyr__control:first-child+.plyr__control .plyr__tooltip--visible,
.plyr__controls>.plyr__control:first-child+.plyr__control:focus-visible .plyr__tooltip,
.plyr__controls>.plyr__control:first-child+.plyr__control:hover .plyr__tooltip,
.plyr__controls>.plyr__control:first-child:focus-visible .plyr__tooltip,
.plyr__controls>.plyr__control:first-child:hover .plyr__tooltip,
.plyr__controls>.plyr__control:last-child .plyr__tooltip--visible,
.plyr__controls>.plyr__control:last-child:focus-visible .plyr__tooltip,
.plyr__controls>.plyr__control:last-child:hover .plyr__tooltip {
    transform: translate(0) scale(1)
}

.plyr__progress {
    left: 6.5px;
    left: calc(var(--plyr-range-thumb-height, 13px)*.5);
    margin-right: 13px;
    margin-right: var(--plyr-range-thumb-height, 13px);
    position: relative
}

.plyr__progress input[type=range],
.plyr__progress__buffer {
    margin-left: -6.5px;
    margin-left: calc(var(--plyr-range-thumb-height, 13px)*-.5);
    margin-right: -6.5px;
    margin-right: calc(var(--plyr-range-thumb-height, 13px)*-.5);
    width: calc(100% + 13px);
    width: calc(100% + var(--plyr-range-thumb-height, 13px))
}

.plyr__progress input[type=range] {
    position: relative;
    z-index: 2
}

.plyr__progress .plyr__tooltip {
    left: 0;
    max-width: 120px;
    overflow-wrap: break-word
}

.plyr__progress__buffer {
    -webkit-appearance: none;
    background: #0000;
    border: 0;
    border-radius: 100px;
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    left: 0;
    margin-top: -2.5px;
    margin-top: calc((var(--plyr-range-track-height, 5px)/2)*-1);
    padding: 0;
    position: absolute;
    top: 50%
}

.plyr__progress__buffer::-webkit-progress-bar {
    background: #0000
}

.plyr__progress__buffer::-webkit-progress-value {
    background: currentColor;
    border-radius: 100px;
    min-width: 5px;
    min-width: var(--plyr-range-track-height, 5px);
    -webkit-transition: width .2s ease;
    transition: width .2s ease
}

.plyr__progress__buffer::-moz-progress-bar {
    background: currentColor;
    border-radius: 100px;
    min-width: 5px;
    min-width: var(--plyr-range-track-height, 5px);
    -moz-transition: width .2s ease;
    transition: width .2s ease
}

.plyr__progress__buffer::-ms-fill {
    border-radius: 100px;
    -ms-transition: width .2s ease;
    transition: width .2s ease
}

.plyr--loading .plyr__progress__buffer {
    animation: plyr-progress 1s linear infinite;
    background-image: linear-gradient(-45deg, #23282f99 25%, #0000 0, #0000 50%, #23282f99 0, #23282f99 75%, #0000 0, #0000);
    background-image: linear-gradient(-45deg, var(--plyr-progress-loading-background, #23282f99) 25%, #0000 25%, #0000 50%, var(--plyr-progress-loading-background, #23282f99) 50%, var(--plyr-progress-loading-background, #23282f99) 75%, #0000 75%, #0000);
    background-repeat: repeat-x;
    background-size: 25px 25px;
    background-size: var(--plyr-progress-loading-size, 25px) var(--plyr-progress-loading-size, 25px);
    color: #0000
}

.plyr--video.plyr--loading .plyr__progress__buffer {
    background-color: #ffffff40;
    background-color: var(--plyr-video-progress-buffered-background, #ffffff40)
}

.plyr--audio.plyr--loading .plyr__progress__buffer {
    background-color: #c1c8d199;
    background-color: var(--plyr-audio-progress-buffered-background, #c1c8d199)
}

.plyr__progress__marker {
    background-color: #fff;
    background-color: var(--plyr-progress-marker-background, #fff);
    border-radius: 1px;
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 3px;
    width: var(--plyr-progress-marker-width, 3px);
    z-index: 3
}

.plyr__volume {
    align-items: center;
    display: flex;
    position: relative
}

.plyr__volume input[type=range] {
    margin-left: 5px;
    margin-left: calc(var(--plyr-control-spacing, 10px)/2);
    margin-right: 5px;
    margin-right: calc(var(--plyr-control-spacing, 10px)/2);
    max-width: 90px;
    min-width: 60px;
    position: relative;
    z-index: 2
}

.plyr--audio {
    display: block
}

.plyr--audio .plyr__controls {
    background: #fff;
    background: var(--plyr-audio-controls-background, #fff);
    border-radius: inherit;
    color: #4a5464;
    color: var(--plyr-audio-control-color, #4a5464);
    padding: 10px;
    padding: var(--plyr-control-spacing, 10px)
}

.js-focus-visible .plyr--audio .plyr__control.focus-visible,
.plyr--audio .plyr__control:hover,
.plyr--audio .plyr__control[aria-expanded=true],
.plyr--audio.js-focus-visible .plyr__control.focus-visible {
    background: #00b2ff;
    background: var(--plyr-audio-control-background-hover, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    color: #fff;
    color: var(--plyr-audio-control-color-hover, #fff)
}

.plyr--audio .plyr__control:focus-visible,
.plyr--audio .plyr__control:hover,
.plyr--audio .plyr__control[aria-expanded=true] {
    background: #00b2ff;
    background: var(--plyr-audio-control-background-hover, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    color: #fff;
    color: var(--plyr-audio-control-color-hover, #fff)
}

.plyr--full-ui.plyr--audio input[type=range]::-webkit-slider-runnable-track {
    background-color: #c1c8d199;
    background-color: var(--plyr-audio-range-track-background, var(--plyr-audio-progress-buffered-background, #c1c8d199))
}

.plyr--full-ui.plyr--audio input[type=range]::-moz-range-track {
    background-color: #c1c8d199;
    background-color: var(--plyr-audio-range-track-background, var(--plyr-audio-progress-buffered-background, #c1c8d199))
}

.plyr--full-ui.plyr--audio input[type=range]::-ms-track {
    background-color: #c1c8d199;
    background-color: var(--plyr-audio-range-track-background, var(--plyr-audio-progress-buffered-background, #c1c8d199))
}

.plyr--full-ui.plyr--audio input[type=range]:active::-webkit-slider-thumb {
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33, 0 0 0 3px #23282f1a;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33), 0 0 0 var(--plyr-range-thumb-active-shadow-width, 3px) var(--plyr-audio-range-thumb-active-shadow-color, #23282f1a)
}

.plyr--full-ui.plyr--audio input[type=range]:active::-moz-range-thumb {
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33, 0 0 0 3px #23282f1a;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33), 0 0 0 var(--plyr-range-thumb-active-shadow-width, 3px) var(--plyr-audio-range-thumb-active-shadow-color, #23282f1a)
}

.plyr--full-ui.plyr--audio input[type=range]:active::-ms-thumb {
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33, 0 0 0 3px #23282f1a;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33), 0 0 0 var(--plyr-range-thumb-active-shadow-width, 3px) var(--plyr-audio-range-thumb-active-shadow-color, #23282f1a)
}

.plyr--audio .plyr__progress__buffer {
    color: #c1c8d199;
    color: var(--plyr-audio-progress-buffered-background, #c1c8d199)
}

.plyr--video {
    overflow: hidden
}

.plyr--video.plyr--menu-open {
    overflow: visible
}

.plyr__video-wrapper {
    background: #000;
    background: var(--plyr-video-background, var(--plyr-video-background, #000));
    border-radius: inherit;
    height: 100%;
    margin: auto;
    overflow: hidden;
    position: relative;
    width: 100%
}

.plyr__video-embed,
.plyr__video-wrapper--fixed-ratio {
    aspect-ratio: 16/9
}

@supports not (aspect-ratio:16/9) {

    .plyr__video-embed,
    .plyr__video-wrapper--fixed-ratio {
        height: 0;
        padding-bottom: 56.25%;
        position: relative
    }
}

.plyr__video-embed iframe,
.plyr__video-wrapper--fixed-ratio video {
    border: 0;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.plyr--full-ui .plyr__video-embed>.plyr__video-embed__container {
    padding-bottom: 240%;
    position: relative;
    transform: translateY(-38.28125%)
}

.plyr--video .plyr__controls {
    background: linear-gradient(#0000, #000000bf);
    background: var(--plyr-video-controls-background, linear-gradient(#0000, #000000bf));
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    bottom: 0;
    color: #fff;
    color: var(--plyr-video-control-color, #fff);
    left: 0;
    padding: 5px;
    padding: calc(var(--plyr-control-spacing, 10px)/2);
    padding-top: 20px;
    padding-top: calc(var(--plyr-control-spacing, 10px)*2);
    position: absolute;
    right: 0;
    transition: opacity .4s ease-in-out, transform .4s ease-in-out;
    z-index: 3
}

@media (min-width:480px) {
    .plyr--video .plyr__controls {
        padding: 10px;
        padding: var(--plyr-control-spacing, 10px);
        padding-top: 35px;
        padding-top: calc(var(--plyr-control-spacing, 10px)*3.5)
    }
}

.plyr--video.plyr--hide-controls .plyr__controls {
    opacity: 0;
    pointer-events: none;
    transform: translateY(100%)
}

.js-focus-visible .plyr--video .plyr__control.focus-visible,
.plyr--video .plyr__control:hover,
.plyr--video .plyr__control[aria-expanded=true],
.plyr--video.js-focus-visible .plyr__control.focus-visible {
    background: #00b2ff;
    background: var(--plyr-video-control-background-hover, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    color: #fff;
    color: var(--plyr-video-control-color-hover, #fff)
}

.plyr--video .plyr__control:focus-visible,
.plyr--video .plyr__control:hover,
.plyr--video .plyr__control[aria-expanded=true] {
    background: #00b2ff;
    background: var(--plyr-video-control-background-hover, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    color: #fff;
    color: var(--plyr-video-control-color-hover, #fff)
}

.plyr__control--overlaid {
    background: #00b2ff;
    background: var(--plyr-video-control-background-hover, var(--plyr-color-main, var(--plyr-color-main, #00b2ff)));
    border: 0;
    border-radius: 100%;
    color: #fff;
    color: var(--plyr-video-control-color, #fff);
    display: none;
    left: 50%;
    opacity: .9;
    padding: 15px;
    padding: calc(var(--plyr-control-spacing, 10px)*1.5);
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    transition: .3s;
    z-index: 2
}

.plyr__control--overlaid svg {
    left: 2px;
    position: relative
}

.plyr__control--overlaid:focus,
.plyr__control--overlaid:hover {
    opacity: 1
}

.plyr--playing .plyr__control--overlaid {
    opacity: 0;
    visibility: hidden
}

.plyr--full-ui.plyr--video .plyr__control--overlaid {
    display: block
}

.plyr--full-ui.plyr--video input[type=range]::-webkit-slider-runnable-track {
    background-color: #ffffff40;
    background-color: var(--plyr-video-range-track-background, var(--plyr-video-progress-buffered-background, #ffffff40))
}

.plyr--full-ui.plyr--video input[type=range]::-moz-range-track {
    background-color: #ffffff40;
    background-color: var(--plyr-video-range-track-background, var(--plyr-video-progress-buffered-background, #ffffff40))
}

.plyr--full-ui.plyr--video input[type=range]::-ms-track {
    background-color: #ffffff40;
    background-color: var(--plyr-video-range-track-background, var(--plyr-video-progress-buffered-background, #ffffff40))
}

.plyr--full-ui.plyr--video input[type=range]:active::-webkit-slider-thumb {
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33, 0 0 0 3px #ffffff80;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33), 0 0 0 var(--plyr-range-thumb-active-shadow-width, 3px) var(--plyr-audio-range-thumb-active-shadow-color, #ffffff80)
}

.plyr--full-ui.plyr--video input[type=range]:active::-moz-range-thumb {
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33, 0 0 0 3px #ffffff80;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33), 0 0 0 var(--plyr-range-thumb-active-shadow-width, 3px) var(--plyr-audio-range-thumb-active-shadow-color, #ffffff80)
}

.plyr--full-ui.plyr--video input[type=range]:active::-ms-thumb {
    box-shadow: 0 1px 1px #23282f26, 0 0 0 1px #23282f33, 0 0 0 3px #ffffff80;
    box-shadow: var(--plyr-range-thumb-shadow, 0 1px 1px #23282f26, 0 0 0 1px #23282f33), 0 0 0 var(--plyr-range-thumb-active-shadow-width, 3px) var(--plyr-audio-range-thumb-active-shadow-color, #ffffff80)
}

.plyr--video .plyr__progress__buffer {
    color: #ffffff40;
    color: var(--plyr-video-progress-buffered-background, #ffffff40)
}

.plyr:fullscreen {
    background: #000;
    border-radius: 0 !important;
    height: 100%;
    margin: 0;
    width: 100%
}

.plyr:fullscreen video {
    height: 100%
}

.plyr:fullscreen .plyr__control .icon--exit-fullscreen {
    display: block
}

.plyr:fullscreen .plyr__control .icon--exit-fullscreen+svg {
    display: none
}

.plyr:fullscreen.plyr--hide-controls {
    cursor: none
}

@media (min-width:1024px) {
    .plyr:fullscreen .plyr__captions {
        font-size: 21px;
        font-size: var(--plyr-font-size-xlarge, 21px)
    }
}

.plyr--fullscreen-fallback {
    background: #000;
    border-radius: 0 !important;
    bottom: 0;
    height: 100%;
    left: 0;
    margin: 0;
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 10000000
}

.plyr--fullscreen-fallback video {
    height: 100%
}

.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen {
    display: block
}

.plyr--fullscreen-fallback .plyr__control .icon--exit-fullscreen+svg {
    display: none
}

.plyr--fullscreen-fallback.plyr--hide-controls {
    cursor: none
}

@media (min-width:1024px) {
    .plyr--fullscreen-fallback .plyr__captions {
        font-size: 21px;
        font-size: var(--plyr-font-size-xlarge, 21px)
    }
}

.plyr__ads {
    border-radius: inherit;
    bottom: 0;
    cursor: pointer;
    left: 0;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1
}

.plyr__ads>div,
.plyr__ads>div iframe {
    height: 100%;
    position: absolute;
    width: 100%
}

.plyr__ads:after {
    background: #23282f;
    border-radius: 2px;
    bottom: 10px;
    bottom: var(--plyr-control-spacing, 10px);
    color: #fff;
    content: attr(data-badge-text);
    font-size: 11px;
    padding: 2px 6px;
    pointer-events: none;
    position: absolute;
    right: 10px;
    right: var(--plyr-control-spacing, 10px);
    z-index: 3
}

.plyr__ads:empty:after {
    display: none
}

.plyr__cues {
    background: currentColor;
    display: block;
    height: 5px;
    height: var(--plyr-range-track-height, 5px);
    left: 0;
    opacity: .8;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    z-index: 3
}

.plyr__preview-thumb {
    background-color: #fff;
    background-color: var(--plyr-tooltip-background, #fff);
    border-radius: 8px;
    border-radius: var(--plyr-menu-radius, 8px);
    bottom: 100%;
    box-shadow: 0 1px 2px #00000026;
    box-shadow: var(--plyr-tooltip-shadow, 0 1px 2px #00000026);
    margin-bottom: 10px;
    margin-bottom: calc(var(--plyr-control-spacing, 10px)/2*2);
    opacity: 0;
    padding: 3px;
    pointer-events: none;
    position: absolute;
    transform: translateY(10px) scale(.8);
    transform-origin: 50% 100%;
    transition: transform .2s ease .1s, opacity .2s ease .1s;
    z-index: 2
}

.plyr__preview-thumb--is-shown {
    opacity: 1;
    transform: translate(0) scale(1)
}

.plyr__preview-thumb:before {
    border-left: 4px solid #0000;
    border-left: var(--plyr-tooltip-arrow-size, 4px) solid #0000;
    border-right: 4px solid #0000;
    border-right: var(--plyr-tooltip-arrow-size, 4px) solid #0000;
    border-top: 4px solid #fff;
    border-top: var(--plyr-tooltip-arrow-size, 4px) solid var(--plyr-tooltip-background, #fff);
    bottom: -4px;
    bottom: calc(var(--plyr-tooltip-arrow-size, 4px)*-1);
    content: "";
    height: 0;
    left: calc(50% + var(--preview-arrow-offset));
    position: absolute;
    transform: translateX(-50%);
    width: 0;
    z-index: 2
}

.plyr__preview-thumb__image-container {
    background: #c1c8d1;
    border-radius: 7px;
    border-radius: calc(var(--plyr-menu-radius, 8px) - 1px);
    overflow: hidden;
    position: relative;
    z-index: 0
}

.plyr__preview-thumb__image-container img,
.plyr__preview-thumb__image-container:after {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.plyr__preview-thumb__image-container:after {
    border-radius: inherit;
    box-shadow: inset 0 0 0 1px #00000026;
    content: "";
    pointer-events: none
}

.plyr__preview-thumb__image-container img {
    max-height: none;
    max-width: none
}

.plyr__preview-thumb__time-container {
    background: linear-gradient(#0000, #000000bf);
    background: var(--plyr-video-controls-background, linear-gradient(#0000, #000000bf));
    border-bottom-left-radius: 7px;
    border-bottom-left-radius: calc(var(--plyr-menu-radius, 8px) - 1px);
    border-bottom-right-radius: 7px;
    border-bottom-right-radius: calc(var(--plyr-menu-radius, 8px) - 1px);
    bottom: 0;
    left: 0;
    line-height: 1.1;
    padding: 20px 6px 6px;
    position: absolute;
    right: 0;
    z-index: 3
}

.plyr__preview-thumb__time-container span {
    color: #fff;
    font-size: 13px;
    font-size: var(--plyr-font-size-time, var(--plyr-font-size-small, 13px))
}

.plyr__preview-scrubbing {
    bottom: 0;
    filter: blur(1px);
    height: 100%;
    left: 0;
    margin: auto;
    opacity: 0;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
    transition: opacity .3s ease;
    width: 100%;
    z-index: 1
}

.plyr__preview-scrubbing--is-shown {
    opacity: 1
}

.plyr__preview-scrubbing img {
    height: 100%;
    left: 0;
    max-height: none;
    max-width: none;
    -o-object-fit: contain;
    object-fit: contain;
    position: absolute;
    top: 0;
    width: 100%
}

.plyr--no-transition {
    transition: none !important
}

.plyr__sr-only {
    clip: rect(1px, 1px, 1px, 1px);
    border: 0 !important;
    height: 1px !important;
    overflow: hidden;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important
}

.plyr [hidden] {
    display: none !important
}

/*! tailwindcss v3.4.1 | MIT License | https://tailwindcss.com*/
*,
:after,
:before {
    border: 0 solid #e2e8f0;
    box-sizing: border-box
}

:after,
:before {
    --tw-content: ""
}

:host,
html {
    -webkit-text-size-adjust: 100%;
    font-feature-settings: normal;
    -webkit-tap-highlight-color: transparent;
    font-family: "Sharp Grotesk", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-variation-settings: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4
}

body {
    line-height: inherit;
    margin: 0
}

hr {
    border-top-width: 1px;
    color: inherit;
    height: 0
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
pre,
samp {
    font-feature-settings: normal;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 1em;
    font-variation-settings: normal
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    border-collapse: collapse;
    border-color: inherit;
    text-indent: 0
}

button,
input,
optgroup,
select,
textarea {
    font-feature-settings: inherit;
    color: inherit;
    font-family: inherit;
    font-size: 100%;
    font-variation-settings: inherit;
    font-weight: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

[type=button],
[type=reset],
[type=submit],
button {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset,
legend {
    padding: 0
}

menu,
ol,
ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #94a3b8;
    opacity: 1
}

input::placeholder,
textarea::placeholder {
    color: #94a3b8;
    opacity: 1
}

[role=button],
button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
    display: block;
    vertical-align: middle
}

img,
video {
    height: auto;
    max-width: 100%
}

[hidden] {
    display: none
}

[multiple],
[type=date],
[type=datetime-local],
[type=email],
[type=month],
[type=number],
[type=password],
[type=search],
[type=tel],
[type=text],
[type=time],
[type=url],
[type=week],
input:where(:not([type])),
select,
textarea {
    --tw-shadow: 0 0 #0000;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border-color: #64748b;
    border-radius: 0;
    border-width: 1px;
    font-size: 1rem;
    line-height: 1.5rem;
    padding: .5rem .75rem
}

[multiple]:focus,
[type=date]:focus,
[type=datetime-local]:focus,
[type=email]:focus,
[type=month]:focus,
[type=number]:focus,
[type=password]:focus,
[type=search]:focus,
[type=tel]:focus,
[type=text]:focus,
[type=time]:focus,
[type=url]:focus,
[type=week]:focus,
input:where(:not([type])):focus,
select:focus,
textarea:focus {
    --tw-ring-inset: var(--tw-empty,
            /*!*/
            /*!*/
        );
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    border-color: #2563eb;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    outline: 2px solid transparent;
    outline-offset: 2px
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #64748b;
    opacity: 1
}

input::placeholder,
textarea::placeholder {
    color: #64748b;
    opacity: 1
}

::-webkit-datetime-edit-fields-wrapper {
    padding: 0
}

::-webkit-date-and-time-value {
    min-height: 1.5em;
    text-align: inherit
}

::-webkit-datetime-edit {
    display: inline-flex
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-meridiem-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-year-field {
    padding-bottom: 0;
    padding-top: 0
}

select {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%2364748b' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E");
    background-position: right .5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact
}

[multiple],
[size]:where(select:not([size="1"])) {
    background-image: none;
    background-position: 0 0;
    background-repeat: unset;
    background-size: initial;
    padding-right: .75rem;
    -webkit-print-color-adjust: unset;
    print-color-adjust: unset
}

[type=checkbox],
[type=radio] {
    --tw-shadow: 0 0 #0000;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    background-origin: border-box;
    border-color: #64748b;
    border-width: 1px;
    color: #2563eb;
    display: inline-block;
    flex-shrink: 0;
    height: 1rem;
    padding: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    width: 1rem
}

[type=checkbox] {
    border-radius: 0
}

[type=radio] {
    border-radius: 100%
}

[type=checkbox]:focus,
[type=radio]:focus {
    --tw-ring-inset: var(--tw-empty,
            /*!*/
            /*!*/
        );
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    outline: 2px solid transparent;
    outline-offset: 2px
}

[type=checkbox]:checked,
[type=radio]:checked {
    background-color: currentColor;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-color: transparent
}

[type=checkbox]:checked {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.207 4.793a1 1 0 0 1 0 1.414l-5 5a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L6.5 9.086l4.293-4.293a1 1 0 0 1 1.414 0z'/%3E%3C/svg%3E")
}

@media (forced-colors:active) {
    [type=checkbox]:checked {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto
    }
}

[type=radio]:checked {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 16 16' fill='%23fff' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='8' cy='8' r='3'/%3E%3C/svg%3E")
}

@media (forced-colors:active) {
    [type=radio]:checked {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto
    }
}

[type=checkbox]:checked:focus,
[type=checkbox]:checked:hover,
[type=radio]:checked:focus,
[type=radio]:checked:hover {
    background-color: currentColor;
    border-color: transparent
}

[type=checkbox]:indeterminate {
    background-color: currentColor;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3E%3Cpath stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3E%3C/svg%3E");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-color: transparent
}

@media (forced-colors:active) {
    [type=checkbox]:indeterminate {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto
    }
}

[type=checkbox]:indeterminate:focus,
[type=checkbox]:indeterminate:hover {
    background-color: currentColor;
    border-color: transparent
}

[type=file] {
    background: unset;
    border-color: inherit;
    border-radius: 0;
    border-width: 0;
    font-size: unset;
    line-height: inherit;
    padding: 0
}

[type=file]:focus {
    outline: 1px solid ButtonText;
    outline: 1px auto -webkit-focus-ring-color
}

:root {
    font-size: 100%;
    font-size: clamp(1rem, 1.6vw, 1.2rem);
    min-height: 0
}

html {
    height: 100dvh
}

:where(html) {
    display: grid;
    height: 100vh
}

[x-cloak] {
    opacity: 0 !important;
    visibility: hidden !important
}

.breakpoint:before {
    display: block
}

.breakpoint:before color {
    50: #f9fafb;
    100: #f3f4f6;
    200: #e5e7eb;
    300: #d1d5db;
    400: #9ca3af;
    500: #6b7280;
    600: #4b5563;
    700: #374151;
    800: #1f2937;
    900: #111827;
    950: #030712;
    -d-e-f-a-u-l-t: #e8e8ec
}

.breakpoint:before {
    content: "-";
    text-transform: uppercase
}

@media (min-width:640px) {
    .breakpoint:before {
        content: "sm"
    }
}

@media (min-width:768px) {
    .breakpoint:before {
        content: "md"
    }
}

@media (min-width:1024px) {
    .breakpoint:before {
        content: "lg"
    }
}

@media (min-width:1280px) {
    .breakpoint:before {
        content: "xl"
    }
}

@media (min-width:1306px) {
    .breakpoint:before {
        content: "1.5xl"
    }
}

@media (min-width:1536px) {
    .breakpoint:before {
        content: "2xl"
    }
}

@media (min-width:1680px) {
    .breakpoint:before {
        content: "3xl"
    }
}

@media (min-width:2100px) {
    .breakpoint:before {
        content: "ultrawide"
    }
}

@media (prefers-reduced-motion:no-preference) {
    a {
        transition: color .3s ease-in-out
    }
}

html {
    background-color: #fff;
    color: #00003c;
    font-family: "Sharp Grotesk", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"
}

::selection {
    background-color: #0028f0;
    color: #d0ff90
}

::-moz-selection {
    background-color: #0028f0;
    color: #d0ff90
}

mark {
    background-color: #0028f0;
    color: #fff
}

@media (min-width:1024px) {
    .creative-carousel-mask {
        -webkit-mask-image: url(/images/ampersand-reverse.svg);
        mask-image: url(/images/ampersand-reverse.svg);
        -webkit-mask-position: 100% 80%;
        mask-position: 100% 80%;
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: 280%;
        mask-size: 280%
    }
}

.stats-mask {
    -webkit-mask-image: url(/images/ampersand.svg);
    mask-image: url(/images/ampersand.svg);
    -webkit-mask-position: 0 90%;
    mask-position: 0 90%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 300%;
    mask-size: 300%
}

html[lang=es] .main-nav>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0 !important;
    margin-left: calc(1.5rem*(1 - var(--tw-space-x-reverse))) !important;
    margin-right: calc(1.5rem*var(--tw-space-x-reverse)) !important
}

html[lang=es] .main-nav-item {
    font-size: .6rem !important
}

.auto-scroll-slider {
    transition-timing-function: linear
}

.visible-scroller::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 7px
}

.visible-scroller::-webkit-scrollbar-thumb {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 120/var(--tw-bg-opacity));
    border-radius: .25rem;
    box-shadow: 0 0 1px hsla(0, 0%, 100%, .5)
}

.two-line-truncation {
    -webkit-line-clamp: 2
}

.three-line-truncation,
.two-line-truncation {
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden
}

.three-line-truncation {
    -webkit-line-clamp: 3
}

.darkened-project-card-overlay {
    background: linear-gradient(0deg, rgba(0, 0, 0, .6) 27.12%, transparent)
}

.project-card-gradient {
    background: rgba(0, 40, 240, .6);
    background-blend-mode: multiply
}

.video-card-gradient {
    background: linear-gradient(0deg, rgba(0, 0, 0, .8), rgba(0, 0, 0, .8) .01%, rgba(0, 0, 0, .16) 100.05%)
}

.market-list li:after {
    content: url(../images/dot.svg);
    margin-left: .5rem
}

.market-list li:last-child:after {
    display: none
}

.swiper-pagination-bullets .swiper-pagination-bullet-active {
    --tw-bg-opacity: 1 !important;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1.1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    --tw-ring-offset-width: 2.5px !important;
    --tw-ring-opacity: 1 !important;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity)) !important;
    --tw-ring-offset-color: #fff !important;
    background-color: rgb(0 40 240/var(--tw-bg-opacity)) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
    height: 18px !important;
    width: 18px !important
}

.swiper-pagination-bullets .swiper-pagination-bullet {
    --tw-bg-opacity: 1;
    background-color: rgb(0 40 240/var(--tw-bg-opacity));
    border-radius: 9999px !important;
    flex-shrink: 0 !important;
    height: 12px;
    opacity: 1 !important;
    transition-duration: .5s !important;
    transition-timing-function: cubic-bezier(1, 0, 0, 1);
    width: 12px
}

.testimonial-pagination .swiper-pagination-bullets .swiper-pagination-bullet-active {
    --tw-bg-opacity: 1 !important;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1.1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    --tw-ring-offset-width: 2.5px !important;
    --tw-ring-opacity: 1 !important;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity)) !important;
    --tw-ring-offset-color: #fff !important;
    background-color: rgb(0 40 240/var(--tw-bg-opacity)) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
    height: 18px !important;
    width: 18px !important
}

.testimonial-pagination .swiper-pagination-bullets .swiper-pagination-bullet {
    --tw-bg-opacity: 1;
    background-color: rgb(232 232 236/var(--tw-bg-opacity));
    border-radius: 9999px !important;
    flex-shrink: 0 !important;
    height: 12px;
    opacity: 1 !important;
    transition-duration: .5s !important;
    transition-timing-function: cubic-bezier(1, 0, 0, 1);
    width: 12px
}

.swiper-pagination-progressbar {
    --tw-bg-opacity: 0.3;
    background-color: rgb(255 255 255/var(--tw-bg-opacity)) !important;
    height: .125rem !important
}

.swiper-pagination-progressbar-fill {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(208 255 144/var(--tw-bg-opacity)) !important
}

.bullet-emphasis a,
.bullet-emphasis strong {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity));
    font-weight: 500
}

.bullet-emphasis-opaque a,
.bullet-emphasis-opaque strong {
    --tw-text-opacity: 1;
    color: rgb(205 205 213/var(--tw-text-opacity));
    font-weight: 500
}

label {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity));
    font-size: .75rem;
    font-weight: 600;
    letter-spacing: .1em;
    line-height: 1rem;
    text-transform: uppercase
}

label.border-label {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    left: .5rem;
    padding-left: .5rem;
    padding-right: .5rem;
    position: absolute;
    top: -.5rem;
    z-index: 10
}

.outer-grid>:last-child input[type=email],
.outer-grid>:last-child input[type=tel],
.outer-grid>:last-child textarea,
.outer-grid>:last-childinput[type=text] {
    margin-bottom: -5rem
}

@media (min-width:768px) {

    .outer-grid>:last-child input[type=email],
    .outer-grid>:last-child input[type=tel],
    .outer-grid>:last-child textarea,
    .outer-grid>:last-childinput[type=text] {
        margin-bottom: -6rem
    }
}

@media (min-width:1024px) {

    .outer-grid>:last-child input[type=email],
    .outer-grid>:last-child input[type=tel],
    .outer-grid>:last-child textarea,
    .outer-grid>:last-childinput[type=text] {
        margin-bottom: -6rem
    }
}

input[type=email],
input[type=tel],
input[type=text],
textarea {
    --tw-border-opacity: 0.3;
    background-color: transparent;
    border-color: rgb(0 0 60/var(--tw-border-opacity));
    border-radius: 0;
    padding-bottom: .75rem !important;
    padding-top: .75rem !important;
    transition-duration: .3s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955);
    width: 100%
}

input[type=email]:focus,
input[type=tel]:focus,
input[type=text]:focus,
textarea:focus {
    --tw-border-opacity: 1;
    border-color: rgb(0 40 240/var(--tw-border-opacity));
    outline-width: 0
}

select.pagination-select {
    background-position: right 0 center;
    border-style: none;
    padding-left: .25rem
}

select.pagination-select:focus {
    --tw-border-opacity: 0;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    outline: 2px solid transparent;
    outline-offset: 2px
}

select {
    --tw-border-opacity: 0.3;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3E%3Cpath d='m12 15-5-5h10l-5 5Z' fill='%2300207A'/%3E%3C/svg%3E");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.5rem 1.5rem;
    border-color: rgb(0 0 60/var(--tw-border-opacity));
    border-radius: 0;
    padding-right: 1.25rem;
    padding: .75rem !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    transition-duration: .3s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

select:focus {
    --tw-border-opacity: 1;
    border-color: rgb(0 40 240/var(--tw-border-opacity))
}

.gform_submission_error,
.validation_message {
    --tw-text-opacity: 1;
    color: rgb(220 38 38/var(--tw-text-opacity));
    font-size: .75rem;
    line-height: 1rem;
    margin-top: .25rem
}

.gform_heading {
    display: none
}

.outer-grid>:last-child.wpcf7-form select {
    margin-bottom: -5rem
}

@media (min-width:768px) {
    .outer-grid>:last-child.wpcf7-form select {
        margin-bottom: -6rem
    }
}

@media (min-width:1024px) {
    .outer-grid>:last-child.wpcf7-form select {
        margin-bottom: -6rem
    }
}

.wpcf7-form select {
    width: 100%
}

.wpcf7-form .form-heading {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    font-size: 56px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 1.25;
    text-transform: none
}

input[type=submit] {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    align-items: center;
    background-color: rgb(0 40 240/var(--tw-bg-opacity));
    color: rgb(208 255 144/var(--tw-text-opacity));
    cursor: pointer;
    display: inline-flex;
    font-size: .875rem;
    font-weight: 500;
    justify-content: space-between;
    letter-spacing: .025em;
    line-height: 1;
    line-height: 1.25rem;
    padding: 1.5rem 2.5rem;
    text-transform: uppercase;
    transition-duration: .3s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

input[type=submit]:hover {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    background-color: rgb(208 255 144/var(--tw-bg-opacity));
    color: rgb(0 0 120/var(--tw-text-opacity))
}

.wpcf7-list-item {
    margin: 0
}

.wpcf7-list-item label {
    --tw-text-opacity: 1;
    align-items: center;
    color: rgb(0 0 60/var(--tw-text-opacity));
    display: flex;
    font-size: 1rem;
    font-weight: 400;
    gap: .5rem;
    letter-spacing: 0;
    line-height: 1.5rem;
    text-transform: none
}

.wpcf7-checkbox,
.wpcf7-radio {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem
}

input[type=radio] {
    --tw-border-opacity: 1;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-color: rgb(0 0 60/var(--tw-border-opacity));
    border-width: 1.5px;
    height: 1rem;
    width: 1rem
}

input[type=radio]:checked {
    --tw-border-opacity: 1 !important;
    background-color: #0028f01a !important;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='3' fill='%230028F0'/%3E%3C/svg%3E");
    background-position-x: left;
    background-position-y: center;
    border-color: rgb(0 40 240/var(--tw-border-opacity)) !important;
    border-width: 2px !important
}

input[type=radio]:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(var(--tw-ring-offset-width)) var(--tw-ring-color);
    --tw-ring-color: transparent;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

input[type=checkbox] {
    --tw-border-opacity: 1;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-color: rgb(0 0 60/var(--tw-border-opacity));
    border-width: 1.5px
}

input[type=checkbox]:checked {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(0 40 240/var(--tw-bg-opacity)) !important;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='6' fill='none' viewBox='0 0 13 12'%3E%3Cpath d='m4.868 7.811 5.675-5.674 1.06 1.06-6.735 6.736L.921 5.985l1.06-1.06L4.869 7.81Z' fill-rule='evenodd' clip-rule='evenodd' fill='%23fff'/%3E%3C/svg%3E");
    background-position: right 45% bottom 50%;
    background-size: 80% auto
}

input[type=checkbox]:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

.wpcf7-form-control-wrap {
    display: block;
    margin-bottom: 1.5rem
}

@media (min-width:768px) {

    .wpcf7-checkbox,
    .wpcf7-radio {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }
}

.prose h1 {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    font-size: 1.875rem;
    line-height: 2.25rem
}

@media (min-width:640px) {
    .prose h1 {
        font-size: 2.25rem;
        line-height: 2.5rem
    }
}

@media (min-width:768px) {
    .prose h1 {
        font-size: 3rem;
        line-height: 1
    }
}

@media (min-width:1024px) {
    .prose h1 {
        font-size: 3.75rem;
        line-height: 1
    }
}

.prose h2 {
    font-size: 2.25rem;
    line-height: 2.5rem
}

@media (min-width:1024px) {
    .prose h2 {
        font-size: 3rem;
        line-height: 1
    }
}

.prose h2 {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    margin-bottom: 2rem;
    margin-top: 2rem
}

.prose h3 {
    font-size: 1.875rem;
    line-height: 2.25rem
}

@media (min-width:768px) {
    .prose h3 {
        font-size: 2.25rem;
        line-height: 2.5rem
    }
}

.prose h3 {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    margin-bottom: 2rem;
    margin-top: 2rem
}

.prose h4 {
    font-size: 1.5rem;
    line-height: 2rem
}

@media (min-width:768px) {
    .prose h4 {
        font-size: 1.875rem;
        line-height: 2.25rem
    }
}

.prose h4 {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    margin-bottom: 2rem;
    margin-top: 2rem
}

.prose p {
    color: currentColor;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.75rem;
    margin-bottom: 1.5rem
}

.prose blockquote {
    padding-left: 3.5rem
}

@media (min-width:768px) {
    .prose blockquote {
        padding-left: 6rem
    }
}

.prose blockquote {
    --tw-text-opacity: 1;
    border-style: none;
    color: rgb(0 0 120/var(--tw-text-opacity));
    font-family: "Sharp Grotesk", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 500;
    position: relative
}

@media (min-width:768px) {
    .prose blockquote {
        font-size: 1.25rem;
        line-height: 1.75rem
    }
}

.prose blockquote {
    line-height: 1.75rem
}

.prose blockquote p {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    font-family: "Sharp Grotesk", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-size: 1.125rem;
    font-style: normal;
    font-weight: 500
}

@media (min-width:768px) {
    .prose blockquote p {
        font-size: 1.25rem;
        line-height: 1.75rem
    }
}

.prose blockquote p {
    line-height: 1.75rem
}

.prose p cite:before {
    display: none
}

.prose p cite {
    font-size: 1.125rem;
    line-height: 1.75rem
}

@media (min-width:768px) {
    .prose p cite {
        font-size: 1.875rem;
        line-height: 2.25rem
    }
}

.prose p cite {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity));
    display: block;
    font-style: normal;
    font-weight: 600;
    line-height: 1;
    margin-top: 1rem;
    padding-left: 3.5rem
}

@media (min-width:768px) {
    .prose p cite {
        padding-left: 6rem
    }
}

.prose p cite span {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity));
    display: block;
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 1.75rem;
    margin-top: .5rem;
    text-transform: uppercase
}

.prose blockquote p:before {
    content: ""
}

.prose blockquote:before {
    --tw-text-opacity: 1;
    --tw-bg-opacity: 1;
    background: no-repeat;
    background-color: rgb(56 136 255/var(--tw-bg-opacity));
    background-image: url(../images/quote.png);
    background-image: url(../images/quote.svg);
    background-position: 50%;
    background-size: 80%;
    color: rgb(0 0 120/var(--tw-text-opacity));
    content: "";
    display: block;
    padding: .5rem
}

@media (min-width:768px) {
    .prose blockquote:before {
        padding: 1rem
    }
}

.prose blockquote:before {
    width: 2.5rem
}

@media (min-width:768px) {
    .prose blockquote:before {
        width: 4rem
    }
}

.prose blockquote:before {
    aspect-ratio: 1/1;
    left: 0;
    position: absolute;
    top: 0
}

.prose ul,
ol {
    color: currentColor;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.75rem;
    margin-bottom: 3rem
}

.prose li::marker {
    color: currentColor;
    margin-bottom: .25rem
}

.prose li {
    color: currentColor;
    margin-bottom: .25rem;
    margin-top: 0
}

.prose a {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity));
    font-weight: 500
}

.prose a:hover {
    --tw-text-opacity: 1;
    color: rgb(0 0 60/var(--tw-text-opacity))
}

.prose a {
    text-decoration-line: underline
}

.prose img {
    margin-bottom: .25rem
}

.prose .wp-caption-text {
    --tw-text-opacity: 1;
    color: rgb(76 76 86/var(--tw-text-opacity));
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.25rem;
    margin-top: .75rem
}

.prose .aligncenter,
.prose .alignleft,
.prose .alignnone,
.prose .alignright {
    max-width: 100%
}

.prose .aligncenter {
    display: block;
    margin: auto
}

.prose .alignleft {
    float: left;
    margin-bottom: 1.5rem;
    margin-right: 1.5rem
}

.prose .alignright {
    float: right;
    margin-bottom: 1.5rem;
    margin-left: 1.5rem
}

.accordion .prose p {
    margin-bottom: .75rem;
    margin-top: .75rem
}

.video-gradient {
    background: linear-gradient(0deg, rgba(0, 0, 0, .9), rgba(0, 0, 0, .24))
}

.news-thumbnail-gradient {
    background: linear-gradient(0deg, rgba(0, 40, 240, 0), rgba(0, 40, 240, 0));
    background-blend-mode: hard-light, normal
}

.stats-mobile-gradient {
    background: linear-gradient(180deg, #3888ff 2.62%, rgba(56, 136, 255, 0) 28.1%)
}

.cta-simple p a {
    --tw-text-opacity: 1;
    color: rgb(208 255 144/var(--tw-text-opacity))
}

.benefits-grid:nth-child(-n+3) {
    border-bottom-width: 1px
}

.benefits-grid:nth-child(3),
.benefits-grid:nth-child(6),
.benefits-grid:nth-child(9) {
    border-right-width: 0
}

.values-grid:nth-child(-n+3) {
    border-bottom-width: 1px
}

.values-grid:nth-child(3),
.values-grid:nth-child(6),
.values-grid:nth-child(9) {
    border-right-width: 0
}

@media only screen and (max-width:767px) {
    .benefits-grid {
        border-bottom-width: 1px
    }

    .benefits-grid:last-child {
        border-style: none
    }

    .values-grid {
        border-bottom-width: 1px
    }

    .values-grid:last-child {
        border-style: none
    }

    .prose .aligncenter,
    .prose .alignleft,
    .prose .alignright {
        float: none;
        margin-left: 0;
        margin-right: 0
    }
}

@media (min-width:768px) and (max-width:1024px) {

    .benefits-grid:nth-child(-n+2),
    .benefits-grid:nth-child(-n+3),
    .values-grid:nth-child(-n+2),
    .values-grid:nth-child(-n+3) {
        border-bottom-width: 0
    }
}

*,
:after,
:before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

.plyr--video {
    background-color: transparent
}

.plyr__control--overlaid {
    --tw-translate-x: -50%;
    --tw-translate-y: -50%;
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    color: rgb(17 24 39/var(--tw-text-opacity));
    opacity: 1;
    padding: 1.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@media (min-width:768px) {
    .plyr__control--overlaid {
        padding: 2rem
    }
}

.plyr__control--overlaid:focus,
.plyr__control--overlaid:hover {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    --tw-bg-opacity: 1 !important;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    background-color: rgb(0 40 240/var(--tw-bg-opacity)) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.plyr__control--overlaid:focus,
.plyr__control--overlaid:focus svg,
.plyr__control--overlaid:hover,
.plyr__control--overlaid:hover svg {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.plyr__control--overlaid:focus svg,
.plyr__control--overlaid:hover svg {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25
}

.plyr__control.plyr__tab-focus,
.plyr__control:hover,
.plyr__control[aria-expanded=true] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(0 40 240/var(--tw-bg-opacity)) !important
}

.plyr__control.plyr__tab-focus {
    --tw-shadow: 0 0 #0000 !important;
    --tw-shadow-colored: 0 0 #0000 !important;
    border-style: none !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
    outline-color: #0028f0
}

.plyr--full-ui input[type=range],
.plyr--full-ui input[type=range].plyr__tab-focus {
    --tw-text-opacity: 1 !important;
    color: rgb(0 40 240/var(--tw-text-opacity)) !important
}

.plyr--full-ui input[type=range].plyr__tab-focus {
    background-color: transparent !important;
    border-style: none !important;
    outline: 2px solid transparent !important;
    outline-offset: 2px !important
}

.plyr__poster {
    background-size: cover
}

.container {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    width: 100%
}

@media (min-width:640px) {
    .container {
        max-width: 640px;
        padding-left: 2rem;
        padding-right: 2rem
    }
}

@media (min-width:768px) {
    .container {
        max-width: 768px
    }
}

@media (min-width:1024px) {
    .container {
        max-width: 1024px;
        padding-left: 4rem;
        padding-right: 4rem
    }
}

@media (min-width:1280px) {
    .container {
        max-width: 1280px;
        padding-left: 5rem;
        padding-right: 5rem
    }
}

@media (min-width:1306px) {
    .container {
        max-width: 1306px
    }
}

@media (min-width:1536px) {
    .container {
        max-width: 1536px;
        padding-left: 6rem;
        padding-right: 6rem
    }
}

@media (min-width:1680px) {
    .container {
        max-width: 1680px
    }
}

@media (min-width:2100px) {
    .container {
        max-width: 2100px
    }
}

.prose {
    color: var(--tw-prose-body);
    max-width: 65ch
}

.prose :where(p):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 1.25em;
    margin-top: 1.25em
}

.prose :where([class~=lead]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-lead);
    font-size: 1.25em;
    line-height: 1.6;
    margin-bottom: 1.2em;
    margin-top: 1.2em
}

.prose :where(a):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-links);
    font-weight: 500;
    text-decoration: underline
}

.prose :where(strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-bold);
    font-weight: 600
}

.prose :where(a strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(blockquote strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(thead th strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(ol):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: decimal;
    margin-bottom: 1.25em;
    margin-top: 1.25em;
    padding-left: 1.625em
}

.prose :where(ol[type=A]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: upper-alpha
}

.prose :where(ol[type=a]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: lower-alpha
}

.prose :where(ol[type=A s]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: upper-alpha
}

.prose :where(ol[type=a s]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: lower-alpha
}

.prose :where(ol[type=I]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: upper-roman
}

.prose :where(ol[type=i]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: lower-roman
}

.prose :where(ol[type=I s]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: upper-roman
}

.prose :where(ol[type=i s]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: lower-roman
}

.prose :where(ol[type="1"]):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: decimal
}

.prose :where(ul):not(:where([class~=not-prose], [class~=not-prose] *)) {
    list-style-type: disc;
    margin-bottom: 1.25em;
    margin-top: 1.25em;
    padding-left: 1.625em
}

.prose :where(ol>li):not(:where([class~=not-prose], [class~=not-prose] *))::marker {
    color: var(--tw-prose-counters);
    font-weight: 400
}

.prose :where(ul>li):not(:where([class~=not-prose], [class~=not-prose] *))::marker {
    color: var(--tw-prose-bullets)
}

.prose :where(dt):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    margin-top: 1.25em
}

.prose :where(hr):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-color: var(--tw-prose-hr);
    border-top-width: 1px;
    margin-bottom: 3em;
    margin-top: 3em
}

.prose :where(blockquote):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-left-color: var(--tw-prose-quote-borders);
    border-left-width: .25rem;
    color: var(--tw-prose-quotes);
    font-style: italic;
    font-weight: 500;
    margin-bottom: 1.6em;
    margin-top: 1.6em;
    padding-left: 1em;
    quotes: "\201C" "\201D" "\2018" "\2019"
}

.prose :where(blockquote p:first-of-type):not(:where([class~=not-prose], [class~=not-prose] *)):before {
    content: open-quote
}

.prose :where(blockquote p:last-of-type):not(:where([class~=not-prose], [class~=not-prose] *)):after {
    content: close-quote
}

.prose :where(h1):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    font-size: 2.25em;
    font-weight: 800;
    line-height: 1.1111111;
    margin-bottom: .8888889em;
    margin-top: 0
}

.prose :where(h1 strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit;
    font-weight: 900
}

.prose :where(h2):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    font-size: 1.5em;
    font-weight: 700;
    line-height: 1.3333333;
    margin-bottom: 1em;
    margin-top: 2em
}

.prose :where(h2 strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit;
    font-weight: 800
}

.prose :where(h3):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    font-size: 1.25em;
    font-weight: 600;
    line-height: 1.6;
    margin-bottom: .6em;
    margin-top: 1.6em
}

.prose :where(h3 strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit;
    font-weight: 700
}

.prose :where(h4):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    line-height: 1.5;
    margin-bottom: .5em;
    margin-top: 1.5em
}

.prose :where(h4 strong):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit;
    font-weight: 700
}

.prose :where(img):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 2em;
    margin-top: 2em
}

.prose :where(picture):not(:where([class~=not-prose], [class~=not-prose] *)) {
    display: block;
    margin-bottom: 2em;
    margin-top: 2em
}

.prose :where(kbd):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-radius: .3125rem;
    box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows)/10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows)/10%);
    color: var(--tw-prose-kbd);
    font-family: inherit;
    font-size: .875em;
    font-weight: 500;
    padding: .1875em .375em
}

.prose :where(code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-code);
    font-size: .875em;
    font-weight: 600
}

.prose :where(code):not(:where([class~=not-prose], [class~=not-prose] *)):before {
    content: "`"
}

.prose :where(code):not(:where([class~=not-prose], [class~=not-prose] *)):after {
    content: "`"
}

.prose :where(a code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(h1 code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(h2 code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit;
    font-size: .875em
}

.prose :where(h3 code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit;
    font-size: .9em
}

.prose :where(h4 code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(blockquote code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(thead th code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: inherit
}

.prose :where(pre):not(:where([class~=not-prose], [class~=not-prose] *)) {
    background-color: var(--tw-prose-pre-bg);
    border-radius: .375rem;
    color: var(--tw-prose-pre-code);
    font-size: .875em;
    font-weight: 400;
    line-height: 1.7142857;
    margin-bottom: 1.7142857em;
    margin-top: 1.7142857em;
    overflow-x: auto;
    padding: .8571429em 1.1428571em
}

.prose :where(pre code):not(:where([class~=not-prose], [class~=not-prose] *)) {
    background-color: transparent;
    border-radius: 0;
    border-width: 0;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
    padding: 0
}

.prose :where(pre code):not(:where([class~=not-prose], [class~=not-prose] *)):before {
    content: none
}

.prose :where(pre code):not(:where([class~=not-prose], [class~=not-prose] *)):after {
    content: none
}

.prose :where(table):not(:where([class~=not-prose], [class~=not-prose] *)) {
    font-size: .875em;
    line-height: 1.7142857;
    margin-bottom: 2em;
    margin-top: 2em;
    table-layout: auto;
    text-align: left;
    width: 100%
}

.prose :where(thead):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-bottom-color: var(--tw-prose-th-borders);
    border-bottom-width: 1px
}

.prose :where(thead th):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    padding-bottom: .5714286em;
    padding-left: .5714286em;
    padding-right: .5714286em;
    vertical-align: bottom
}

.prose :where(tbody tr):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-bottom-color: var(--tw-prose-td-borders);
    border-bottom-width: 1px
}

.prose :where(tbody tr:last-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-bottom-width: 0
}

.prose :where(tbody td):not(:where([class~=not-prose], [class~=not-prose] *)) {
    vertical-align: baseline
}

.prose :where(tfoot):not(:where([class~=not-prose], [class~=not-prose] *)) {
    border-top-color: var(--tw-prose-th-borders);
    border-top-width: 1px
}

.prose :where(tfoot td):not(:where([class~=not-prose], [class~=not-prose] *)) {
    vertical-align: top
}

.prose :where(figure>*):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 0;
    margin-top: 0
}

.prose :where(figcaption):not(:where([class~=not-prose], [class~=not-prose] *)) {
    color: var(--tw-prose-captions);
    font-size: .875em;
    line-height: 1.4285714;
    margin-top: .8571429em
}

.prose {
    --tw-prose-body: #374151;
    --tw-prose-headings: #111827;
    --tw-prose-lead: #4b5563;
    --tw-prose-links: #111827;
    --tw-prose-bold: #111827;
    --tw-prose-counters: #6b7280;
    --tw-prose-bullets: #d1d5db;
    --tw-prose-hr: #e5e7eb;
    --tw-prose-quotes: #111827;
    --tw-prose-quote-borders: #e5e7eb;
    --tw-prose-captions: #6b7280;
    --tw-prose-kbd: #111827;
    --tw-prose-kbd-shadows: 17 24 39;
    --tw-prose-code: #111827;
    --tw-prose-pre-code: #e5e7eb;
    --tw-prose-pre-bg: #1f2937;
    --tw-prose-th-borders: #d1d5db;
    --tw-prose-td-borders: #e5e7eb;
    --tw-prose-invert-body: #d1d5db;
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: #9ca3af;
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: #9ca3af;
    --tw-prose-invert-bullets: #4b5563;
    --tw-prose-invert-hr: #374151;
    --tw-prose-invert-quotes: #f3f4f6;
    --tw-prose-invert-quote-borders: #374151;
    --tw-prose-invert-captions: #9ca3af;
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: #d1d5db;
    --tw-prose-invert-pre-bg: rgba(0, 0, 0, .5);
    --tw-prose-invert-th-borders: #4b5563;
    --tw-prose-invert-td-borders: #374151;
    font-size: 1rem;
    line-height: 1.75
}

.prose :where(picture>img):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 0;
    margin-top: 0
}

.prose :where(video):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 2em;
    margin-top: 2em
}

.prose :where(li):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: .5em;
    margin-top: .5em
}

.prose :where(ol>li):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding-left: .375em
}

.prose :where(ul>li):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding-left: .375em
}

.prose :where(.prose>ul>li p):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: .75em;
    margin-top: .75em
}

.prose :where(.prose>ul>li>:first-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 1.25em
}

.prose :where(.prose>ul>li>:last-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 1.25em
}

.prose :where(.prose>ol>li>:first-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 1.25em
}

.prose :where(.prose>ol>li>:last-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 1.25em
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: .75em;
    margin-top: .75em
}

.prose :where(dl):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 1.25em;
    margin-top: 1.25em
}

.prose :where(dd):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: .5em;
    padding-left: 1.625em
}

.prose :where(hr+*):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 0
}

.prose :where(h2+*):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 0
}

.prose :where(h3+*):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 0
}

.prose :where(h4+*):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 0
}

.prose :where(thead th:first-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding-left: 0
}

.prose :where(thead th:last-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding-right: 0
}

.prose :where(tbody td, tfoot td):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding: .5714286em
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding-left: 0
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    padding-right: 0
}

.prose :where(figure):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 2em;
    margin-top: 2em
}

.prose :where(.prose>:first-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 0
}

.prose :where(.prose>:last-child):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 0
}

.prose :where(ul>li p, ol>li p):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 0 !important;
    margin-top: 0 !important
}

.prose :where(:where(.prose>div>:first-child)):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-top: 0 !important
}

.prose :where(:where(.prose>div>:last-child)):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 0 !important
}

.prose :where(.not-prose):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin: 2rem 0 !important
}

.prose :where(.alignright):not(:where([class~=not-prose], [class~=not-prose] *)) {
    float: right;
    margin-bottom: 1.5rem;
    margin-left: 1.5rem
}

.prose :where(.alignright img):not(:where([class~=not-prose], [class~=not-prose] *)) {
    margin-bottom: 0
}

.prose :where(p.wp-caption-text):not(:where([class~=not-prose], [class~=not-prose] *)) {
    font-size: .875rem;
    margin-top: .25rem
}

.fluid-container {
    margin-left: auto;
    margin-right: auto;
    max-width: 1536px;
    padding-left: calc(env(safe-area-inset-left, 0rem) + 2rem);
    padding-right: calc(env(safe-area-inset-right, 0rem) + 2rem);
    width: 100%
}

.no-scroll {
    height: 100%;
    overflow: hidden
}

.outer-grid {
    display: grid;
    padding-bottom: 5rem;
    padding-top: 5rem;
    row-gap: 5rem;
    width: 100%
}

.outer-grid>:last-child.w-full {
    margin-bottom: -5rem
}

@media (min-width:768px) {
    .outer-grid {
        padding-bottom: 6rem;
        padding-top: 6rem;
        row-gap: 6rem
    }

    .outer-grid>:last-child.w-full {
        margin-bottom: -6rem
    }
}

@media (min-width:1024px) {
    .fluid-container {
        padding-left: calc(env(safe-area-inset-left, 0rem) + 3rem)
    }

    .fluid-container,
    .fluid-container-right {
        padding-right: calc(env(safe-area-inset-right, 0rem) + 3rem)
    }

    .outer-grid {
        padding-bottom: 6rem;
        padding-top: 6rem;
        row-gap: 6rem
    }

    .outer-grid>:last-child.w-full {
        margin-bottom: -6rem
    }
}

@media (min-width:1536px) {
    . {
        padding-left: calc(env(safe-area-inset-left, 0rem) + 4rem);
        padding-right: calc(env(safe-area-inset-right, 0rem) + 4rem)
    }

    .\! {
        padding-left: calc(env(safe-area-inset-left, 0rem) + 4rem) !important;
        padding-right: calc(env(safe-area-inset-right, 0rem) + 4rem) !important
    }
}

.copy-column-text {
    -moz-columns: 1;
    -moz-column-count: 1;
    column-count: 1;
    gap: 2rem
}

@media (min-width:1024px) {
    .copy-column-text {
        -moz-columns: 2;
        -moz-column-count: 2;
        column-count: 2
    }
}

.sr-only {
    clip: rect(0, 0, 0, 0);
    border-width: 0;
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    white-space: nowrap;
    width: 1px
}

.pointer-events-none {
    pointer-events: none
}

.\!visible {
    visibility: visible !important
}

.visible {
    visibility: visible
}

.invisible {
    visibility: hidden
}

.collapse {
    visibility: collapse
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.\!sticky {
    position: sticky !important
}

.sticky {
    position: sticky
}

.inset-0 {
    inset: 0
}

.inset-x-0 {
    left: 0;
    right: 0
}

.inset-y-0 {
    bottom: 0;
    top: 0
}

.\!top-1 {
    top: .25rem !important
}

.-bottom-20 {
    bottom: -5rem
}

.-bottom-px {
    bottom: -1px
}

.-left-1\/2 {
    left: -50%
}

.-left-1\/4 {
    left: -25%
}

.-left-20 {
    left: -5rem
}

.-left-8 {
    left: -2rem
}

.-top-1\/4 {
    top: -25%
}

.-top-16 {
    top: -4rem
}

.-top-px {
    top: -1px
}

.bottom-0 {
    bottom: 0
}

.bottom-12 {
    bottom: 3rem
}

.bottom-5 {
    bottom: 1.25rem
}

.left-0 {
    left: 0
}

.left-5 {
    left: 1.25rem
}

.left-8 {
    left: 2rem
}

.left-9 {
    left: 2.25rem
}

.left-\[55\%\] {
    left: 55%
}

.right-0 {
    right: 0
}

.right-10 {
    right: 2.5rem
}

.right-4 {
    right: 1rem
}

.right-8 {
    right: 2rem
}

.top-0 {
    top: 0
}

.top-10 {
    top: 2.5rem
}

.top-12 {
    top: 3rem
}

.top-4 {
    top: 1rem
}

.top-8 {
    top: 2rem
}

.top-\[-200\%\] {
    top: -200%
}

.top-px {
    top: 1px
}

.z-0 {
    z-index: 0
}

.z-10 {
    z-index: 10
}

.z-20 {
    z-index: 20
}

.z-30 {
    z-index: 30
}

.z-40 {
    z-index: 40
}

.z-50 {
    z-index: 50
}

.col-span-2 {
    grid-column: span 2/span 2
}

.-m-px {
    margin: -1px
}

.m-0 {
    margin: 0
}

.m-auto {
    margin: auto
}

.mx-2 {
    margin-left: .5rem;
    margin-right: .5rem
}

.mx-3 {
    margin-left: .75rem;
    margin-right: .75rem
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-12 {
    margin-bottom: 3rem;
    margin-top: 3rem
}

.my-4 {
    margin-bottom: 1rem;
    margin-top: 1rem
}

.my-6 {
    margin-bottom: 1.5rem;
    margin-top: 1.5rem
}

.my-7 {
    margin-bottom: 1.75rem;
    margin-top: 1.75rem
}

.my-8 {
    margin-bottom: 2rem;
    margin-top: 2rem
}

.\!mr-0 {
    margin-right: 0 !important
}

.-mb-10 {
    margin-bottom: -2.5rem
}

.-mb-8 {
    margin-bottom: -2rem
}

.-mb-px {
    margin-bottom: -1px
}

.-mr-8 {
    margin-right: -2rem
}

.-mt-20 {
    margin-top: -5rem
}

.-mt-6 {
    margin-top: -1.5rem
}

.mb-0 {
    margin-bottom: 0
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-10 {
    margin-bottom: 2.5rem
}

.mb-11 {
    margin-bottom: 2.75rem
}

.mb-12 {
    margin-bottom: 3rem
}

.mb-14 {
    margin-bottom: 3.5rem
}

.mb-16 {
    margin-bottom: 4rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mb-32 {
    margin-bottom: 8rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-5 {
    margin-bottom: 1.25rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.mb-8 {
    margin-bottom: 2rem
}

.mb-9 {
    margin-bottom: 2.25rem
}

.ml-1 {
    margin-left: .25rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-4 {
    margin-left: 1rem
}

.ml-5 {
    margin-left: 1.25rem
}

.ml-6 {
    margin-left: 1.5rem
}

.ml-\[-15\%\] {
    margin-left: -15%
}

.ml-\[-60\%\] {
    margin-left: -60%
}

.mr-4 {
    margin-right: 1rem
}

.mt-0 {
    margin-top: 0
}

.mt-1 {
    margin-top: .25rem
}

.mt-10 {
    margin-top: 2.5rem
}

.mt-11 {
    margin-top: 2.75rem
}

.mt-12 {
    margin-top: 3rem
}

.mt-14 {
    margin-top: 3.5rem
}

.mt-16 {
    margin-top: 4rem
}

.mt-18 {
    margin-top: 4.5rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-20 {
    margin-top: 5rem
}

.mt-3 {
    margin-top: .75rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-6 {
    margin-top: 1.5rem
}

.mt-8 {
    margin-top: 2rem
}

.mt-9 {
    margin-top: 2.25rem
}

.mt-\[90\%\] {
    margin-top: 90%
}

.block {
    display: block
}

.inline-block {
    display: inline-block
}

.inline {
    display: inline
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.grid {
    display: grid
}

.contents {
    display: contents
}

.hidden {
    display: none
}

.aspect-100\/70 {
    aspect-ratio: 100/70
}

.aspect-151\/100 {
    aspect-ratio: 151/100
}

.aspect-16\/10 {
    aspect-ratio: 16/10
}

.aspect-16\/9 {
    aspect-ratio: 16/9
}

.aspect-19\/10 {
    aspect-ratio: 19/10
}

.aspect-3\/2 {
    aspect-ratio: 3/2
}

.aspect-3\/4 {
    aspect-ratio: 3/4
}

.aspect-36\/25 {
    aspect-ratio: 36/25
}

.aspect-49\/25 {
    aspect-ratio: 49/25
}

.aspect-5\/2 {
    aspect-ratio: 5/2
}

.aspect-63\/25 {
    aspect-ratio: 63/25
}

.aspect-63\/50 {
    aspect-ratio: 63/50
}

.aspect-73\/100 {
    aspect-ratio: 73/100
}

.aspect-81\/65 {
    aspect-ratio: 81/65
}

.aspect-87\/50 {
    aspect-ratio: 87/50
}

.aspect-square {
    aspect-ratio: 1/1
}

.aspect-video {
    aspect-ratio: 16/9
}

.h-0 {
    height: 0
}

.h-0\.5 {
    height: .125rem
}

.h-1 {
    height: .25rem
}

.h-1\.5 {
    height: .375rem
}

.h-1\/3 {
    height: 33.333333%
}

.h-10 {
    height: 2.5rem
}

.h-11 {
    height: 2.75rem
}

.h-12 {
    height: 3rem
}

.h-14 {
    height: 3.5rem
}

.h-16 {
    height: 4rem
}

.h-2 {
    height: .5rem
}

.h-20 {
    height: 5rem
}

.h-24 {
    height: 6rem
}

.h-3 {
    height: .75rem
}

.h-3\/4 {
    height: 75%
}

.h-36 {
    height: 9rem
}

.h-4 {
    height: 1rem
}

.h-5 {
    height: 1.25rem
}

.h-56 {
    height: 14rem
}

.h-6 {
    height: 1.5rem
}

.h-7 {
    height: 1.75rem
}

.h-8 {
    height: 2rem
}

.h-9 {
    height: 2.25rem
}

.h-\[0\.375rem\] {
    height: .375rem
}

.h-\[110\%\] {
    height: 110%
}

.h-\[150\%\] {
    height: 150%
}

.h-\[200\%\] {
    height: 200%
}

.h-\[24px\] {
    height: 24px
}

.h-\[2px\] {
    height: 2px
}

.h-\[30rem\] {
    height: 30rem
}

.h-\[3px\] {
    height: 3px
}

.h-\[40\%\] {
    height: 40%
}

.h-\[42rem\] {
    height: 42rem
}

.h-\[45vh\] {
    height: 45vh
}

.h-\[50vh\] {
    height: 50vh
}

.h-\[90\%\] {
    height: 90%
}

.h-auto {
    height: auto
}

.h-fit {
    height: -moz-fit-content;
    height: fit-content
}

.h-full {
    height: 100%
}

.h-px {
    height: 1px
}

.h-screen {
    height: 100vh
}

.h-screen-50 {
    height: 50vh
}

.h-screen-80 {
    height: 80vh
}

.max-h-44 {
    max-height: 11rem
}

.max-h-8 {
    max-height: 2rem
}

.max-h-\[90\%\] {
    max-height: 90%
}

.min-h-0 {
    min-height: 0
}

.min-h-52 {
    min-height: 13rem
}

.min-h-72 {
    min-height: 18rem
}

.min-h-\[40rem\] {
    min-height: 40rem
}

.min-h-\[calc\(100vh-62px\)\] {
    min-height: calc(100vh - 62px)
}

.min-h-screen {
    min-height: 100vh
}

.min-h-screen-80 {
    min-height: 80vh
}

.min-h-xl {
    min-height: 36rem
}

.\!w-fit {
    width: -moz-fit-content !important;
    width: fit-content !important
}

.w-0 {
    width: 0
}

.w-1 {
    width: .25rem
}

.w-1\.5 {
    width: .375rem
}

.w-1\/2 {
    width: 50%
}

.w-10 {
    width: 2.5rem
}

.w-11 {
    width: 2.75rem
}

.w-11\/12 {
    width: 91.666667%
}

.w-12 {
    width: 3rem
}

.w-14 {
    width: 3.5rem
}

.w-16 {
    width: 4rem
}

.w-2 {
    width: .5rem
}

.w-2\.5 {
    width: .625rem
}

.w-2\/3 {
    width: 66.666667%
}

.w-2\/5 {
    width: 40%
}

.w-20 {
    width: 5rem
}

.w-24 {
    width: 6rem
}

.w-3 {
    width: .75rem
}

.w-3\/5 {
    width: 60%
}

.w-32 {
    width: 8rem
}

.w-36 {
    width: 9rem
}

.w-4 {
    width: 1rem
}

.w-48 {
    width: 12rem
}

.w-5 {
    width: 1.25rem
}

.w-6 {
    width: 1.5rem
}

.w-60 {
    width: 15rem
}

.w-7 {
    width: 1.75rem
}

.w-72 {
    width: 18rem
}

.w-8 {
    width: 2rem
}

.w-9 {
    width: 2.25rem
}

.w-\[115\%\] {
    width: 115%
}

.w-\[150\%\] {
    width: 150%
}

.w-\[180\%\] {
    width: 180%
}

.w-\[200\%\] {
    width: 200%
}

.w-\[224px\] {
    width: 224px
}

.w-\[24px\] {
    width: 24px
}

.w-\[30\%\] {
    width: 30%
}

.w-\[300vw\] {
    width: 300vw
}

.w-\[380px\] {
    width: 380px
}

.w-\[40\%\] {
    width: 40%
}

.w-\[53\%\] {
    width: 53%
}

.w-\[58\%\] {
    width: 58%
}

.w-\[589px\] {
    width: 589px
}

.w-\[63\%\] {
    width: 63%
}

.w-\[70vw\] {
    width: 70vw
}

.w-\[calc\(100\%-2\.5rem\)\] {
    width: calc(100% - 2.5rem)
}

.w-\[calc\(100vw-2rem\)\] {
    width: calc(100vw - 2rem)
}

.w-\[calc\(100vw-4rem\)\] {
    width: calc(100vw - 4rem)
}

.w-\[calc\(90vw-2rem\)\] {
    width: calc(90vw - 2rem)
}

.w-auto {
    width: auto
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-full {
    width: 100%
}

.w-min {
    width: -moz-min-content;
    width: min-content
}

.w-px {
    width: 1px
}

.w-screen {
    width: 100vw
}

.min-w-0 {
    min-width: 0
}

.min-w-36 {
    min-width: 9rem
}

.min-w-40 {
    min-width: 10rem
}

.min-w-fit {
    min-width: -moz-fit-content;
    min-width: fit-content
}

.max-w-2xl {
    max-width: 42rem
}

.max-w-3xl {
    max-width: 48rem
}

.max-w-4xl {
    max-width: 56rem
}

.max-w-5xl {
    max-width: 64rem
}

.max-w-\[1200px\] {
    max-width: 1200px
}

.max-w-\[150vh\] {
    max-width: 150vh
}

.max-w-\[16rem\] {
    max-width: 16rem
}

.max-w-\[22rem\] {
    max-width: 22rem
}

.max-w-\[26rem\] {
    max-width: 26rem
}

.max-w-\[36rem\] {
    max-width: 36rem
}

.max-w-\[40rem\] {
    max-width: 40rem
}

.max-w-\[7\.875rem\] {
    max-width: 7.875rem
}

.max-w-\[85\%\] {
    max-width: 85%
}

.max-w-\[875px\] {
    max-width: 875px
}

.max-w-full {
    max-width: 100%
}

.max-w-lg {
    max-width: 32rem
}

.max-w-md {
    max-width: 28rem
}

.max-w-none {
    max-width: none
}

.max-w-sm {
    max-width: 24rem
}

.max-w-xl {
    max-width: 36rem
}

.max-w-xs {
    max-width: 20rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-shrink-0,
.shrink-0 {
    flex-shrink: 0
}

.flex-grow {
    flex-grow: 1
}

.basis-1\/2 {
    flex-basis: 50%
}

.basis-1\/3 {
    flex-basis: 33.333333%
}

.basis-2\/3 {
    flex-basis: 66.666667%
}

.basis-2\/5 {
    flex-basis: 40%
}

.basis-3\/5 {
    flex-basis: 60%
}

.basis-\[40\%\] {
    flex-basis: 40%
}

.basis-\[50\%\] {
    flex-basis: 50%
}

.basis-\[calc\(100vw-3\.2rem\)\] {
    flex-basis: calc(100vw - 3.2rem)
}

.basis-\[calc\(100vw-4rem\+1rem\)\] {
    flex-basis: calc(100vw - 3rem)
}

.basis-\[calc\(83\.6vw\+1rem\)\] {
    flex-basis: calc(83.6vw + 1rem)
}

.basis-full {
    flex-basis: 100%
}

.origin-left {
    transform-origin: left
}

.origin-right {
    transform-origin: right
}

.origin-top {
    transform-origin: top
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%
}

.-translate-x-1\/2,
.-translate-x-4 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-x-4 {
    --tw-translate-x: -1rem
}

.-translate-x-\[5\%\] {
    --tw-translate-x: -5%
}

.-translate-x-\[5\%\],
.-translate-x-\[calc\(112px-50\%\)\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-x-\[calc\(112px-50\%\)\] {
    --tw-translate-x: calc(-112px - -50%)
}

.-translate-x-full {
    --tw-translate-x: -100%
}

.-translate-x-full,
.-translate-x-over {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-x-over {
    --tw-translate-x: -101%
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%
}

.-translate-y-1\/2,
.-translate-y-24 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-y-24 {
    --tw-translate-y: -6rem
}

.-translate-y-over {
    --tw-translate-y: -101%
}

.-translate-y-over,
.translate-x-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-0 {
    --tw-translate-x: 0px
}

.translate-x-4 {
    --tw-translate-x: 1rem
}

.translate-x-4,
.translate-x-\[-10\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-\[-10\%\] {
    --tw-translate-x: -10%
}

.translate-x-\[-20\%\] {
    --tw-translate-x: -20%
}

.translate-x-\[-20\%\],
.translate-x-\[-8\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-\[-8\%\] {
    --tw-translate-x: -8%
}

.translate-x-\[100\%\] {
    --tw-translate-x: 100%
}

.translate-x-\[100\%\],
.translate-x-\[5\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-\[5\%\] {
    --tw-translate-x: 5%
}

.translate-x-\[calc\(100\%\+\.19rem\)\] {
    --tw-translate-x: calc(100% + 0.19rem)
}

.translate-x-\[calc\(100\%\+0\.4rem\)\],
.translate-x-\[calc\(100\%\+\.19rem\)\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-\[calc\(100\%\+0\.4rem\)\] {
    --tw-translate-x: calc(100% + 0.4rem)
}

.translate-x-\[calc\(112px-50\%\)\] {
    --tw-translate-x: calc(112px - 50%)
}

.translate-x-\[calc\(112px-50\%\)\],
.translate-x-full {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-full {
    --tw-translate-x: 100%
}

.translate-x-over {
    --tw-translate-x: 101%
}

.translate-x-over,
.translate-y-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-0 {
    --tw-translate-y: 0px
}

.translate-y-3 {
    --tw-translate-y: 0.75rem
}

.translate-y-3,
.translate-y-4 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-y-4 {
    --tw-translate-y: 1rem
}

.translate-y-over {
    --tw-translate-y: 101%
}

.-rotate-180,
.translate-y-over {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-rotate-180 {
    --tw-rotate: -180deg
}

.-rotate-90 {
    --tw-rotate: -90deg
}

.-rotate-90,
.rotate-180 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-180 {
    --tw-rotate: 180deg
}

.-skew-x-6 {
    --tw-skew-x: -6deg
}

.-skew-x-6,
.skew-x-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.skew-x-0 {
    --tw-skew-x: 0deg
}

.skew-x-6 {
    --tw-skew-x: 6deg
}

.scale-0,
.skew-x-6 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0
}

.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1
}

.scale-100,
.scale-105 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05
}

.scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1
}

.scale-110,
.scale-125 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-125 {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25
}

.scale-150 {
    --tw-scale-x: 1.5;
    --tw-scale-y: 1.5
}

.scale-150,
.scale-50 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-50 {
    --tw-scale-x: .5;
    --tw-scale-y: .5
}

.scale-90 {
    --tw-scale-x: .9;
    --tw-scale-y: .9
}

.scale-90,
.scale-\[0\.96\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-\[0\.96\] {
    --tw-scale-x: 0.96;
    --tw-scale-y: 0.96
}

.scale-\[65\%\] {
    --tw-scale-x: 65%;
    --tw-scale-y: 65%
}

.-scale-x-100,
.scale-\[65\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-scale-x-100 {
    --tw-scale-x: -1
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

@keyframes ping {

    75%,
    to {
        opacity: 0;
        transform: scale(2)
    }
}

.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, .2, 1) infinite
}

.animate-spin-slow {
    animation: spin 3s linear infinite
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.animate-spin-super-slow {
    animation: spin 8.5s linear infinite
}

.cursor-default {
    cursor: default
}

.cursor-grab {
    cursor: grab
}

.cursor-not-allowed {
    cursor: not-allowed
}

.cursor-pointer {
    cursor: pointer
}

.cursor-wait {
    cursor: wait
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.resize {
    resize: both
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr))
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr))
}

.flex-row {
    flex-direction: row
}

.flex-col {
    flex-direction: column
}

.flex-col-reverse {
    flex-direction: column-reverse
}

.flex-wrap {
    flex-wrap: wrap
}

.place-content-center {
    place-content: center
}

.place-content-start {
    place-content: start
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.gap-0 {
    gap: 0
}

.gap-1 {
    gap: .25rem
}

.gap-10 {
    gap: 2.5rem
}

.gap-11 {
    gap: 2.75rem
}

.gap-12 {
    gap: 3rem
}

.gap-14 {
    gap: 3.5rem
}

.gap-16 {
    gap: 4rem
}

.gap-2 {
    gap: .5rem
}

.gap-3 {
    gap: .75rem
}

.gap-32 {
    gap: 8rem
}

.gap-4 {
    gap: 1rem
}

.gap-5 {
    gap: 1.25rem
}

.gap-6 {
    gap: 1.5rem
}

.gap-8 {
    gap: 2rem
}

.gap-\[10px\] {
    gap: 10px
}

.gap-\[300px\] {
    gap: 300px
}

.gap-x-6 {
    -moz-column-gap: 1.5rem;
    column-gap: 1.5rem
}

.gap-x-8 {
    -moz-column-gap: 2rem;
    column-gap: 2rem
}

.gap-y-10 {
    row-gap: 2.5rem
}

.gap-y-16 {
    row-gap: 4rem
}

.space-x-1>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.25rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.25rem*var(--tw-space-x-reverse))
}

.space-x-3>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.75rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.75rem*var(--tw-space-x-reverse))
}

.space-x-5>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(1.25rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(1.25rem*var(--tw-space-x-reverse))
}

.space-x-6>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(1.5rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(1.5rem*var(--tw-space-x-reverse))
}

.space-x-8>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(2rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(2rem*var(--tw-space-x-reverse))
}

.space-y-10>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(2.5rem*var(--tw-space-y-reverse));
    margin-top: calc(2.5rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-12>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(3rem*var(--tw-space-y-reverse));
    margin-top: calc(3rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-2>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(.5rem*var(--tw-space-y-reverse));
    margin-top: calc(.5rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-4>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(1rem*var(--tw-space-y-reverse));
    margin-top: calc(1rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-6>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(1.5rem*var(--tw-space-y-reverse));
    margin-top: calc(1.5rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-8>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(2rem*var(--tw-space-y-reverse));
    margin-top: calc(2rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-\[4px\]>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(4px*var(--tw-space-y-reverse));
    margin-top: calc(4px*(1 - var(--tw-space-y-reverse)))
}

.self-end {
    align-self: flex-end
}

.overflow-hidden {
    overflow: hidden
}

.overflow-visible {
    overflow: visible
}

.overflow-scroll {
    overflow: scroll
}

.overflow-y-auto {
    overflow-y: auto
}

.\!overflow-y-scroll {
    overflow-y: scroll !important
}

.whitespace-nowrap {
    white-space: nowrap
}

.text-balance {
    text-wrap: balance
}

.rounded {
    border-radius: .25rem
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-sm {
    border-radius: .125rem
}

.rounded-xl {
    border-radius: .75rem
}

.border {
    border-width: 1px
}

.border-2 {
    border-width: 2px
}

.border-4 {
    border-width: 4px
}

.border-\[3px\] {
    border-width: 3px
}

.border-y {
    border-top-width: 1px
}

.border-b,
.border-y {
    border-bottom-width: 1px
}

.border-l {
    border-left-width: 1px
}

.border-l-0 {
    border-left-width: 0
}

.border-l-2 {
    border-left-width: 2px
}

.border-r {
    border-right-width: 1px
}

.border-r-0 {
    border-right-width: 0
}

.border-t {
    border-top-width: 1px
}

.border-t-2 {
    border-top-width: 2px
}

.border-none {
    border-style: none
}

.\!border-primary {
    --tw-border-opacity: 1 !important;
    border-color: rgb(0 40 240/var(--tw-border-opacity)) !important
}

.\!border-primary-400 {
    --tw-border-opacity: 1 !important;
    border-color: rgb(56 136 255/var(--tw-border-opacity)) !important
}

.\!border-white {
    --tw-border-opacity: 1 !important;
    border-color: rgb(255 255 255/var(--tw-border-opacity)) !important
}

.border-black {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity))
}

.border-current {
    border-color: currentColor
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(226 232 240/var(--tw-border-opacity))
}

.border-light-gray {
    --tw-border-opacity: 1;
    border-color: rgb(217 217 217/var(--tw-border-opacity))
}

.border-light-gray\/40 {
    border-color: hsla(0, 0%, 85%, .4)
}

.border-neutral {
    --tw-border-opacity: 1;
    border-color: rgb(232 232 236/var(--tw-border-opacity))
}

.border-neutral-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity))
}

.border-neutral-500 {
    --tw-border-opacity: 1;
    border-color: rgb(107 114 128/var(--tw-border-opacity))
}

.border-primary {
    --tw-border-opacity: 1;
    border-color: rgb(0 40 240/var(--tw-border-opacity))
}

.border-primary-400 {
    --tw-border-opacity: 1;
    border-color: rgb(56 136 255/var(--tw-border-opacity))
}

.border-primary-400\/60 {
    border-color: rgba(56, 136, 255, .6)
}

.border-primary-600 {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 120/var(--tw-border-opacity))
}

.border-primary-800 {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 60/var(--tw-border-opacity))
}

.border-primary\/40 {
    border-color: rgba(0, 40, 240, .4)
}

.border-secondary {
    --tw-border-opacity: 1;
    border-color: rgb(208 255 144/var(--tw-border-opacity))
}

.border-transparent {
    border-color: transparent
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity))
}

.border-white\/20 {
    border-color: hsla(0, 0%, 100%, .2)
}

.\!border-opacity-100 {
    --tw-border-opacity: 1 !important
}

.border-opacity-10 {
    --tw-border-opacity: 0.1
}

.border-opacity-100 {
    --tw-border-opacity: 1
}

.border-opacity-20 {
    --tw-border-opacity: 0.2
}

.border-opacity-30 {
    --tw-border-opacity: 0.3
}

.border-opacity-40 {
    --tw-border-opacity: 0.4
}

.\!bg-neutral-700\/10 {
    background-color: rgba(55, 65, 81, .1) !important
}

.\!bg-primary-900 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(6 7 12/var(--tw-bg-opacity)) !important
}

.\!bg-white {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 255 255/var(--tw-bg-opacity)) !important
}

.\!bg-white\/20 {
    background-color: hsla(0, 0%, 100%, .2) !important
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity))
}

.bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(15 23 42/var(--tw-bg-opacity))
}

.bg-neutral {
    --tw-bg-opacity: 1;
    background-color: rgb(232 232 236/var(--tw-bg-opacity))
}

.bg-neutral-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity))
}

.bg-neutral-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity))
}

.bg-neutral-300 {
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219/var(--tw-bg-opacity))
}

.bg-neutral-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(156 163 175/var(--tw-bg-opacity))
}

.bg-neutral-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity))
}

.bg-primary {
    --tw-bg-opacity: 1;
    background-color: rgb(0 40 240/var(--tw-bg-opacity))
}

.bg-primary-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(176 255 255/var(--tw-bg-opacity))
}

.bg-primary-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(56 136 255/var(--tw-bg-opacity))
}

.bg-primary-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 120/var(--tw-bg-opacity))
}

.bg-primary-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 60/var(--tw-bg-opacity))
}

.bg-primary-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(6 7 12/var(--tw-bg-opacity))
}

.bg-primary-900\/30 {
    background-color: rgba(6, 7, 12, .3)
}

.bg-primary-900\/70 {
    background-color: rgba(6, 7, 12, .7)
}

.bg-pseudo-opaque {
    --tw-bg-opacity: 1;
    background-color: rgb(205 205 213/var(--tw-bg-opacity))
}

.bg-red-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38/var(--tw-bg-opacity))
}

.bg-secondary {
    --tw-bg-opacity: 1;
    background-color: rgb(55 111 183/var(--tw-bg-opacity))
}

.bg-transparent {
    background-color: transparent
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.bg-white\/10 {
    background-color: hsla(0, 0%, 100%, .1)
}

.bg-white\/20 {
    background-color: hsla(0, 0%, 100%, .2)
}

.bg-white\/40 {
    background-color: hsla(0, 0%, 100%, .4)
}

.bg-opacity-5 {
    --tw-bg-opacity: 0.05
}

.bg-opacity-80 {
    --tw-bg-opacity: 0.8
}

.bg-gradient-to-b {
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops))
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops))
}

.bg-gradient-to-l {
    background-image: linear-gradient(to left, var(--tw-gradient-stops))
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops))
}

.bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops))
}

.from-black {
    --tw-gradient-from: #000 var(--tw-gradient-from-position);
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-primary-800 {
    --tw-gradient-from: #00003c var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(0, 0, 60, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-primary-900 {
    --tw-gradient-from: #06070c var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(6, 7, 12, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-primary-900\/80 {
    --tw-gradient-from: rgba(6, 7, 12, .8) var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(6, 7, 12, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-20\% {
    --tw-gradient-from-position: 20%
}

.via-transparent {
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to)
}

.to-black {
    --tw-gradient-to: #000 var(--tw-gradient-to-position)
}

.to-transparent {
    --tw-gradient-to: transparent var(--tw-gradient-to-position)
}

.fill-current {
    fill: currentColor
}

.stroke-current {
    stroke: currentColor
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover
}

.p-1 {
    padding: .25rem
}

.p-10 {
    padding: 2.5rem
}

.p-16 {
    padding: 4rem
}

.p-2 {
    padding: .5rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-5 {
    padding: 1.25rem
}

.p-6 {
    padding: 1.5rem
}

.p-8 {
    padding: 2rem
}

.px-0 {
    padding-left: 0;
    padding-right: 0
}

.px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.px-\[0\.4rem\] {
    padding-left: .4rem;
    padding-right: .4rem
}

.px-\[5px\] {
    padding-left: 5px;
    padding-right: 5px
}

.py-1 {
    padding-bottom: .25rem;
    padding-top: .25rem
}

.py-10 {
    padding-bottom: 2.5rem;
    padding-top: 2.5rem
}

.py-12 {
    padding-bottom: 3rem;
    padding-top: 3rem
}

.py-14 {
    padding-bottom: 3.5rem;
    padding-top: 3.5rem
}

.py-16 {
    padding-bottom: 4rem;
    padding-top: 4rem
}

.py-2 {
    padding-bottom: .5rem;
    padding-top: .5rem
}

.py-2\.5 {
    padding-bottom: .625rem;
    padding-top: .625rem
}

.py-20 {
    padding-bottom: 5rem;
    padding-top: 5rem
}

.py-24 {
    padding-bottom: 6rem;
    padding-top: 6rem
}

.py-28 {
    padding-bottom: 7rem;
    padding-top: 7rem
}

.py-3 {
    padding-bottom: .75rem;
    padding-top: .75rem
}

.py-32 {
    padding-bottom: 8rem;
    padding-top: 8rem
}

.py-4 {
    padding-bottom: 1rem;
    padding-top: 1rem
}

.py-5 {
    padding-bottom: 1.25rem;
    padding-top: 1.25rem
}

.py-6 {
    padding-bottom: 1.5rem;
    padding-top: 1.5rem
}

.py-64 {
    padding-bottom: 16rem;
    padding-top: 16rem
}

.py-8 {
    padding-bottom: 2rem;
    padding-top: 2rem
}

.py-9 {
    padding-bottom: 2.25rem;
    padding-top: 2.25rem
}

.py-\[0\.125rem\] {
    padding-bottom: .125rem;
    padding-top: .125rem
}

.\!pb-0 {
    padding-bottom: 0 !important
}

.\!pt-0 {
    padding-top: 0 !important
}

.pb-10 {
    padding-bottom: 2.5rem
}

.pb-11 {
    padding-bottom: 2.75rem
}

.pb-12 {
    padding-bottom: 3rem
}

.pb-14 {
    padding-bottom: 3.5rem
}

.pb-16 {
    padding-bottom: 4rem
}

.pb-20 {
    padding-bottom: 5rem
}

.pb-24 {
    padding-bottom: 6rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-40 {
    padding-bottom: 10rem
}

.pb-5 {
    padding-bottom: 1.25rem
}

.pb-56 {
    padding-bottom: 14rem
}

.pb-6 {
    padding-bottom: 1.5rem
}

.pb-8 {
    padding-bottom: 2rem
}

.pb-9 {
    padding-bottom: 2.25rem
}

.pb-\[0\.9\%\] {
    padding-bottom: .9%
}

.pb-px {
    padding-bottom: 1px
}

.pl-24 {
    padding-left: 6rem
}

.pl-4 {
    padding-left: 1rem
}

.pl-5 {
    padding-left: 1.25rem
}

.pl-6 {
    padding-left: 1.5rem
}

.pr-1 {
    padding-right: .25rem
}

.pr-12 {
    padding-right: 3rem
}

.pr-3 {
    padding-right: .75rem
}

.pr-8 {
    padding-right: 2rem
}

.pt-0 {
    padding-top: 0
}

.pt-0\.5 {
    padding-top: .125rem
}

.pt-1 {
    padding-top: .25rem
}

.pt-10 {
    padding-top: 2.5rem
}

.pt-11 {
    padding-top: 2.75rem
}

.pt-12 {
    padding-top: 3rem
}

.pt-14 {
    padding-top: 3.5rem
}

.pt-16 {
    padding-top: 4rem
}

.pt-18 {
    padding-top: 4.5rem
}

.pt-2 {
    padding-top: .5rem
}

.pt-20 {
    padding-top: 5rem
}

.pt-32 {
    padding-top: 8rem
}

.pt-6 {
    padding-top: 1.5rem
}

.pt-7 {
    padding-top: 1.75rem
}

.pt-8 {
    padding-top: 2rem
}

.pt-\[calc\(0\.75rem\+1px\)\] {
    padding-top: calc(.75rem + 1px)
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

.font-sans {
    font-family: "Sharp Grotesk", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3\.5xl {
    font-size: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-4\.5xl {
    font-size: 2.625rem
}

.text-5\.25xl {
    font-size: 3.15rem
}

.text-5\.5xl {
    font-size: 3.5rem
}

.text-5xl {
    font-size: 3rem;
    line-height: 1
}

.text-6xl {
    font-size: 3.75rem;
    line-height: 1
}

.text-\[15px\] {
    font-size: 15px
}

.text-\[20px\] {
    font-size: 20px
}

.text-\[23\.5px\] {
    font-size: 23.5px
}

.text-\[28px\] {
    font-size: 28px
}

.text-\[32px\] {
    font-size: 32px
}

.text-\[52px\] {
    font-size: 52px
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.font-bold {
    font-weight: 700
}

.font-light {
    font-weight: 300
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.uppercase {
    text-transform: uppercase
}

.not-italic {
    font-style: normal
}

.\!leading-\[1\.15\] {
    line-height: 1.15 !important
}

.\!leading-none {
    line-height: 1 !important
}

.\!leading-tight {
    line-height: 1.25 !important
}

.leading-9 {
    line-height: 2.25rem
}

.leading-\[35px\] {
    line-height: 35px
}

.leading-none {
    line-height: 1
}

.leading-relaxed {
    line-height: 1.625
}

.leading-tight {
    line-height: 1.25
}

.tracking-tight {
    letter-spacing: -.025em
}

.tracking-wide {
    letter-spacing: .025em
}

.tracking-wider {
    letter-spacing: .05em
}

.tracking-widest {
    letter-spacing: .1em
}

.\!text-primary-600 {
    --tw-text-opacity: 1 !important;
    color: rgb(0 0 120/var(--tw-text-opacity)) !important
}

.\!text-primary-800 {
    --tw-text-opacity: 1 !important;
    color: rgb(0 0 60/var(--tw-text-opacity)) !important
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity))
}

.text-current {
    color: currentColor
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(100 116 139/var(--tw-text-opacity))
}

.text-inherit {
    color: inherit
}

.text-neutral {
    --tw-text-opacity: 1;
    color: rgb(232 232 236/var(--tw-text-opacity))
}

.text-neutral-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.text-neutral-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity))
}

.text-neutral-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55/var(--tw-text-opacity))
}

.text-neutral-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity))
}

.text-orange-300 {
    --tw-text-opacity: 1;
    color: rgb(253 186 116/var(--tw-text-opacity))
}

.text-primary {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity))
}

.text-primary-400 {
    --tw-text-opacity: 1;
    color: rgb(56 136 255/var(--tw-text-opacity))
}

.text-primary-600 {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity))
}

.text-primary-800 {
    --tw-text-opacity: 1;
    color: rgb(0 0 60/var(--tw-text-opacity))
}

.text-primary-900 {
    --tw-text-opacity: 1;
    color: rgb(6 7 12/var(--tw-text-opacity))
}

.text-primary\/40 {
    color: rgba(0, 40, 240, .4)
}

.text-pseudo-opaque {
    --tw-text-opacity: 1;
    color: rgb(205 205 213/var(--tw-text-opacity))
}

.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity))
}

.text-secondary {
    --tw-text-opacity: 1;
    color: rgb(55 111 183/var(--tw-text-opacity))
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.text-opacity-50 {
    --tw-text-opacity: 0.5
}

.text-opacity-60 {
    --tw-text-opacity: 0.6
}

.text-opacity-65 {
    --tw-text-opacity: 0.65
}

.text-opacity-80 {
    --tw-text-opacity: 0.8
}

.underline {
    text-decoration-line: underline
}

.overline {
    text-decoration-line: overline
}

.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.opacity-0 {
    opacity: 0
}

.opacity-100 {
    opacity: 1
}

.opacity-20 {
    opacity: .2
}

.opacity-30 {
    opacity: .3
}

.opacity-40 {
    opacity: .4
}

.opacity-50 {
    opacity: .5
}

.opacity-65 {
    opacity: .65
}

.opacity-80 {
    opacity: .8
}

.bg-blend-hard-light {
    background-blend-mode: hard-light
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color)
}

.shadow,
.shadow-md {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.outline {
    outline-style: solid
}

.outline-0 {
    outline-width: 0
}

.outline-1 {
    outline-width: 1px
}

.outline-offset-4 {
    outline-offset: 4px
}

.outline-primary {
    outline-color: #0028f0
}

.ring {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring,
.ring-1 {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.ring-primary {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

.blur {
    --tw-blur: blur(8px)
}

.blur,
.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.transition {
    transition-duration: .3s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

.transition-all {
    transition-duration: .3s;
    transition-property: all;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

.transition-colors {
    transition-duration: .3s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

.transition-opacity {
    transition-duration: .3s;
    transition-property: opacity;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

.transition-transform {
    transition-duration: .3s;
    transition-property: transform;
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

.delay-150 {
    transition-delay: .15s
}

.delay-75 {
    transition-delay: 75ms
}

.duration-200 {
    transition-duration: .2s
}

.duration-300 {
    transition-duration: .3s
}

.duration-500 {
    transition-duration: .5s
}

.duration-700 {
    transition-duration: .7s
}

.duration-75 {
    transition-duration: 75ms
}

.ease-in {
    transition-timing-function: cubic-bezier(.4, 0, 1, 1)
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-in-out-circ {
    transition-timing-function: cubic-bezier(.785, .135, .15, .86)
}

.ease-in-out-expo {
    transition-timing-function: cubic-bezier(1, 0, 0, 1)
}

.ease-in-out-quad {
    transition-timing-function: cubic-bezier(.455, .03, .515, .955)
}

.ease-in-out-quint {
    transition-timing-function: cubic-bezier(.86, 0, .07, 1)
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.ease-out-expo {
    transition-timing-function: cubic-bezier(.19, 1, .22, 1)
}

.ease-out-quint {
    transition-timing-function: cubic-bezier(.23, 1, .32, 1)
}

.no-scrollbar {
    scrollbar-width: none
}

.no-scrollbar::-webkit-scrollbar {
    display: none
}

.country-selector.weglot-dropdown {
    background-color: transparent;
    color: currentColor;
    font-family: "Sharp Grotesk", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    z-index: 30
}

.country-selector.weglot-dropdown label.wgcurrent {
    border-width: 0;
    color: currentColor
}

.country-selector.weglot-dropdown .wgcurrent a,
.country-selector.weglot-dropdown .wgcurrent span {
    padding-right: 2.5rem
}

.country-selector.weglot-dropdown a,
.country-selector.weglot-dropdown span {
    align-items: center;
    display: flex
}

.wglanguage-name {
    color: currentColor;
    font-size: .75rem !important;
    font-weight: 500 !important;
    line-height: 1rem !important;
    text-transform: uppercase
}

.country-selector.weglot-dropdown .wgcurrent:after {
    background: url(../images/arrow.svg) no-repeat;
    background-size: contain
}


@media (min-width:1536px) {
    .group:hover .group-hover\: {
        padding-left: calc(env(safe-area-inset-left, 0rem) + 4rem);
        padding-right: calc(env(safe-area-inset-right, 0rem) + 4rem)
    }
}

@media (min-width:768px) {
    .md\:fluid-container {
        margin-left: auto;
        margin-right: auto;
        max-width: 1536px;
        padding-left: calc(env(safe-area-inset-left, 0rem) + 2rem);
        padding-right: calc(env(safe-area-inset-right, 0rem) + 2rem);
        width: 100%
    }

    @media (min-width:1024px) {
        .md\:fluid-container {
            padding-left: calc(env(safe-area-inset-left, 0rem) + 3rem);
            padding-right: calc(env(safe-area-inset-right, 0rem) + 3rem)
        }
    }
}

@media (min-width:1024px) {
    .lg\:fluid-container {
        margin-left: auto;
        margin-right: auto;
        max-width: 1536px;
        padding-left: calc(env(safe-area-inset-left, 0rem) + 2rem);
        padding-right: calc(env(safe-area-inset-right, 0rem) + 2rem);
        width: 100%
    }

    .outer-grid>.lg\:w-full:last-child {
        margin-bottom: -5rem
    }

    @media (min-width:768px) {
        .outer-grid>.lg\:w-full:last-child {
            margin-bottom: -6rem
        }
    }

    @media (min-width:1024px) {
        .lg\:fluid-container {
            padding-left: calc(env(safe-area-inset-left, 0rem) + 3rem);
            padding-right: calc(env(safe-area-inset-right, 0rem) + 3rem)
        }

        .outer-grid>.lg\:w-full:last-child {
            margin-bottom: -6rem
        }
    }
}

@media (min-width:1280px) {
    .outer-grid>.xl\:w-full:last-child {
        margin-bottom: -5rem
    }

    @media (min-width:768px) {
        .outer-grid>.xl\:w-full:last-child {
            margin-bottom: -6rem
        }
    }

    @media (min-width:1024px) {
        .outer-grid>.xl\:w-full:last-child {
            margin-bottom: -6rem
        }
    }
}

.placeholder\:text-sm::-moz-placeholder {
    font-size: .875rem;
    line-height: 1.25rem
}

.placeholder\:text-sm::placeholder {
    font-size: .875rem;
    line-height: 1.25rem
}

.placeholder\:text-current::-moz-placeholder {
    color: currentColor
}

.placeholder\:text-current::placeholder {
    color: currentColor
}

.placeholder\:text-neutral-400::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.placeholder\:text-neutral-400::placeholder {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity))
}

.placeholder\:text-primary-800::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(0 0 60/var(--tw-text-opacity))
}

.placeholder\:text-primary-800::placeholder {
    --tw-text-opacity: 1;
    color: rgb(0 0 60/var(--tw-text-opacity))
}

.placeholder\:text-opacity-60::-moz-placeholder {
    --tw-text-opacity: 0.6
}

.placeholder\:text-opacity-60::placeholder {
    --tw-text-opacity: 0.6
}

.first\:pt-6:first-child {
    padding-top: 1.5rem
}

.last\:mb-0:last-child {
    margin-bottom: 0
}

.last\:border-b-0:last-child {
    border-bottom-width: 0
}

.last\:border-none:last-child {
    border-style: none
}

.last\:pb-6:last-child {
    padding-bottom: 1.5rem
}

.hover\:z-10:hover {
    z-index: 10
}

.hover\:scale-105:hover {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05
}

.hover\:scale-105:hover,
.hover\:scale-110:hover {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.hover\:scale-110:hover {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1
}

.hover\:scale-125:hover {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25
}

.hover\:scale-125:hover,
.hover\:scale-95:hover {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.hover\:scale-95:hover {
    --tw-scale-x: .95;
    --tw-scale-y: .95
}

.hover\:border-secondary:hover {
    --tw-border-opacity: 1;
    border-color: rgb(208 255 144/var(--tw-border-opacity))
}

.hover\:border-opacity-100:hover {
    --tw-border-opacity: 1
}

.hover\:bg-gray-950:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(2 6 23/var(--tw-bg-opacity))
}

.hover\:bg-primary:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(0 40 240/var(--tw-bg-opacity))
}

.hover\:bg-red-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68/var(--tw-bg-opacity))
}

.hover\:bg-secondary:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(208 255 144/var(--tw-bg-opacity))
}

.hover\:bg-sky-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(224 242 254/var(--tw-bg-opacity))
}

.hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

.hover\:px-9:hover {
    padding-left: 2.25rem;
    padding-right: 2.25rem
}

.hover\:text-current:hover {
    color: currentColor
}

.hover\:text-orange-500:hover {
    --tw-text-opacity: 1;
    color: rgb(249 115 22/var(--tw-text-opacity))
}

.hover\:text-pink-400:hover {
    --tw-text-opacity: 1;
    color: rgb(244 114 182/var(--tw-text-opacity))
}

.hover\:text-primary:hover {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity))
}

.hover\:text-primary-600:hover {
    --tw-text-opacity: 1;
    color: rgb(0 0 120/var(--tw-text-opacity))
}

.hover\:text-red-400:hover {
    --tw-text-opacity: 1;
    color: rgb(248 113 113/var(--tw-text-opacity))
}

.hover\:text-secondary:hover {
    --tw-text-opacity: 1;
    color: rgb(208 255 144/var(--tw-text-opacity))
}

.hover\:text-teal-400:hover {
    --tw-text-opacity: 1;
    color: rgb(45 212 191/var(--tw-text-opacity))
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.focus\:\!border-secondary:focus {
    --tw-border-opacity: 1 !important;
    border-color: rgb(208 255 144/var(--tw-border-opacity)) !important
}

.focus\:border-transparent:focus {
    border-color: transparent
}

.focus\:bg-primary:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(0 40 240/var(--tw-bg-opacity))
}

.focus\:p-1:focus {
    padding: .25rem
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:\!outline-2:focus {
    outline-width: 2px !important
}

.focus\:\!outline-secondary:focus {
    outline-color: #d0ff90 !important
}

.focus\:ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-0:focus,
.focus\:ring-2:focus {
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)
}

.focus\:ring-primary:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

.focus\:ring-secondary:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(208 255 144/var(--tw-ring-opacity))
}

.focus\:ring-transparent:focus {
    --tw-ring-color: transparent
}

.focus\:placeholder\:text-transparent:focus::-moz-placeholder {
    color: transparent
}

.focus\:placeholder\:text-transparent:focus::placeholder {
    color: transparent
}

.focus-visible\:translate-y-0.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:translate-y-0.focus-visible {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.focus-visible\:translate-y-0:focus-visible {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.focus-visible\:text-primary-400.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:text-primary-400.focus-visible {
    --tw-text-opacity: 1;
    color: rgb(56 136 255/var(--tw-text-opacity))
}

.focus-visible\:text-primary-400:focus-visible {
    --tw-text-opacity: 1;
    color: rgb(56 136 255/var(--tw-text-opacity))
}

.focus-visible\:opacity-100.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:opacity-100.focus-visible {
    opacity: 1
}

.focus-visible\:opacity-100:focus-visible {
    opacity: 1
}

.focus-visible\:outline-none.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:outline-none.focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus-visible\:outline-none:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus-visible\:ring-2.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:ring-2.focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-4.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:ring-4.focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-4:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus-visible\:ring-primary.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:ring-primary.focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

.focus-visible\:ring-primary:focus-visible {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

.focus-visible\:ring-primary-400\/70.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:ring-primary-400\/70.focus-visible {
    --tw-ring-color: rgba(56, 136, 255, .7)
}

.focus-visible\:ring-primary-400\/70:focus-visible {
    --tw-ring-color: rgba(56, 136, 255, .7)
}

.focus-visible\:ring-primary\/40.focus-visible.js-focus-visible,
.js-focus-visible .focus-visible\:ring-primary\/40.focus-visible {
    --tw-ring-color: rgba(0, 40, 240, .4)
}

.focus-visible\:ring-primary\/40:focus-visible {
    --tw-ring-color: rgba(0, 40, 240, .4)
}

.group:hover .group-hover\:z-20 {
    z-index: 20
}

.group:hover .group-hover\:w-3 {
    width: .75rem
}

.group:hover .group-hover\:-translate-x-6 {
    --tw-translate-x: -1.5rem
}

.group:hover .group-hover\:-translate-x-6,
.group:hover .group-hover\:-translate-y-8 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:-translate-y-8 {
    --tw-translate-y: -2rem
}

.group:hover .group-hover\:translate-x-2 {
    --tw-translate-x: 0.5rem
}

.group:hover .group-hover\:translate-x-2,
.group:hover .group-hover\:translate-x-6 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:translate-x-6 {
    --tw-translate-x: 1.5rem
}

.group:hover .group-hover\:translate-y-1\/4 {
    --tw-translate-y: 25%
}

.group:hover .group-hover\:scale-100,
.group:hover .group-hover\:translate-y-1\/4 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1
}

.group:hover .group-hover\:scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1
}

.group:hover .group-hover\:scale-110,
.group:hover .group-hover\:scale-125 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:scale-125 {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25
}

.group:hover .group-hover\:scale-90 {
    --tw-scale-x: .9;
    --tw-scale-y: .9
}

.group:hover .group-hover\:scale-90,
.group:hover .group-hover\:scale-\[95\%\] {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:scale-\[95\%\] {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%
}

.group:hover .group-hover\:border-primary {
    --tw-border-opacity: 1;
    border-color: rgb(0 40 240/var(--tw-border-opacity))
}

.group:hover .group-hover\:border-primary-800 {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 60/var(--tw-border-opacity))
}

.group:hover .group-hover\:border-secondary {
    --tw-border-opacity: 1;
    border-color: rgb(208 255 144/var(--tw-border-opacity))
}

.group:hover .group-hover\:border-opacity-100 {
    --tw-border-opacity: 1
}

.group:hover .group-hover\:bg-secondary {
    --tw-bg-opacity: 1;
    background-color: rgb(208 255 144/var(--tw-bg-opacity))
}

.group:hover .group-hover\:text-primary {
    --tw-text-opacity: 1;
    color: rgb(0 40 240/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-primary-800 {
    --tw-text-opacity: 1;
    color: rgb(0 0 60/var(--tw-text-opacity))
}

.group:hover .group-hover\:text-secondary {
    --tw-text-opacity: 1;
    color: rgb(208 255 144/var(--tw-text-opacity))
}

.group:hover .group-hover\:opacity-0 {
    opacity: 0
}

.group:hover .group-hover\:opacity-100 {
    opacity: 1
}

.group:focus .group-focus\:bg-secondary {
    --tw-bg-opacity: 1;
    background-color: rgb(208 255 144/var(--tw-bg-opacity))
}

.group.focus-visible.js-focus-visible .group-focus-visible\:translate-y-0,
.js-focus-visible .group.focus-visible .group-focus-visible\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:focus-visible .group-focus-visible\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group.focus-visible.js-focus-visible .group-focus-visible\:scale-105,
.js-focus-visible .group.focus-visible .group-focus-visible\:scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:focus-visible .group-focus-visible\:scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group.focus-visible.js-focus-visible .group-focus-visible\:scale-125,
.js-focus-visible .group.focus-visible .group-focus-visible\:scale-125 {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:focus-visible .group-focus-visible\:scale-125 {
    --tw-scale-x: 1.25;
    --tw-scale-y: 1.25;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group.focus-visible.js-focus-visible .group-focus-visible\:bg-primary-200,
.js-focus-visible .group.focus-visible .group-focus-visible\:bg-primary-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(176 255 255/var(--tw-bg-opacity))
}

.group:focus-visible .group-focus-visible\:bg-primary-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(176 255 255/var(--tw-bg-opacity))
}

.group.focus-visible.js-focus-visible .group-focus-visible\:text-primary-200,
.js-focus-visible .group.focus-visible .group-focus-visible\:text-primary-200 {
    --tw-text-opacity: 1;
    color: rgb(176 255 255/var(--tw-text-opacity))
}

.group:focus-visible .group-focus-visible\:text-primary-200 {
    --tw-text-opacity: 1;
    color: rgb(176 255 255/var(--tw-text-opacity))
}

.group.focus-visible.js-focus-visible .group-focus-visible\:opacity-100,
.js-focus-visible .group.focus-visible .group-focus-visible\:opacity-100 {
    opacity: 1
}

.group:focus-visible .group-focus-visible\:opacity-100 {
    opacity: 1
}

.group.focus-visible.js-focus-visible .group-focus-visible\:opacity-80,
.js-focus-visible .group.focus-visible .group-focus-visible\:opacity-80 {
    opacity: .8
}

.group:focus-visible .group-focus-visible\:opacity-80 {
    opacity: .8
}

.group.focus-visible.js-focus-visible .group-focus-visible\:ring-4,
.js-focus-visible .group.focus-visible .group-focus-visible\:ring-4 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.group:focus-visible .group-focus-visible\:ring-4 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.group.focus-visible.js-focus-visible .group-focus-visible\:ring-primary,
.js-focus-visible .group.focus-visible .group-focus-visible\:ring-primary {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

.group:focus-visible .group-focus-visible\:ring-primary {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(0 40 240/var(--tw-ring-opacity))
}

@media (prefers-reduced-motion:no-preference) {
    .motion-safe\:transition-transform {
        transition-duration: .3s;
        transition-property: transform;
        transition-timing-function: cubic-bezier(.455, .03, .515, .955)
    }
}

@media (min-width:640px) {
    .sm\:absolute {
        position: absolute
    }

    .sm\:-top-12 {
        top: -3rem
    }

    .sm\:bottom-0 {
        bottom: 0
    }

    .sm\:bottom-px {
        bottom: 1px
    }

    .sm\:top-0 {
        top: 0
    }

    .sm\:my-14 {
        margin-bottom: 3.5rem;
        margin-top: 3.5rem
    }

    .sm\:my-28 {
        margin-bottom: 7rem;
        margin-top: 7rem
    }

    .sm\:my-8 {
        margin-bottom: 2rem;
        margin-top: 2rem
    }

    .sm\:mb-0 {
        margin-bottom: 0
    }

    .sm\:mb-10 {
        margin-bottom: 2.5rem
    }

    .sm\:mb-14 {
        margin-bottom: 3.5rem
    }

    .sm\:mb-18 {
        margin-bottom: 4.5rem
    }

    .sm\:mb-6 {
        margin-bottom: 1.5rem
    }

    .sm\:mb-8 {
        margin-bottom: 2rem
    }

    .sm\:mb-9 {
        margin-bottom: 2.25rem
    }

    .sm\:ml-0 {
        margin-left: 0
    }

    .sm\:mr-0 {
        margin-right: 0
    }

    .sm\:mt-0 {
        margin-top: 0
    }

    .sm\:mt-14 {
        margin-top: 3.5rem
    }

    .sm\:mt-16 {
        margin-top: 4rem
    }

    .sm\:mt-18 {
        margin-top: 4.5rem
    }

    .sm\:mt-2 {
        margin-top: .5rem
    }

    .sm\:mt-36 {
        margin-top: 9rem
    }

    .sm\:mt-4 {
        margin-top: 1rem
    }

    .sm\:mt-48 {
        margin-top: 12rem
    }

    .sm\:mt-56 {
        margin-top: 14rem
    }

    .sm\:mt-9 {
        margin-top: 2.25rem
    }

    .sm\:block {
        display: block
    }

    .sm\:flex {
        display: flex
    }

    .sm\:inline-flex {
        display: inline-flex
    }

    .sm\:hidden {
        display: none
    }

    .sm\:aspect-151\/100 {
        aspect-ratio: 151/100
    }

    .sm\:aspect-16\/9 {
        aspect-ratio: 16/9
    }

    .sm\:aspect-27\/20 {
        aspect-ratio: 27/20
    }

    .sm\:aspect-41\/50 {
        aspect-ratio: 41/50
    }

    .sm\:aspect-85\/50 {
        aspect-ratio: 85/50
    }

    .sm\:aspect-video {
        aspect-ratio: 16/9
    }

    .sm\:h-18 {
        height: 4.5rem
    }

    .sm\:h-6 {
        height: 1.5rem
    }

    .sm\:h-8 {
        height: 2rem
    }

    .sm\:h-9 {
        height: 2.25rem
    }

    .sm\:h-auto {
        height: auto
    }

    .sm\:h-full {
        height: 100%
    }

    .sm\:min-h-screen {
        min-height: 100vh
    }

    .sm\:w-18 {
        width: 4.5rem
    }

    .sm\:w-2\/5 {
        width: 40%
    }

    .sm\:w-24 {
        width: 6rem
    }

    .sm\:w-3\/5 {
        width: 60%
    }

    .sm\:w-4\/5 {
        width: 80%
    }

    .sm\:w-6 {
        width: 1.5rem
    }

    .sm\:w-8 {
        width: 2rem
    }

    .sm\:w-9 {
        width: 2.25rem
    }

    .sm\:w-\[53\%\] {
        width: 53%
    }

    .sm\:w-auto {
        width: auto
    }

    .sm\:w-fit {
        width: -moz-fit-content;
        width: fit-content
    }

    .sm\:w-min {
        width: -moz-min-content;
        width: min-content
    }

    .sm\:max-w-md {
        max-width: 28rem
    }

    .sm\:basis-\[calc\(87\.3vw\+1rem\)\] {
        flex-basis: calc(87.3vw + 1rem)
    }

    .sm\:basis-auto {
        flex-basis: auto
    }

    .sm\:translate-x-0 {
        --tw-translate-x: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .sm\:flex-row {
        flex-direction: row
    }

    .sm\:flex-nowrap {
        flex-wrap: nowrap
    }

    .sm\:items-start {
        align-items: flex-start
    }

    .sm\:justify-end {
        justify-content: flex-end
    }

    .sm\:justify-between {
        justify-content: space-between
    }

    .sm\:gap-0 {
        gap: 0
    }

    .sm\:gap-1 {
        gap: .25rem
    }

    .sm\:gap-10 {
        gap: 2.5rem
    }

    .sm\:gap-4 {
        gap: 1rem
    }

    .sm\:gap-5 {
        gap: 1.25rem
    }

    .sm\:gap-6 {
        gap: 1.5rem
    }

    .sm\:gap-8 {
        gap: 2rem
    }

    .sm\:gap-9 {
        gap: 2.25rem
    }

    .sm\:space-y-0>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(0px*var(--tw-space-y-reverse));
        margin-top: calc(0px*(1 - var(--tw-space-y-reverse)))
    }

    .sm\:overflow-hidden {
        overflow: hidden
    }

    .sm\:whitespace-nowrap {
        white-space: nowrap
    }

    .sm\:border-l {
        border-left-width: 1px
    }

    .sm\:border-t {
        border-top-width: 1px
    }

    .sm\:border-none {
        border-style: none
    }

    .sm\:bg-neutral {
        --tw-bg-opacity: 1;
        background-color: rgb(232 232 236/var(--tw-bg-opacity))
    }

    .sm\:bg-opacity-30 {
        --tw-bg-opacity: 0.3
    }

    .sm\:p-0 {
        padding: 0
    }

    .sm\:p-3 {
        padding: .75rem
    }

    .sm\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .sm\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .sm\:py-0 {
        padding-bottom: 0;
        padding-top: 0
    }

    .sm\:py-10 {
        padding-bottom: 2.5rem;
        padding-top: 2.5rem
    }

    .sm\:py-14 {
        padding-bottom: 3.5rem;
        padding-top: 3.5rem
    }

    .sm\:py-16 {
        padding-bottom: 4rem;
        padding-top: 4rem
    }

    .sm\:py-24 {
        padding-bottom: 6rem;
        padding-top: 6rem
    }

    .sm\:pb-0 {
        padding-bottom: 0
    }

    .sm\:pb-12 {
        padding-bottom: 3rem
    }

    .sm\:pb-18 {
        padding-bottom: 4.5rem
    }

    .sm\:pb-20 {
        padding-bottom: 5rem
    }

    .sm\:pb-24 {
        padding-bottom: 6rem
    }

    .sm\:pb-6 {
        padding-bottom: 1.5rem
    }

    .sm\:pl-0 {
        padding-left: 0
    }

    .sm\:pl-8 {
        padding-left: 2rem
    }

    .sm\:pl-\[calc\(\(100vw-1536px\)\/2\)\] {
        padding-left: calc(50vw - 768px)
    }

    .sm\:pr-6 {
        padding-right: 1.5rem
    }

    .sm\:pr-8 {
        padding-right: 2rem
    }

    .sm\:pr-\[calc\(\(100vw-1536px\)\/2\)\] {
        padding-right: calc(50vw - 768px)
    }

    .sm\:pt-0 {
        padding-top: 0
    }

    .sm\:pt-18 {
        padding-top: 4.5rem
    }

    .sm\:pt-20 {
        padding-top: 5rem
    }

    .sm\:pt-6 {
        padding-top: 1.5rem
    }

    .sm\:text-left {
        text-align: left
    }

    .sm\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .sm\:text-3\.5xl {
        font-size: 2rem
    }

    .sm\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .sm\:text-5\.5xl {
        font-size: 3.5rem
    }

    .sm\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .sm\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .sm\:font-medium {
        font-weight: 500
    }

    .sm\:font-normal {
        font-weight: 400
    }

    .sm\:text-primary-600 {
        --tw-text-opacity: 1;
        color: rgb(0 0 120/var(--tw-text-opacity))
    }

    .sm\:hover\:bg-secondary:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(208 255 144/var(--tw-bg-opacity))
    }

    .sm\:hover\:bg-white:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(255 255 255/var(--tw-bg-opacity))
    }

    .sm\:hover\:text-primary:hover {
        --tw-text-opacity: 1;
        color: rgb(0 40 240/var(--tw-text-opacity))
    }
}

@media (min-width:768px) {
    .md\:inset-0 {
        inset: 0
    }

    .md\:-bottom-\[25\%\] {
        bottom: -25%
    }

    .md\:-top-16 {
        top: -4rem
    }

    .md\:bottom-10 {
        bottom: 2.5rem
    }

    .md\:left-10 {
        left: 2.5rem
    }

    .md\:right-10 {
        right: 2.5rem
    }

    .md\:right-14 {
        right: 3.5rem
    }

    .md\:top-auto {
        top: auto
    }

    .md\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .md\:-mb-40 {
        margin-bottom: -10rem
    }

    .md\:-mt-24 {
        margin-top: -6rem
    }

    .md\:mb-0 {
        margin-bottom: 0
    }

    .md\:mb-10 {
        margin-bottom: 2.5rem
    }

    .md\:mb-16 {
        margin-bottom: 4rem
    }

    .md\:mt-0 {
        margin-top: 0
    }

    .md\:mt-12 {
        margin-top: 3rem
    }

    .md\:mt-6 {
        margin-top: 1.5rem
    }

    .md\:block {
        display: block
    }

    .md\:flex {
        display: flex
    }

    .md\:hidden {
        display: none
    }

    .md\:aspect-16\/9 {
        aspect-ratio: 16/9
    }

    .md\:aspect-3\/2 {
        aspect-ratio: 3/2
    }

    .md\:aspect-43\/20 {
        aspect-ratio: 43/20
    }

    .md\:aspect-5\/2 {
        aspect-ratio: 5/2
    }

    .md\:aspect-video {
        aspect-ratio: 16/9
    }

    .md\:h-0 {
        height: 0
    }

    .md\:h-14 {
        height: 3.5rem
    }

    .md\:h-16 {
        height: 4rem
    }

    .md\:h-60 {
        height: 15rem
    }

    .md\:h-96 {
        height: 24rem
    }

    .md\:min-h-\[60vh\] {
        min-height: 60vh
    }

    .md\:w-1\/2 {
        width: 50%
    }

    .md\:w-14 {
        width: 3.5rem
    }

    .md\:w-16 {
        width: 4rem
    }

    .md\:w-2\/3 {
        width: 66.666667%
    }

    .md\:w-20 {
        width: 5rem
    }

    .md\:w-3\/5 {
        width: 60%
    }

    .md\:w-4 {
        width: 1rem
    }

    .md\:w-8 {
        width: 2rem
    }

    .md\:w-\[190vw\] {
        width: 190vw
    }

    .md\:w-\[20\%\] {
        width: 20%
    }

    .md\:w-\[25\%\] {
        width: 25%
    }

    .md\:w-\[30\%\] {
        width: 30%
    }

    .md\:w-\[45\%\] {
        width: 45%
    }

    .md\:w-\[7\.5rem\] {
        width: 7.5rem
    }

    .md\:w-\[80\%\] {
        width: 80%
    }

    .md\:w-auto {
        width: auto
    }

    .md\:w-fit {
        width: -moz-fit-content;
        width: fit-content
    }

    .md\:max-w-\[50\%\] {
        max-width: 50%
    }

    .md\:max-w-lg {
        max-width: 32rem
    }

    .md\:max-w-none {
        max-width: none
    }

    .md\:basis-1\/2 {
        flex-basis: 50%
    }

    .md\:basis-full {
        flex-basis: 100%
    }

    .md\:translate-x-\[-5\%\] {
        --tw-translate-x: -5%;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .md\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .md\:flex-row {
        flex-direction: row
    }

    .md\:items-start {
        align-items: flex-start
    }

    .md\:items-end {
        align-items: flex-end
    }

    .md\:justify-start {
        justify-content: flex-start
    }

    .md\:justify-end {
        justify-content: flex-end
    }

    .md\:justify-center {
        justify-content: center
    }

    .md\:justify-between {
        justify-content: space-between
    }

    .md\:justify-around {
        justify-content: space-around
    }

    .md\:gap-10 {
        gap: 2.5rem
    }

    .md\:gap-12 {
        gap: 3rem
    }

    .md\:gap-16 {
        gap: 4rem
    }

    .md\:gap-2 {
        gap: .5rem
    }

    .md\:gap-4 {
        gap: 1rem
    }

    .md\:gap-6 {
        gap: 1.5rem
    }

    .md\:gap-8 {
        gap: 2rem
    }

    .md\:space-x-4>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-left: calc(1rem*(1 - var(--tw-space-x-reverse)));
        margin-right: calc(1rem*var(--tw-space-x-reverse))
    }

    .md\:space-x-6>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-left: calc(1.5rem*(1 - var(--tw-space-x-reverse)));
        margin-right: calc(1.5rem*var(--tw-space-x-reverse))
    }

    .md\:space-y-0>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(0px*var(--tw-space-y-reverse));
        margin-top: calc(0px*(1 - var(--tw-space-y-reverse)))
    }

    .md\:space-y-20>:not([hidden])~:not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(5rem*var(--tw-space-y-reverse));
        margin-top: calc(5rem*(1 - var(--tw-space-y-reverse)))
    }

    .md\:border-0 {
        border-width: 0
    }

    .md\:border-y {
        border-bottom-width: 1px;
        border-top-width: 1px
    }

    .md\:border-b-0 {
        border-bottom-width: 0
    }

    .md\:border-r {
        border-right-width: 1px
    }

    .md\:border-t {
        border-top-width: 1px
    }

    .md\:border-none {
        border-style: none
    }

    .md\:from-30\% {
        --tw-gradient-from-position: 30%
    }

    .md\:p-0 {
        padding: 0
    }

    .md\:p-12 {
        padding: 3rem
    }

    .md\:p-2 {
        padding: .5rem
    }

    .md\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .md\:px-10 {
        padding-left: 2.5rem;
        padding-right: 2.5rem
    }

    .md\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .md\:px-16 {
        padding-left: 4rem;
        padding-right: 4rem
    }

    .md\:px-4 {
        padding-left: 1rem;
        padding-right: 1rem
    }

    .md\:py-12 {
        padding-bottom: 3rem;
        padding-top: 3rem
    }

    .md\:py-20 {
        padding-bottom: 5rem;
        padding-top: 5rem
    }

    .md\:py-28 {
        padding-bottom: 7rem;
        padding-top: 7rem
    }

    .md\:py-36 {
        padding-bottom: 9rem;
        padding-top: 9rem
    }

    .md\:py-8 {
        padding-bottom: 2rem;
        padding-top: 2rem
    }

    .md\:pb-0 {
        padding-bottom: 0
    }

    .md\:pb-4 {
        padding-bottom: 1rem
    }

    .md\:pb-8 {
        padding-bottom: 2rem
    }

    .md\:pl-6 {
        padding-left: 1.5rem
    }

    .md\:pr-6 {
        padding-right: 1.5rem
    }

    .md\:pt-0 {
        padding-top: 0
    }

    .md\:pt-10 {
        padding-top: 2.5rem
    }

    .md\:pt-12 {
        padding-top: 3rem
    }

    .md\:pt-16 {
        padding-top: 4rem
    }

    .md\:pt-36 {
        padding-top: 9rem
    }

    .md\:pt-6 {
        padding-top: 1.5rem
    }

    .md\:text-left {
        text-align: left
    }

    .md\:text-center {
        text-align: center
    }

    .md\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .md\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem
    }

    .md\:text-4\.5xl {
        font-size: 2.625rem
    }

    .md\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem
    }

    .md\:text-5\.5xl {
        font-size: 3.5rem
    }

    .md\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }

    .md\:text-6xl {
        font-size: 3.75rem;
        line-height: 1
    }

    .md\:text-\[52px\] {
        font-size: 52px
    }

    .md\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem
    }

    .md\:text-primary-600 {
        --tw-text-opacity: 1;
        color: rgb(0 0 120/var(--tw-text-opacity))
    }
}

@media (min-width:1024px) {
    .lg\:visible {
        visibility: visible
    }

    .lg\:absolute {
        position: absolute
    }

    .lg\:inset-y-0 {
        bottom: 0;
        top: 0
    }

    .lg\:left-0 {
        left: 0
    }

    .lg\:right-20 {
        right: 5rem
    }

    .lg\:mx-0 {
        margin-left: 0;
        margin-right: 0
    }

    .lg\:-mt-24 {
        margin-top: -6rem
    }

    .lg\:mb-0 {
        margin-bottom: 0
    }

    .lg\:mb-16 {
        margin-bottom: 4rem
    }

    .lg\:mb-20 {
        margin-bottom: 5rem
    }

    .lg\:mb-8 {
        margin-bottom: 2rem
    }

    .lg\:mr-0 {
        margin-right: 0
    }

    .lg\:mt-0 {
        margin-top: 0
    }

    .lg\:mt-10 {
        margin-top: 2.5rem
    }

    .lg\:mt-14 {
        margin-top: 3.5rem
    }

    .lg\:mt-16 {
        margin-top: 4rem
    }

    .lg\:block {
        display: block
    }

    .lg\:flex {
        display: flex
    }

    .lg\:grid {
        display: grid
    }

    .lg\:hidden {
        display: none
    }

    .lg\:aspect-16\/9 {
        aspect-ratio: 16/9
    }

    .lg\:aspect-2\/1 {
        aspect-ratio: 2/1
    }

    .lg\:aspect-5\/2 {
        aspect-ratio: 5/2
    }

    .lg\:aspect-auto {
        aspect-ratio: auto
    }

    .lg\:h-1 {
        height: .25rem
    }

    .lg\:h-1\.5 {
        height: .375rem
    }

    .lg\:h-16 {
        height: 4rem
    }

    .lg\:h-\[150\%\] {
        height: 150%
    }

    .lg\:h-auto {
        height: auto
    }

    .lg\:h-full {
        height: 100%
    }

    .lg\:min-h-screen {
        min-height: 100vh
    }

    .lg\:w-1\/2 {
        width: 50%
    }

    .lg\:w-1\/3 {
        width: 33.333333%
    }

    .lg\:w-16 {
        width: 4rem
    }

    .lg\:w-2\/3 {
        width: 66.666667%
    }

    .lg\:w-2\/5 {
        width: 40%
    }

    .lg\:w-3\/4 {
        width: 75%
    }

    .lg\:w-3\/5 {
        width: 60%
    }

    .lg\:w-\[35vw\] {
        width: 35vw
    }

    .lg\:w-\[36\%\] {
        width: 36%
    }

    .lg\:w-\[40\%\] {
        width: 40%
    }

    .lg\:w-\[40vw\] {
        width: 40vw
    }

    .lg\:w-\[45\%\] {
        width: 45%
    }

    .lg\:w-\[50vw\] {
        width: 50vw
    }

    .lg\:w-\[52\.8vw\] {
        width: 52.8vw
    }

    .lg\:w-\[60vw\] {
        width: 60vw
    }

    .lg\:w-\[72\.5vw\] {
        width: 72.5vw
    }

    .lg\:w-\[calc\(100vw-6rem\)\] {
        width: calc(100vw - 6rem)
    }

    .lg\:w-full {
        width: 100%
    }

    .lg\:min-w-40 {
        min-width: 10rem
    }

    .lg\:max-w-\[11rem\] {
        max-width: 11rem
    }

    .lg\:max-w-\[36rem\] {
        max-width: 36rem
    }

    .lg\:max-w-\[40rem\] {
        max-width: 40rem
    }

    .lg\:max-w-\[72\.6vw\] {
        max-width: 72.6vw
    }

    .lg\:max-w-none {
        max-width: none
    }

    .lg\:max-w-sm {
        max-width: 24rem
    }

    .lg\:basis-1\/3 {
        flex-basis: 33.333333%
    }

    .lg\:basis-\[calc\(100vw-6rem\+1rem\)\] {
        flex-basis: calc(100vw - 5rem)
    }

    .lg\:translate-x-\[-47\%\] {
        --tw-translate-x: -47%;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .lg\:cursor-none {
        cursor: none
    }

    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .lg\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr))
    }

    .lg\:flex-row {
        flex-direction: row
    }

    .lg\:flex-row-reverse {
        flex-direction: row-reverse
    }

    .lg\:items-start {
        align-items: flex-start
    }

    .lg\:items-center {
        align-items: center
    }

    .lg\:items-stretch {
        align-items: stretch
    }

    .lg\:justify-start {
        justify-content: flex-start
    }

    .lg\:justify-end {
        justify-content: flex-end
    }

    .lg\:justify-center {
        justify-content: center
    }

    .lg\:justify-between {
        justify-content: space-between
    }

    .lg\:gap-16 {
        gap: 4rem
    }

    .lg\:gap-28 {
        gap: 7rem
    }

    .lg\:space-x-10>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-left: calc(2.5rem*(1 - var(--tw-space-x-reverse)));
        margin-right: calc(2.5rem*var(--tw-space-x-reverse))
    }

    .lg\:overflow-visible {
        overflow: visible
    }

    .lg\:border-\[6px\] {
        border-width: 6px
    }

    .lg\:border-y {
        border-top-width: 1px
    }

    .lg\:border-b,
    .lg\:border-y {
        border-bottom-width: 1px
    }

    .lg\:border-b-0 {
        border-bottom-width: 0
    }

    .lg\:border-b-2 {
        border-bottom-width: 2px
    }

    .lg\:border-r {
        border-right-width: 1px
    }

    .lg\:border-t-0 {
        border-top-width: 0
    }

    .lg\:border-t-2 {
        border-top-width: 2px
    }

    .lg\:border-none {
        border-style: none
    }

    .lg\:border-light-gray {
        --tw-border-opacity: 1;
        border-color: rgb(217 217 217/var(--tw-border-opacity))
    }

    .lg\:bg-neutral {
        --tw-bg-opacity: 1;
        background-color: rgb(232 232 236/var(--tw-bg-opacity))
    }

    .lg\:p-0 {
        padding: 0
    }

    .lg\:p-16 {
        padding: 4rem
    }

    .lg\:px-0 {
        padding-left: 0;
        padding-right: 0
    }

    .lg\:px-3 {
        padding-left: .75rem;
        padding-right: .75rem
    }

    .lg\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .lg\:py-0 {
        padding-bottom: 0;
        padding-top: 0
    }

    .lg\:py-1 {
        padding-bottom: .25rem;
        padding-top: .25rem
    }

    .lg\:py-10 {
        padding-bottom: 2.5rem;
        padding-top: 2.5rem
    }

    .lg\:py-12 {
        padding-bottom: 3rem;
        padding-top: 3rem
    }

    .lg\:py-16 {
        padding-bottom: 4rem;
        padding-top: 4rem
    }

    .lg\:py-20 {
        padding-bottom: 5rem;
        padding-top: 5rem
    }

    .lg\:py-28 {
        padding-bottom: 7rem;
        padding-top: 7rem
    }

    .lg\:py-6 {
        padding-bottom: 1.5rem;
        padding-top: 1.5rem
    }

    .lg\:pb-0 {
        padding-bottom: 0
    }

    .lg\:pb-6 {
        padding-bottom: 1.5rem
    }

    .lg\:pr-0 {
        padding-right: 0
    }

    .lg\:pr-11 {
        padding-right: 2.75rem
    }

    .lg\:pr-2 {
        padding-right: .5rem
    }

    .lg\:pr-8 {
        padding-right: 2rem
    }

    .lg\:pt-12 {
        padding-top: 3rem
    }

    .lg\:pt-16 {
        padding-top: 4rem
    }

    .lg\:pt-24 {
        padding-top: 6rem
    }

    .lg\:text-left {
        text-align: left
    }

    .lg\:text-center {
        text-align: center
    }

    .lg\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem
    }

    .lg\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem
    }

    .lg\:text-5\.5xl {
        font-size: 3.5rem
    }

    .lg\:text-7xl {
        font-size: 4.5rem;
        line-height: 1
    }

    .lg\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }

    .lg\:text-primary {
        --tw-text-opacity: 1;
        color: rgb(0 40 240/var(--tw-text-opacity))
    }

    .lg\:hover\:scale-105:hover {
        --tw-scale-x: 1.05;
        --tw-scale-y: 1.05
    }

    .lg\:hover\:scale-105:hover,
    .lg\:hover\:scale-125:hover {
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .lg\:hover\:scale-125:hover {
        --tw-scale-x: 1.25;
        --tw-scale-y: 1.25
    }

    .lg\:hover\:bg-primary:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(0 40 240/var(--tw-bg-opacity))
    }

    .lg\:hover\:bg-secondary:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(208 255 144/var(--tw-bg-opacity))
    }

    .lg\:hover\:text-primary:hover {
        --tw-text-opacity: 1;
        color: rgb(0 40 240/var(--tw-text-opacity))
    }

    .lg\:hover\:text-secondary:hover {
        --tw-text-opacity: 1;
        color: rgb(208 255 144/var(--tw-text-opacity))
    }

    .lg\:hover\:opacity-100:hover {
        opacity: 1
    }

    .group:hover .lg\:group-hover\:scale-100 {
        --tw-scale-x: 1;
        --tw-scale-y: 1
    }

    .group:hover .lg\:group-hover\:scale-100,
    .group:hover .lg\:group-hover\:scale-105 {
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .group:hover .lg\:group-hover\:scale-105 {
        --tw-scale-x: 1.05;
        --tw-scale-y: 1.05
    }

    .group:hover .lg\:group-hover\:scale-110 {
        --tw-scale-x: 1.1;
        --tw-scale-y: 1.1
    }

    .group:hover .lg\:group-hover\:scale-110,
    .group:hover .lg\:group-hover\:scale-125 {
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .group:hover .lg\:group-hover\:scale-125 {
        --tw-scale-x: 1.25;
        --tw-scale-y: 1.25
    }

    .group:hover .lg\:group-hover\:scale-\[115\%\] {
        --tw-scale-x: 115%;
        --tw-scale-y: 115%
    }

    .group:hover .lg\:group-hover\:scale-\[115\%\],
    .group:hover .lg\:group-hover\:scale-\[118\%\] {
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .group:hover .lg\:group-hover\:scale-\[118\%\] {
        --tw-scale-x: 118%;
        --tw-scale-y: 118%
    }

    .group:hover .lg\:group-hover\:scale-\[120\%\] {
        --tw-scale-x: 120%;
        --tw-scale-y: 120%;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .group:hover .lg\:group-hover\:border-primary {
        --tw-border-opacity: 1;
        border-color: rgb(0 40 240/var(--tw-border-opacity))
    }

    .group:hover .lg\:group-hover\:border-secondary {
        --tw-border-opacity: 1;
        border-color: rgb(208 255 144/var(--tw-border-opacity))
    }

    .group:hover .lg\:group-hover\:bg-primary {
        --tw-bg-opacity: 1;
        background-color: rgb(0 40 240/var(--tw-bg-opacity))
    }

    .group:hover .lg\:group-hover\:text-primary {
        --tw-text-opacity: 1;
        color: rgb(0 40 240/var(--tw-text-opacity))
    }

    .group:hover .lg\:group-hover\:text-primary-400 {
        --tw-text-opacity: 1;
        color: rgb(56 136 255/var(--tw-text-opacity))
    }

    .group:hover .lg\:group-hover\:text-primary-600 {
        --tw-text-opacity: 1;
        color: rgb(0 0 120/var(--tw-text-opacity))
    }

    .group:hover .lg\:group-hover\:opacity-100 {
        opacity: 1
    }

    .group:hover .lg\:group-hover\:opacity-40 {
        opacity: .4
    }

    .group:hover .lg\:group-hover\:opacity-75 {
        opacity: .75
    }
}

@media (min-width:1280px) {
    .xl\:mb-9 {
        margin-bottom: 2.25rem
    }

    .xl\:mt-0 {
        margin-top: 0
    }

    .xl\:block {
        display: block
    }

    .xl\:flex {
        display: flex
    }

    .xl\:aspect-63\/25 {
        aspect-ratio: 63/25
    }

    .xl\:aspect-auto {
        aspect-ratio: auto
    }

    .xl\:h-px {
        height: 1px
    }

    .xl\:min-h-\[calc\(100vh-70px\)\] {
        min-height: calc(100vh - 70px)
    }

    .xl\:w-1\/2 {
        width: 50%
    }

    .xl\:w-1\/3 {
        width: 33.333333%
    }

    .xl\:w-2\/3 {
        width: 66.666667%
    }

    .xl\:w-64 {
        width: 16rem
    }

    .xl\:w-\[27vw\] {
        width: 27vw
    }

    .xl\:w-\[30\%\] {
        width: 30%
    }

    .xl\:w-\[65\%\] {
        width: 65%
    }

    .xl\:w-auto {
        width: auto
    }

    .xl\:w-full {
        width: 100%
    }

    .xl\:min-w-120 {
        min-width: 30rem
    }

    .xl\:max-w-2xl {
        max-width: 42rem
    }

    .xl\:max-w-80 {
        max-width: 20rem
    }

    .xl\:basis-1\/4 {
        flex-basis: 25%
    }

    .xl\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .xl\:grid-cols-6 {
        grid-template-columns: repeat(6, minmax(0, 1fr))
    }

    .xl\:flex-row {
        flex-direction: row
    }

    .xl\:flex-row-reverse {
        flex-direction: row-reverse
    }

    .xl\:flex-nowrap {
        flex-wrap: nowrap
    }

    .xl\:justify-end {
        justify-content: flex-end
    }

    .xl\:justify-between {
        justify-content: space-between
    }

    .xl\:gap-16 {
        gap: 4rem
    }

    .xl\:gap-20 {
        gap: 5rem
    }

    .xl\:gap-24 {
        gap: 6rem
    }

    .xl\:gap-28 {
        gap: 7rem
    }

    .xl\:gap-6 {
        gap: 1.5rem
    }

    .xl\:gap-\[400px\] {
        gap: 400px
    }

    .xl\:space-x-4>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-left: calc(1rem*(1 - var(--tw-space-x-reverse)));
        margin-right: calc(1rem*var(--tw-space-x-reverse))
    }

    .xl\:p-3 {
        padding: .75rem
    }

    .xl\:px-12 {
        padding-left: 3rem;
        padding-right: 3rem
    }

    .xl\:px-3 {
        padding-left: .75rem;
        padding-right: .75rem
    }

    .xl\:py-16 {
        padding-bottom: 4rem;
        padding-top: 4rem
    }

    .xl\:py-20 {
        padding-bottom: 5rem;
        padding-top: 5rem
    }

    .xl\:py-60 {
        padding-bottom: 15rem;
        padding-top: 15rem
    }

    .xl\:pb-24 {
        padding-bottom: 6rem
    }

    .xl\:pl-4 {
        padding-left: 1rem
    }

    .xl\:text-4\.5xl {
        font-size: 2.625rem
    }

    .xl\:text-5\.5xl {
        font-size: 3.5rem
    }

    .xl\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }
}

@media (min-width:1536px) {
    .\32xl\:ml-6 {
        margin-left: 1.5rem
    }

    .\32xl\:w-2\/5 {
        width: 40%
    }

    .\32xl\:w-3\/5 {
        width: 60%
    }

    .\32xl\:w-\[50vw\] {
        width: 50vw
    }

    .\32xl\:w-\[55\%\] {
        width: 55%
    }

    .\32xl\:w-\[calc\(100vw-6rem\+1rem-\(100vw-1536px\)\)\] {
        width: calc(-5rem + 1536px)
    }

    .\32xl\:max-w-3xl,
    .\32xl\:max-w-\[48rem\] {
        max-width: 48rem
    }

    .\32xl\:max-w-lg {
        max-width: 32rem
    }

    .\32xl\:basis-\[calc\(100vw-6rem\+1rem-\(100vw-1536px\)\)\] {
        flex-basis: calc(-5rem + 1536px)
    }

    .\32xl\:gap-\[543px\] {
        gap: 543px
    }

    .\32xl\:space-x-10>:not([hidden])~:not([hidden]) {
        --tw-space-x-reverse: 0;
        margin-left: calc(2.5rem*(1 - var(--tw-space-x-reverse)));
        margin-right: calc(2.5rem*var(--tw-space-x-reverse))
    }

    .\32xl\:py-24 {
        padding-bottom: 6rem;
        padding-top: 6rem
    }

    .\32xl\:py-32 {
        padding-bottom: 8rem;
        padding-top: 8rem
    }

    .\32xl\:py-40 {
        padding-bottom: 10rem;
        padding-top: 10rem
    }

    .\32xl\:pl-6 {
        padding-left: 1.5rem
    }
}

@media (min-width:1680px) {
    .\33xl\:pr-\[calc\(\(100vw-1536px\)\/2\)\] {
        padding-right: calc(50vw - 768px)
    }
}

@media (min-width:2100px) {
    .ultrawide\:aspect-16\/7 {
        aspect-ratio: 16/7
    }

    .ultrawide\:w-1\/4 {
        width: 25%
    }

    .ultrawide\:w-3\/4 {
        width: 75%
    }

    .ultrawide\:w-\[30vw\] {
        width: 30vw
    }

    .ultrawide\:w-\[35vw\] {
        width: 35vw
    }

    .ultrawide\:w-\[37vw\] {
        width: 37vw
    }

    .ultrawide\:max-w-\[1800px\] {
        max-width: 1800px
    }
}

@media (min-height:1500px) and (min-width:2500px) {
    .tall-and-wide\:min-h-screen-75 {
        min-height: 75vh
    }
}

@media (max-height:700px) and (min-width:1200px) {
    .short-desktop\:aspect-16\/7 {
        aspect-ratio: 16/7
    }

    .short-desktop\:min-h-screen {
        min-height: 100vh
    }

    .short-desktop\:max-w-\[120vh\] {
        max-width: 120vh
    }
}

.gallery-container{
    margin: 0;
    display: grid;
    place-items: center; 
}

.team-gallery {
    --size: 100px;
    display: grid;
    grid-template-columns: repeat(12, var(--size));
    grid-auto-rows: var(--size);
    margin-bottom: var(--size);
    place-items: start center;
    gap: 5px;
    
    &:has(:hover) img:not(:hover),
    &:has(:focus) img:not(:focus){
      filter: brightness(0.5) contrast(0.5);
    }
  
    & img {
      object-fit: cover;
      object-position: top;
      width: calc(var(--size) * 2);
      height: calc(var(--size) * 2);
      clip-path: path("M90,10 C100,0 100,0 110,10 190,90 190,90 190,90 200,100 200,100 190,110 190,110 110,190 110,190 100,200 100,200 90,190 90,190 10,110 10,110 0,100 0,100 10,90Z");
      transition: clip-path 0.25s, filter 0.75s;
      grid-column: auto / span 2;
      border-radius: 5px;
  
      &:nth-child(11n - 5) { 
        grid-column: 2 / span 2 
      }
  
      &:hover,
      &:focus {
        clip-path: path("M0,0 C0,0 200,0 200,0 200,0 200,100 200,100 200,100 200,200 200,200 200,200 100,200 100,200 100,200 100,200 0,200 0,200 0,100 0,100 0,100 0,100 0,100Z");
        z-index: 1;
        transition: clip-path 0.25s, filter 0.25s;
      }
      
      &:focus {
        outline: 1px dashed black;
        outline-offset: -5px;
      }
    }
}

#brasfield-gorrie-header-weglot {
    display:none;
}

.company-padd {
    padding: 80px;
}