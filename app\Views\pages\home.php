<div id="smooth-wrapper" style="inset: 0px; width: 100%; height: 100%; position: fixed; overflow: hidden;">
<div id="smooth-content" style="translate: none; rotate: none; scale: none; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -5145.21, 0, 1); box-sizing: border-box; width: 100%; overflow: visible;">
<div data-portal="destination" data-portal-namespace="secondary" class="home page-template-default page page-id-6 -mb-px">
    <div x-data="" x-init="
        $store.header.bg_color = 'white';
        $store.header.bg = false;
        $store.header.theme = 'dark';
        $store.header.border = true;
        $store.header.border_color = 'white_opaque';
        " class="hidden"></div>
    <div id="maincontent"></div>
    <section x-data="" class="relative w-full overflow-hidden bg-primary-600">
        <div x-data="{
            activeTab: 1,
            totalTabs: 5,
            speed: 5000,
            paused: false,
            play() {
            if (this.totalTabs < 1) {
            return
            }
            if (!this.paused) {
            this.activeTab = this.activeTab < this.totalTabs ? this.activeTab + 1 : 1;
            }
            setTimeout(() => this.play(), this.speed);
            },
            autoPlay() {
            setTimeout(() => this.play(), avalanche.delay.enter * 1000 + this.speed);
            window.addEventListener('blur', () => {
            this.paused = true;
            });
            window.addEventListener('focus', () => {
            this.paused = false;
            });
            ScrollTrigger.create({
            trigger: this.$refs.hero,
            start: 'top 100%',
            end: 'bottom 20%',
            onEnter: () => {
            this.paused = false;
            },
            onEnterBack: () => {
            this.paused = false;
            },
            onLeave: () => {
            this.paused = true;
            },
            onLeaveBack: () => {
            this.paused = true;
            },
            });
            },
            changeTab(tab) {
            this.paused = true;
            this.activeTab = tab;
            },
            init() {
            this.autoPlay();
            },
            }" x-ref="hero" class="relative flex flex-col justify-end w-full min-h-screen-80 sm:min-h-screen tall-and-wide:min-h-screen-75">
            <div class="absolute inset-0 h-full w-full flex flex-col justify-center" x-show="activeTab == 1" x-transition:enter="transition duration-500 ease-in-out-circ" x-transition:enter-start="opacity-0 skew-x-6 scale-125 translate-x-[5%]" x-transition:enter-end="opacity-100 skew-x-0 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-circ" x-transition:leave-start="opacity-100 skew-x-0 translate-x-0" x-transition:leave-end="opacity-0 -skew-x-6 scale-125 -translate-x-[5%]" style="display: none;">
                <div x-data="animate()" x-init="
                    scale = {
                    ...scale, active: true,
                    start: 1.5,
                    end: 1,
                    duration: 0.5,
                    ease: 'circ.out',
                    };
                    setTrigger($refs.hero);
                    scrollTrigger = true;
                    trigger = $refs.hero;
                    opacity.duration = 0.6;
                    mounted();
                    " x-ref="element" class="absolute inset-0">
                    <div class="absolute inset-0 overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">
                            <picture>
                                <img src="/images/hero-images/easterncomp.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                            </picture>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute inset-0 h-full w-full flex flex-col justify-center" x-show="activeTab == 2" x-transition:enter="transition duration-500 ease-in-out-circ" x-transition:enter-start="opacity-0 skew-x-6 scale-125 translate-x-[5%]" x-transition:enter-end="opacity-100 skew-x-0 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-circ" x-transition:leave-start="opacity-100 skew-x-0 translate-x-0" x-transition:leave-end="opacity-0 -skew-x-6 scale-125 -translate-x-[5%]" style="display: none;">
                <div class="absolute inset-0">
                    <div class="absolute inset-0 overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full">
                            <picture>
                                <img src="/images/hero-images/commercial.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                            </picture>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute inset-0 h-full w-full flex flex-col justify-center" x-show="activeTab == 3" x-transition:enter="transition duration-500 ease-in-out-circ" x-transition:enter-start="opacity-0 skew-x-6 scale-125 translate-x-[5%]" x-transition:enter-end="opacity-100 skew-x-0 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-circ" x-transition:leave-start="opacity-100 skew-x-0 translate-x-0" x-transition:leave-end="opacity-0 -skew-x-6 scale-125 -translate-x-[5%]" style="display: none;">
                <div class="absolute inset-0">
                    <div class="absolute inset-0 overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full">
                            <picture>
                                <img src="/images/hero-images/industrial.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                            </picture>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute inset-0 h-full w-full flex flex-col justify-center" x-show="activeTab == 4" x-transition:enter="transition duration-500 ease-in-out-circ" x-transition:enter-start="opacity-0 skew-x-6 scale-125 translate-x-[5%]" x-transition:enter-end="opacity-100 skew-x-0 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-circ" x-transition:leave-start="opacity-100 skew-x-0 translate-x-0" x-transition:leave-end="opacity-0 -skew-x-6 scale-125 -translate-x-[5%]" style="display: none;">
                <div class="absolute inset-0">
                    <div class="absolute inset-0 overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full">
                            <picture>
                                <img src="/images/hero-images/power-solutions.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                            </picture>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute inset-0 h-full w-full flex flex-col justify-center" x-show="activeTab == 5" x-transition:enter="transition duration-500 ease-in-out-circ" x-transition:enter-start="opacity-0 skew-x-6 scale-125 translate-x-[5%]" x-transition:enter-end="opacity-100 skew-x-0 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-circ" x-transition:leave-start="opacity-100 skew-x-0 translate-x-0" x-transition:leave-end="opacity-0 -skew-x-6 scale-125 -translate-x-[5%]">
                <div class="absolute inset-0">
                    <div class="absolute inset-0 overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full">
                            <picture>
                                <img src="/images/hero-images/glass-aluminum.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                            </picture>
                        </div>
                    </div>
                </div>
            </div>

            <div x-show="activeTab == 1" class="absolute inset-0 h-full w-full flex flex-col justify-center" style="display: none;">
                <div class="fluid-container relative z-10 py-32 md:py-36 text-white">
                    <div x-show="activeTab == 1" x-transition:enter="transition duration-500 delay-75 ease-in-out-quad" x-transition:enter-start="opacity-0 translate-x-4" x-transition:enter-end="opacity-100 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-quad" x-transition:leave-start="opacity-100 translate-x-0" x-transition:leave-end="opacity-0 -translate-x-4" class="max-w-3xl space-y-4" style="display: none;">
                        <h2 x-data="fadeIn()" x-init="
                            scrollTrigger = false;
                            delay = avalanche.delay.enter + 0.4;
                            mounted();
                            " x-ref="element" class="text-current font-sans leading-none font-semibold text-5xl md:text-6xl text-balance" style="opacity: 1;">
                            Together, We Make it Happen
                        </h2>
                        <p x-data="fadeIn()" x-init="
                            scrollTrigger = false;
                            delay = avalanche.delay.enter + 0.4;
                            mounted();
                            " x-ref="element" class="text-current m-0 pb-4 text-base md:text-2xl font-normal" style="opacity: 1;">
                            Four unique specialized companies with a common focus.
                        </p>
                        <div x-data="animate()" x-init="
                            yPercent = {
                            ...yPercent, active: true,
                            start: 10,
                            end: 0,
                            };
                            setTrigger($refs.hero);
                            start = 'top bottom';
                            delay = delay + 0.7;
                            mounted();
                            " x-ref="element" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="w-full md:w-fit relative inline-flex hover:z-10">
                                    <a class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-sm uppercase tracking-wide border px-10 hover:px-9 py-6 text-primary bg-white border-primary hover:bg-secondary myheaderbg" href="/our-companies">
                                        What We Do
                                        <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-show="activeTab == 2" class="absolute inset-0 h-full w-full flex flex-col justify-center" style="display: none;">
                <div class="fluid-container relative z-10 py-32 md:py-36 text-white">
                    <div x-show="activeTab == 2" x-transition:enter="transition duration-500 delay-75 ease-in-out-quad" x-transition:enter-start="opacity-0 translate-x-4" x-transition:enter-end="opacity-100 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-quad" x-transition:leave-start="opacity-100 translate-x-0" x-transition:leave-end="opacity-0 -translate-x-4" class="max-w-3xl space-y-4" style="display: none;">
                        <h2 class="text-current font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                            Eastern Builders
                        </h2>
                        <p class="text-current m-0 pb-4 text-base md:text-2xl font-normal">
                            Eastern Builders provides an extensive range of construction services tailored to meet each project's unique needs.
                        </p>
                        <div>
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="w-full md:w-fit relative inline-flex hover:z-10">
                                    <a class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-sm uppercase tracking-wide border px-10 hover:px-9 py-6 text-primary bg-white border-primary hover:bg-secondary myheaderbg" href="/our-companies">
                                        Commercial
                                        <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-show="activeTab == 3" class="absolute inset-0 h-full w-full flex flex-col justify-center" style="display: none;">
                <div class="fluid-container relative z-10 py-32 md:py-36 text-white">
                    <div x-show="activeTab == 3" x-transition:enter="transition duration-500 delay-75 ease-in-out-quad" x-transition:enter-start="opacity-0 translate-x-4" x-transition:enter-end="opacity-100 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-quad" x-transition:leave-start="opacity-100 translate-x-0" x-transition:leave-end="opacity-0 -translate-x-4" class="max-w-3xl space-y-4" style="display: none;">
                        <h2 class="text-current font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                            Eastern Contractors Corporation
                        </h2>
                        <p class="text-current m-0 pb-4 text-base md:text-2xl font-normal">
                            A US-based company offering industrial general contracting services in North America.
                        </p>
                        <div>
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="w-full md:w-fit relative inline-flex hover:z-10">
                                    <a class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-sm uppercase tracking-wide border px-10 hover:px-9 py-6 text-primary bg-white border-primary hover:bg-secondary myheaderbg" href="/our-companies">
                                        Industrial
                                        <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-show="activeTab == 4" class="absolute inset-0 h-full w-full flex flex-col justify-center" style="display: none;">
                <div class="fluid-container relative z-10 py-32 md:py-36 text-white">
                    <div x-show="activeTab == 4" x-transition:enter="transition duration-500 delay-75 ease-in-out-quad" x-transition:enter-start="opacity-0 translate-x-4" x-transition:enter-end="opacity-100 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-quad" x-transition:leave-start="opacity-100 translate-x-0" x-transition:leave-end="opacity-0 -translate-x-4" class="max-w-3xl space-y-4" style="display: none;">
                        <h2 class="text-current font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                            Eastern Power Solutions
                        </h2>
                        <p class="text-current m-0 pb-4 text-base md:text-2xl font-normal">
                            Eastern Power Solutions has a deep commitment to promulgating renewable and sustainable energy systems for the future.
                        </p>
                        <div>
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="w-full md:w-fit relative inline-flex hover:z-10">
                                    <a class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-sm uppercase tracking-wide border px-10 hover:px-9 py-6 text-primary bg-white border-primary hover:bg-secondary myheaderbg myheaderbg" href="/our-companies">
                                        Power Solutions
                                        <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-show="activeTab == 5" class="absolute inset-0 h-full w-full flex flex-col justify-center">
                <div class="fluid-container relative z-10 py-32 md:py-36 text-white">
                    <div x-show="activeTab == 5" x-transition:enter="transition duration-500 delay-75 ease-in-out-quad" x-transition:enter-start="opacity-0 translate-x-4" x-transition:enter-end="opacity-100 translate-x-0" x-transition:leave="transition duration-500 ease-in-out-quad" x-transition:leave-start="opacity-100 translate-x-0" x-transition:leave-end="opacity-0 -translate-x-4" class="max-w-3xl space-y-4">
                        <h2 class="text-current font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                            Eastern Glass and Aluminum
                        </h2>
                        <p class="text-current m-0 pb-4 text-base md:text-2xl font-normal">
                            Award Winning Company for Top 50 Contract Glaziers 2024.One of the leading specialty contractors in the industry.
                        </p>
                        <div>
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="w-full md:w-fit relative inline-flex hover:z-10">
                                    <a class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-sm uppercase tracking-wide border px-10 hover:px-9 py-6 text-primary bg-white border-primary hover:bg-secondary myheaderbg" href="/our-companies">
                                        Glass & Aluminum
                                        <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute bottom-0 inset-x-0 h-1/3 bg-gradient-to-t from-primary-900 to-transparent"></div>
            <div class="absolute inset-0 bg-primary-900/30"></div>
            <div class="fluid-container relative z-20 hidden lg:block">
                <div class="relative">
                    <div class="absolute top-px inset-x-0 h-px overflow-hidden">
                        <div x-data="animate()" x-init="
                            opacity.active = false;
                            xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                            duration: 0.8,
                            ease: 'circ.inOut',
                            };
                            setTrigger($refs.hero);
                            delay = delay + 0.3;
                            mounted();
                            " x-ref="element" class="w-full h-full bg-white/40" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                    </div>
                    <div x-data="animate()" x-init="
                        mm.add(avalanche.breakpoint(avalanche.screens.md), () => {
                        setTrigger($refs.hero);
                        element = [...$refs.hero.querySelectorAll('[data-tab]')];
                        stagger = 0.05;
                        delay = delay + 1;
                        mounted();
                        });
                        " class="relative pb-3 flex">
                        <button data-tab="" @click="changeTab(1)" title="Together, We Make it Happen" class="relative flex-grow py-4 select-none focus-visible:outline-none group" style="opacity: 1;">
                            <div class="absolute top-0 inset-x-0 h-[3px] overflow-hidden">
                                <div x-show="activeTab == 1" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="transform -translate-x-full" x-transition:enter-end="transform translate-x-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="transform translate-x-0" x-transition:leave-end="transform translate-x-full" class="w-full h-full bg-secondary" style="display: none;"></div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-end">
                                <div class="w-2 h-2 rounded-full bg-primary-200 opacity-0 group-focus-visible:opacity-100 translate-y-4 group-focus-visible:translate-y-0 transition duration-200"></div>
                            </div>
                            <div class="relative select-none overflow-hidden group-focus-visible:text-primary-200">
                                <div x-show="activeTab == 1" x-transition:enter="transition-transform delay-75 duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-secondary font-bold" style="display: none;">
                                    <div class="w-4"><img class="logo-modal" src="/images/logo/site-favicon.png" alt="Eastern Builders Logo" class="w-full h-auto">
                                    </div>
                                </div>
                                <div x-show="activeTab != 1" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-white opacity-65 lg:group-hover:opacity-100 transition duration-200">
                                    <div class="w-4"><img class="logo-modal" src="/images/logo/site-favicon.png" alt="Eastern Builders Logo" class="w-full h-auto">
                                    </div>
                                </div>
                                <div class="invisible opacity-0">Initial</div>
                            </div>
                        </button>
                        <button data-tab="" @click="changeTab(2)" title="Commercial" class="relative flex-grow py-4 select-none focus-visible:outline-none group" style="opacity: 1;">
                            <div class="absolute top-0 inset-x-0 h-[3px] overflow-hidden">
                                <div x-show="activeTab == 2" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="transform -translate-x-full" x-transition:enter-end="transform translate-x-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="transform translate-x-0" x-transition:leave-end="transform translate-x-full" class="w-full h-full bg-secondary" style="display: none;"></div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-end">
                                <div class="w-2 h-2 rounded-full bg-primary-200 opacity-0 group-focus-visible:opacity-100 translate-y-4 group-focus-visible:translate-y-0 transition duration-200"></div>
                            </div>
                            <div class="relative select-none overflow-hidden group-focus-visible:text-primary-200">
                                <div x-show="activeTab == 2" x-transition:enter="transition-transform delay-75 duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-secondary font-bold" style="display: none;">
                                    Commercial
                                </div>
                                <div x-show="activeTab != 2" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-white opacity-65 lg:group-hover:opacity-100 transition duration-200">
                                    Commercial
                                </div>
                                <div class="invisible opacity-0">Commercial</div>
                            </div>
                        </button>
                        <button data-tab="" @click="changeTab(3)" title="Industrial" class="relative flex-grow py-4 select-none focus-visible:outline-none group" style="opacity: 1;">
                            <div class="absolute top-0 inset-x-0 h-[3px] overflow-hidden">
                                <div x-show="activeTab == 3" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="transform -translate-x-full" x-transition:enter-end="transform translate-x-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="transform translate-x-0" x-transition:leave-end="transform translate-x-full" class="w-full h-full bg-secondary" style="display: none;"></div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-end">
                                <div class="w-2 h-2 rounded-full bg-primary-200 opacity-0 group-focus-visible:opacity-100 translate-y-4 group-focus-visible:translate-y-0 transition duration-200"></div>
                            </div>
                            <div class="relative select-none overflow-hidden group-focus-visible:text-primary-200">
                                <div x-show="activeTab == 3" x-transition:enter="transition-transform delay-75 duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-secondary font-bold" style="display: none;">
                                    Industrial
                                </div>
                                <div x-show="activeTab != 3" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-white opacity-65 lg:group-hover:opacity-100 transition duration-200">
                                    Industrial
                                </div>
                                <div class="invisible opacity-0">Industrial</div>
                            </div>
                        </button>
                        <button data-tab="" @click="changeTab(4)" title="Power Solutions" class="relative flex-grow py-4 select-none focus-visible:outline-none group" style="opacity: 1;">
                            <div class="absolute top-0 inset-x-0 h-[3px] overflow-hidden">
                                <div x-show="activeTab == 4" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="transform -translate-x-full" x-transition:enter-end="transform translate-x-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="transform translate-x-0" x-transition:leave-end="transform translate-x-full" class="w-full h-full bg-secondary" style="display: none;"></div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-end">
                                <div class="w-2 h-2 rounded-full bg-primary-200 opacity-0 group-focus-visible:opacity-100 translate-y-4 group-focus-visible:translate-y-0 transition duration-200"></div>
                            </div>
                            <div class="relative select-none overflow-hidden group-focus-visible:text-primary-200">
                                <div x-show="activeTab == 4" x-transition:enter="transition-transform delay-75 duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-secondary font-bold" style="display: none;">
                                    Power Solutions
                                </div>
                                <div x-show="activeTab != 4" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-white opacity-65 lg:group-hover:opacity-100 transition duration-200">
                                    Power Solutions
                                </div>
                                <div class="invisible opacity-0">Power Solutions</div>
                            </div>
                        </button>
                        <button data-tab="" @click="changeTab(5)" title="Glass & Aluminum" class="relative flex-grow py-4 select-none focus-visible:outline-none group" style="opacity: 1;">
                            <div class="absolute top-0 inset-x-0 h-[3px] overflow-hidden">
                                <div x-show="activeTab == 5" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="transform -translate-x-full" x-transition:enter-end="transform translate-x-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="transform translate-x-0" x-transition:leave-end="transform translate-x-full" class="w-full h-full bg-secondary"></div>
                            </div>
                            <div class="absolute inset-0 flex justify-center items-end">
                                <div class="w-2 h-2 rounded-full bg-primary-200 opacity-0 group-focus-visible:opacity-100 translate-y-4 group-focus-visible:translate-y-0 transition duration-200"></div>
                            </div>
                            <div class="relative select-none overflow-hidden group-focus-visible:text-primary-200">
                                <div x-show="activeTab == 5" x-transition:enter="transition-transform delay-75 duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-secondary font-bold">
                                    Glass & Aluminum
                                </div>
                                <div x-show="activeTab != 5" x-transition:enter="transition-transform duration-300 ease-in-out-circ" x-transition:enter-start="-translate-y-over" x-transition:enter-end="translate-y-0" x-transition:leave="transition-transform duration-300 ease-in-out-circ" x-transition:leave-start="translate-y-0" x-transition:leave-end="translate-y-over" class="absolute inset-0 flex justify-center items-center text-white opacity-65 lg:group-hover:opacity-100 transition duration-200" style="display: none;">
                                    Glass & Aluminum
                                </div>
                                <div class="invisible opacity-0">Glass & Aluminum</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <div x-data="animate()" x-init="
                mm.add(avalanche.breakpoint(avalanche.screens.md, 'max'), () => {
                setTrigger($refs.hero);
                element = [...$refs.hero.querySelectorAll('[data-bullet]')];
                stagger = 0.05;
                delay = delay + 1;
                mounted();
                });
                " class="relative z-20 flex lg:hidden items-center justify-center gap-5 mb-8">
                <div data-bullet="">
                    <button @click="activeTab = 1" title="Together, We Make it Happen" class="relative flex-shrink-0 w-7 sm:w-8 h-7 sm:h-8 p-1 rounded-full select-none focus-visible:outline-none group">
                        <div class="relative w-full h-full rounded-full transition duration-200 bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105" :class="
                            activeTab == 1
                            ? 'bg-secondary'
                            : 'bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105'
                            "></div>
                        <div class="absolute inset-0 rounded-full border border-secondary transition duration-200 opacity-0 scale-0" :class="
                            activeTab == 1
                            ? 'opacity-100'
                            : 'opacity-0 scale-0'
                            "></div>
                    </button>
                </div>
                <div data-bullet="">
                    <button @click="activeTab = 2" title="Commercial" class="relative flex-shrink-0 w-7 sm:w-8 h-7 sm:h-8 p-1 rounded-full select-none focus-visible:outline-none group">
                        <div class="relative w-full h-full rounded-full transition duration-200 bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105" :class="
                            activeTab == 2
                            ? 'bg-secondary'
                            : 'bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105'
                            "></div>
                        <div class="absolute inset-0 rounded-full border border-secondary transition duration-200 opacity-0 scale-0" :class="
                            activeTab == 2
                            ? 'opacity-100'
                            : 'opacity-0 scale-0'
                            "></div>
                    </button>
                </div>
                <div data-bullet="">
                    <button @click="activeTab = 3" title="Industrial" class="relative flex-shrink-0 w-7 sm:w-8 h-7 sm:h-8 p-1 rounded-full select-none focus-visible:outline-none group">
                        <div class="relative w-full h-full rounded-full transition duration-200 bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105" :class="
                            activeTab == 3
                            ? 'bg-secondary'
                            : 'bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105'
                            "></div>
                        <div class="absolute inset-0 rounded-full border border-secondary transition duration-200 opacity-0 scale-0" :class="
                            activeTab == 3
                            ? 'opacity-100'
                            : 'opacity-0 scale-0'
                            "></div>
                    </button>
                </div>
                <div data-bullet="">
                    <button @click="activeTab = 4" title="Power Solutions" class="relative flex-shrink-0 w-7 sm:w-8 h-7 sm:h-8 p-1 rounded-full select-none focus-visible:outline-none group">
                        <div class="relative w-full h-full rounded-full transition duration-200 bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105" :class="
                            activeTab == 4
                            ? 'bg-secondary'
                            : 'bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105'
                            "></div>
                        <div class="absolute inset-0 rounded-full border border-secondary transition duration-200 opacity-0 scale-0" :class="
                            activeTab == 4
                            ? 'opacity-100'
                            : 'opacity-0 scale-0'
                            "></div>
                    </button>
                </div>
                <div data-bullet="">
                    <button @click="activeTab = 5" title="Glass & Aluminum" class="relative flex-shrink-0 w-7 sm:w-8 h-7 sm:h-8 p-1 rounded-full select-none focus-visible:outline-none group">
                        <div class="relative w-full h-full rounded-full transition duration-200 bg-secondary" :class="
                            activeTab == 5
                            ? 'bg-secondary'
                            : 'bg-white group-focus-visible:bg-primary-200 opacity-65 lg:group-hover:opacity-100 group-focus-visible:opacity-100 scale-[65%] group-focus-visible:scale-105'
                            "></div>
                        <div class="absolute inset-0 rounded-full border border-secondary transition duration-200 opacity-100" :class="
                            activeTab == 5
                            ? 'opacity-100'
                            : 'opacity-0 scale-0'
                            "></div>
                    </button>
                </div>
            </div>
        </div>
    </section>
    <main class="!pt-0 !pb-0 relative outer-grid overflow-hidden -mb-px">
        <section x-data="" class="relative overflow-hidden">
            <div x-ref="container" class="relative hidden lg:flex flex-col justify-center min-h-[calc(100vh-62px)] xl:min-h-[calc(100vh-70px)] tall-and-wide:min-h-screen-75 overflow-hidden">
                <div x-data="animate()" x-init="
                    mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                    opacity.active = false;
                    scale = {
                    ...scale, active: true,
                    start: 1.24,
                    end: 1.24,
                    ease: 'none',
                    };
                    yPercent = {
                    ...yPercent, active: true,
                    start: 12,
                    end: -12,
                    ease: 'none',
                    };
                    trigger = $refs.container;
                    scrub = 1.2;
                    start = 'top bottom';
                    mounted();
                    });
                    " class="right-0 absolute top-0 w-3/5 2xl:w-[55%] h-full hidden lg:block">
                    <div x-ref="element" class="stats-mask origin-left relative w-full h-full overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%) scale(1.24, 1.24);">
                        <div x-data="animate()" x-init="
                            mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                            element = [...$refs.container.querySelectorAll('[data-curtain]')];
                            opacity.active = false;
                            xPercent = {
                            ...xPercent, active: true,
                            start: 80,
                            end: 0,
                            duration: 1,
                            };
                            yPercent = {
                            ...yPercent, active: true,
                            start: 20,
                            end: 0,
                            duration: 1,
                            };
                            setTrigger($refs.container);
                            stagger = 0.2;
                            mounted();
                            });
                            " class="relative w-full h-full">
                            <div x-data="animate()" x-init="
                                mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                                element = $refs.mediaWrapper;
                                opacity.active = false;
                                scale = {
                                ...scale, active: true,
                                start: 1.25,
                                end: 1,
                                duration: 0.6,
                                ease: 'circ.out',
                                };
                                setTrigger($refs.container);
                                delay = delay + 1;
                                mounted();
                                });
                                " x-ref="mediaWrapper" class="absolute inset-0 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                <div x-data="animate()" x-init="
                                    mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                                    opacity.duration = 0.2;
                                    element = $refs.media;
                                    setTrigger($refs.container);
                                    delay = delay + 0.7;
                                    mounted();
                                    });
                                    " x-ref="media" class="absolute inset-0 overflow-hidden" style="opacity: 1;">
                                    <div x-data="animate()" x-init="
                                        mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 1.16,
                                        end: 1.16,
                                        ease: 'none',
                                        };
                                        yPercent = {
                                        ...yPercent, active: true,
                                        start: -8,
                                        end: 8,
                                        ease: 'none',
                                        };
                                        trigger = $refs.container;
                                        scrub = 1.2;
                                        start = 'top bottom';
                                        mounted();
                                        });
                                        " class="absolute inset-0 overflow-hidden">
                                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 8%) scale(1.16, 1.16);">
                                            <picture>
                                                <source srcset="
                                                    /images/slider-images/slider-img-6.jpg 3200w
                                                    " sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                <img src="/images/slider-images/slider-img-6.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 50% 50%" alt="Aerial view of a Brasfield & Gorrie construction site with a tall crane in a cityscape, showcasing urban buildings, roads, and vehicles under a cloudy sky.">
                                            </picture>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div x-data="animate()" x-init="
                                mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                                opacity.start = 1;
                                opacity.end = 0;
                                setTrigger($refs.container);
                                delay = delay + 1;
                                mounted();
                                });
                                " x-ref="element" class="absolute inset-0" style="opacity: 0;">
                                <div data-curtain="" class="absolute -left-1/4 -top-1/4 h-[150%] aspect-square bg-secondary rounded-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                <div data-curtain="" class="absolute -left-1/4 -top-1/4 h-[150%] aspect-square bg-primary-600 rounded-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fluid-container py-28">
                    <div class="max-w-[22rem]">
                        <h2 x-data="animateText()" x-init="
                            opacity.active = false;
                            yPercent = {
                            ...yPercent, active: true,
                            start: 102,
                            duration: 0.4,
                            };
                            type = `chars`;
                            wordsClass = avalanche.textClass.words.h1;
                            stagger = 0.03;
                            setTrigger($refs.content);
                            mounted();
                            " x-ref="element" class="text-primary-600 !leading-none font-sans font-semibold text-3xl md:text-4xl xl:text-5.5xl mb-4">
                            <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">A</div>
                                </div>
                                <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                    little
                                </div>
                                <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                    about
                                </div>
                                <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                    us
                                </div>
                            </div>
                        </h2>
                        <div x-ref="stats">
                            <div class="relative">
                                <div class="py-2 flex justify-start items-center gap-3">
                                    <div class="relative w-auto">
                                        <div class="flex">
                                            <span x-text="26 > 0 ? formatNumber(26) : 26" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">26</span>
                                        </div>
                                        <div class="absolute inset-0 flex">
                                            <div class="relative overflow-hidden -mr-8 pr-8">
                                                <div x-data="animate()" x-init="
                                                    opacity.active = false;
                                                    yPercent = {
                                                    ...yPercent, active: true,
                                                    start: 102,
                                                    duration: 0.3,
                                                    };
                                                    setTrigger($refs.stats);
                                                    delay = delay + 0 + 0;
                                                    mounted();
                                                    " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                    <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                        if (avalanche.inView($refs.stats)) {
                                                        delay = avalanche.delay.enter;
                                                        } else {
                                                        delay = avalanche.delay.default;
                                                        }
                                                        scrollSettings = {
                                                        start: `top 85%`,
                                                        end: `bottom top`,
                                                        toggleActions: `play none play none`,
                                                        trigger: $refs.stats,
                                                        }
                                                        gsap.from($refs.number, {
                                                        textContent: 0,
                                                        duration: 0.8,
                                                        ease: `circ.out`,
                                                        snap: { textContent: 1 },
                                                        delay: delay + 0 + 0.5,
                                                        scrollTrigger: scrollSettings,
                                                        stagger: {
                                                        each: 1.0,
                                                        onUpdate: function() {
                                                        this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                        },
                                                        }
                                                        });
                                                        " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">26</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p x-data="fadeIn()" x-init="
                                        delay = avalanche.delay.enter + 0 + 0 + 0.1;
                                        type = `chars`;
                                        stagger = 0.01;
                                        mounted();
                                        " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        Years of experience
                                    </p>
                                </div>
                                <div class="relative w-full overflow-hidden">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        xPercent = {
                                        ...xPercent, active: true,
                                        start: -101,
                                        end: 0,
                                        };
                                        setTrigger($refs.stats);
                                        delay = delay + 0 + 0.6;
                                        mounted();
                                        " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="py-2 flex justify-start items-center gap-3">
                                    <div class="relative w-auto">
                                        <div class="flex">
                                            <span x-text="28 > 0 ? formatNumber(28) : 28" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">28</span>
                                            <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                            +
                                            </span>
                                        </div>
                                        <div class="absolute inset-0 flex">
                                            <div class="relative overflow-hidden -mr-8 pr-8">
                                                <div x-data="animate()" x-init="
                                                    opacity.active = false;
                                                    yPercent = {
                                                    ...yPercent, active: true,
                                                    start: 102,
                                                    duration: 0.3,
                                                    };
                                                    setTrigger($refs.stats);
                                                    delay = delay + 0.1 + 0;
                                                    mounted();
                                                    " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                    <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                        if (avalanche.inView($refs.stats)) {
                                                        delay = avalanche.delay.enter;
                                                        } else {
                                                        delay = avalanche.delay.default;
                                                        }
                                                        scrollSettings = {
                                                        start: `top 85%`,
                                                        end: `bottom top`,
                                                        toggleActions: `play none play none`,
                                                        trigger: $refs.stats,
                                                        }
                                                        gsap.from($refs.number, {
                                                        textContent: 0,
                                                        duration: 0.8,
                                                        ease: `circ.out`,
                                                        snap: { textContent: 1 },
                                                        delay: delay + 0.1 + 0.5,
                                                        scrollTrigger: scrollSettings,
                                                        stagger: {
                                                        each: 1.0,
                                                        onUpdate: function() {
                                                        this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                        },
                                                        }
                                                        });
                                                        " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">28</span>
                                                </div>
                                            </div>
                                            <div x-data="animateText()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.1 + 0 + 1.2;
                                                mounted();
                                                " class="relative ml-2">
                                                <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"> +</div>
                                                    </div>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <p x-data="fadeIn()" x-init="
                                        delay = avalanche.delay.enter + 0.1 + 0 + 0.1;
                                        type = `chars`;
                                        stagger = 0.01;
                                        mounted();
                                        " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        Licensed States
                                    </p>
                                </div>
                                <div class="relative w-full overflow-hidden">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        xPercent = {
                                        ...xPercent, active: true,
                                        start: -101,
                                        end: 0,
                                        };
                                        setTrigger($refs.stats);
                                        delay = delay + 0.1 + 0.6;
                                        mounted();
                                        " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="py-2 flex justify-start items-center gap-3">
                                    <div class="relative w-auto">
                                        <div class="flex">
                                            <span x-text="6 > 0 ? formatNumber(6) : 6" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">6</span>
                                        </div>
                                        <div class="absolute inset-0 flex">
                                            <div class="relative overflow-hidden -mr-8 pr-8">
                                                <div x-data="animate()" x-init="
                                                    opacity.active = false;
                                                    yPercent = {
                                                    ...yPercent, active: true,
                                                    start: 102,
                                                    duration: 0.3,
                                                    };
                                                    setTrigger($refs.stats);
                                                    delay = delay + 0.2 + 0;
                                                    mounted();
                                                    " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                    <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                        if (avalanche.inView($refs.stats)) {
                                                        delay = avalanche.delay.enter;
                                                        } else {
                                                        delay = avalanche.delay.default;
                                                        }
                                                        scrollSettings = {
                                                        start: `top 85%`,
                                                        end: `bottom top`,
                                                        toggleActions: `play none play none`,
                                                        trigger: $refs.stats,
                                                        }
                                                        gsap.from($refs.number, {
                                                        textContent: 0,
                                                        duration: 0.8,
                                                        ease: `circ.out`,
                                                        snap: { textContent: 1 },
                                                        delay: delay + 0.2 + 0.5,
                                                        scrollTrigger: scrollSettings,
                                                        stagger: {
                                                        each: 1.0,
                                                        onUpdate: function() {
                                                        this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                        },
                                                        }
                                                        });
                                                        " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">6</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p x-data="fadeIn()" x-init="
                                        delay = avalanche.delay.enter + 0.2 + 0 + 0;
                                        type = `chars`;
                                        stagger = 0.01;
                                        mounted();
                                        " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        Office Locations
                                    </p>
                                </div>
                                <div class="relative w-full overflow-hidden">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        xPercent = {
                                        ...xPercent, active: true,
                                        start: -101,
                                        end: 0,
                                        };
                                        setTrigger($refs.stats);
                                        delay = delay + 0.2 + 0.6;
                                        mounted();
                                        " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="py-2 flex justify-start items-center gap-3">
                                    <div class="relative w-auto">
                                        <div class="flex">
                                            <span x-text="0.74 > 0 ? formatNumber(0.74 , 2) : 0.74" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">.74</span>
                                        </div>
                                        <div class="absolute inset-0 flex">
                                            <div class="relative overflow-hidden -mr-8 pr-8">
                                                <div x-data="animate()" x-init="
                                                    opacity.active = false;
                                                    yPercent = {
                                                    ...yPercent, active: true,
                                                    start: 102,
                                                    duration: 0.3,
                                                    };
                                                    setTrigger($refs.stats);
                                                    delay = delay + 0.3 + 0;
                                                    mounted();
                                                    " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                    <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                        if (avalanche.inView($refs.stats)) {
                                                        delay = avalanche.delay.enter;
                                                        } else {
                                                        delay = avalanche.delay.default;
                                                        }
                                                        scrollSettings = {
                                                        start: `top 85%`,
                                                        end: `bottom top`,
                                                        toggleActions: `play none play none`,
                                                        trigger: $refs.stats,
                                                        }
                                                        gsap.from($refs.number, {
                                                        textContent: 0,
                                                        duration: 0.8,
                                                        ease: `circ.out`,
                                                        snap: { textContent: 0.01 },
                                                        delay: delay + 0.3 + 0.5,
                                                        scrollTrigger: scrollSettings,
                                                        stagger: {
                                                        each: 1.0,
                                                        onUpdate: function() {
                                                        this.targets()[0].innerHTML = formatNumber(parseFloat(this.targets()[0].textContent));
                                                        },
                                                        }
                                                        });
                                                        " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">0.74</span>
                                                </div>
                                            </div>
                                            <div x-data="animateText()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.3 + 0 + 1.2;
                                                mounted();
                                                " class="relative ml-2">
                                            </div>
                                        </div>
                                    </div>
                                    <p x-data="fadeIn()" x-init="
                                        delay = avalanche.delay.enter + 0.3 + 0 + 0.1;
                                        type = `chars`;
                                        stagger = 0.01;
                                        mounted();
                                        " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        EMR Rating
                                    </p>
                                </div>
                                <div class="relative w-full overflow-hidden">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        xPercent = {
                                        ...xPercent, active: true,
                                        start: -101,
                                        end: 0,
                                        };
                                        setTrigger($refs.stats);
                                        delay = delay + 0.3 + 0.6;
                                        mounted();
                                        " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="py-2 flex justify-start items-center gap-3">
                                    <div class="relative w-auto">
                                        <div class="flex">
                                            <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                            $
                                            </span>
                                            <span x-text="700 > 0 ? formatNumber(700) : 700" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">700</span>
                                            <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                            M
                                            </span>
                                        </div>
                                        <div class="absolute inset-0 flex">
                                            <div x-data="animateText()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                type = 'chars';
                                                wordsClass = avalanche.textClass.words.h1;
                                                stagger = 0.03;
                                                setTrigger($refs.stats);
                                                delay = delay + 0.4;
                                                mounted();
                                                ">
                                                <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                        <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">$</div>
                                                        </div>
                                                    </div>
                                                </span>
                                            </div>
                                            <div class="relative overflow-hidden -mr-8 pr-8">
                                                <div x-data="animate()" x-init="
                                                    opacity.active = false;
                                                    yPercent = {
                                                    ...yPercent, active: true,
                                                    start: 102,
                                                    duration: 0.3,
                                                    };
                                                    setTrigger($refs.stats);
                                                    delay = delay + 0.4 + 0.1;
                                                    mounted();
                                                    " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                    <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                        if (avalanche.inView($refs.stats)) {
                                                        delay = avalanche.delay.enter;
                                                        } else {
                                                        delay = avalanche.delay.default;
                                                        }
                                                        scrollSettings = {
                                                        start: `top 85%`,
                                                        end: `bottom top`,
                                                        toggleActions: `play none play none`,
                                                        trigger: $refs.stats,
                                                        }
                                                        gsap.from($refs.number, {
                                                        textContent: 0,
                                                        duration: 0.8,
                                                        ease: `circ.out`,
                                                        snap: { textContent: 1 },
                                                        delay: delay + 0.4 + 0.5,
                                                        scrollTrigger: scrollSettings,
                                                        stagger: {
                                                        each: 1.0,
                                                        onUpdate: function() {
                                                        this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                        },
                                                        }
                                                        });
                                                        " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">700</span>
                                                </div>
                                            </div>
                                            <div x-data="animateText()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.4 + 0.1 + 1.2;
                                                mounted();
                                                " class="relative ml-2">
                                                <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">M</div>
                                                    </div>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <p x-data="fadeIn()" x-init="
                                        delay = avalanche.delay.enter + 0.4 + 0.1 + 0.1;
                                        type = `chars`;
                                        stagger = 0.01;
                                        mounted();
                                        " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        Bonding Capacity
                                    </p>
                                </div>
                                <div class="relative w-full overflow-hidden">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        xPercent = {
                                        ...xPercent, active: true,
                                        start: -101,
                                        end: 0,
                                        };
                                        setTrigger($refs.stats);
                                        delay = delay + 0.4 + 0.6;
                                        mounted();
                                        " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                                </div>
                            </div>
                            <div class="relative">
                                <div class="py-2 flex justify-start items-center gap-3">
                                    <div class="relative w-auto">
                                        <div class="flex">
                                            <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                            #
                                            </span>
                                            <span x-text="22 > 0 ? formatNumber(22) : 22" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">22</span>
                                        </div>
                                        <div class="absolute inset-0 flex">
                                            <div x-data="animateText()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                type = 'chars';
                                                wordsClass = avalanche.textClass.words.h1;
                                                stagger = 0.03;
                                                setTrigger($refs.stats);
                                                delay = delay + 0.5;
                                                mounted();
                                                ">
                                                <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                        <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">#</div>
                                                        </div>
                                                    </div>
                                                </span>
                                            </div>
                                            <div class="relative overflow-hidden -mr-8 pr-8">
                                                <div x-data="animate()" x-init="
                                                    opacity.active = false;
                                                    yPercent = {
                                                    ...yPercent, active: true,
                                                    start: 102,
                                                    duration: 0.3,
                                                    };
                                                    setTrigger($refs.stats);
                                                    delay = delay + 0.5 + 0.1;
                                                    mounted();
                                                    " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                    <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                        if (avalanche.inView($refs.stats)) {
                                                        delay = avalanche.delay.enter;
                                                        } else {
                                                        delay = avalanche.delay.default;
                                                        }
                                                        scrollSettings = {
                                                        start: `top 85%`,
                                                        end: `bottom top`,
                                                        toggleActions: `play none play none`,
                                                        trigger: $refs.stats,
                                                        }
                                                        gsap.from($refs.number, {
                                                        textContent: 0,
                                                        duration: 0.8,
                                                        ease: `circ.out`,
                                                        snap: { textContent: 1 },
                                                        delay: delay + 0.5 + 0.5,
                                                        scrollTrigger: scrollSettings,
                                                        stagger: {
                                                        each: 1.0,
                                                        onUpdate: function() {
                                                        this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                        },
                                                        }
                                                        });
                                                        " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">22</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p x-data="fadeIn()" x-init="
                                        delay = avalanche.delay.enter + 0.5 + 0.1 + 0;
                                        type = `chars`;
                                        stagger = 0.01;
                                        mounted();
                                        " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                        top contractor nationally, according to Engineering News-Record
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-data="" x-ref="container" class="relative flex lg:hidden flex-col justify-between min-h-[calc(100vh-62px)] bg-primary-400">
                <div class="fluid-container relative -mb-10 pt-12 z-10">
                    <h2 x-data="animateText()" x-init="
                        opacity.active = false;
                        yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                        };
                        type = `chars`;
                        wordsClass = avalanche.textClass.words.h1;
                        stagger = 0.03;
                        setTrigger($refs.container);
                        mounted();
                        " x-ref="element" class="text-white font-sans leading-none font-semibold text-5xl md:text-6xl text-center mb-4 text-balance">
                        <div class="overflow-hidden" style="display: block; text-align: center; position: relative;">
                            <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">A</div>
                            </div>
                            <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">l</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">i</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">l</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">e</div>
                            </div>
                            <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">a</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">b</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">o</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">u</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">t</div>
                            </div>
                            <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">u</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">s</div>
                            </div>
                        </div>
                    </h2>
                    <div class="relative mt-12 w-full overflow-hidden">
                        <div x-data="animate()" x-init="
                            opacity.active = false;
                            xPercent = {
                            ...xPercent, active: true,
                            start: -101,
                            end: 0,
                            };
                            setTrigger($refs.stats);
                            delay = delay + 0 + 0.6;
                            mounted();
                            " x-ref="element" class="w-full h-px bg-white/20" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                    </div>
                    <div x-ref="stats">
                        <div class="relative">
                            <div class="py-2 flex justify-start items-center gap-3">
                                <div class="relative w-auto">
                                    <div class="flex">
                                        <span x-text="4000 > 0 ? formatNumber(4000) : 4000" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">4,000</span>
                                        <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                        +
                                        </span>
                                    </div>
                                    <div class="absolute inset-0 flex">
                                        <div class="relative overflow-hidden -mr-8 pr-8">
                                            <div x-data="animate()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0 + 0;
                                                mounted();
                                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                    if (avalanche.inView($refs.stats)) {
                                                    delay = avalanche.delay.enter;
                                                    } else {
                                                    delay = avalanche.delay.default;
                                                    }
                                                    scrollSettings = {
                                                    start: `top 85%`,
                                                    end: `bottom top`,
                                                    toggleActions: `play none play none`,
                                                    trigger: $refs.stats,
                                                    }
                                                    gsap.from($refs.number, {
                                                    textContent: 0,
                                                    duration: 0.8,
                                                    ease: `circ.out`,
                                                    snap: { textContent: 1 },
                                                    delay: delay + 0 + 0.5,
                                                    scrollTrigger: scrollSettings,
                                                    stagger: {
                                                    each: 1.0,
                                                    onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                    },
                                                    }
                                                    });
                                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">4,000</span>
                                            </div>
                                        </div>
                                        <div x-data="animateText()" x-init="
                                            opacity.active = false;
                                            yPercent = {
                                            ...yPercent, active: true,
                                            start: 102,
                                            duration: 0.3,
                                            };
                                            setTrigger($refs.stats);
                                            delay = delay + 0 + 0 + 1.2;
                                            mounted();
                                            " class="relative ml-2">
                                            <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> +</div>
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <p x-data="fadeIn()" x-init="
                                    delay = avalanche.delay.enter + 0 + 0 + 0.1;
                                    type = `chars`;
                                    stagger = 0.01;
                                    mounted();
                                    " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                    Years of experience
                                </p>
                            </div>
                            <div class="relative w-full overflow-hidden">
                                <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    xPercent = {
                                    ...xPercent, active: true,
                                    start: -101,
                                    end: 0,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0 + 0.6;
                                    mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="py-2 flex justify-start items-center gap-3">
                                <div class="relative w-auto">
                                    <div class="flex">
                                        <span x-text="220 > 0 ? formatNumber(220) : 220" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">220</span>
                                        <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                        +
                                        </span>
                                    </div>
                                    <div class="absolute inset-0 flex">
                                        <div class="relative overflow-hidden -mr-8 pr-8">
                                            <div x-data="animate()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.1 + 0;
                                                mounted();
                                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                    if (avalanche.inView($refs.stats)) {
                                                    delay = avalanche.delay.enter;
                                                    } else {
                                                    delay = avalanche.delay.default;
                                                    }
                                                    scrollSettings = {
                                                    start: `top 85%`,
                                                    end: `bottom top`,
                                                    toggleActions: `play none play none`,
                                                    trigger: $refs.stats,
                                                    }
                                                    gsap.from($refs.number, {
                                                    textContent: 0,
                                                    duration: 0.8,
                                                    ease: `circ.out`,
                                                    snap: { textContent: 1 },
                                                    delay: delay + 0.1 + 0.5,
                                                    scrollTrigger: scrollSettings,
                                                    stagger: {
                                                    each: 1.0,
                                                    onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                    },
                                                    }
                                                    });
                                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">220</span>
                                            </div>
                                        </div>
                                        <div x-data="animateText()" x-init="
                                            opacity.active = false;
                                            yPercent = {
                                            ...yPercent, active: true,
                                            start: 102,
                                            duration: 0.3,
                                            };
                                            setTrigger($refs.stats);
                                            delay = delay + 0.1 + 0 + 1.2;
                                            mounted();
                                            " class="relative ml-2">
                                            <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> +</div>
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <p x-data="fadeIn()" x-init="
                                    delay = avalanche.delay.enter + 0.1 + 0 + 0.1;
                                    type = `chars`;
                                    stagger = 0.01;
                                    mounted();
                                    " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                    active projects
                                </p>
                            </div>
                            <div class="relative w-full overflow-hidden">
                                <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    xPercent = {
                                    ...xPercent, active: true,
                                    start: -101,
                                    end: 0,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.1 + 0.6;
                                    mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="py-2 flex justify-start items-center gap-3">
                                <div class="relative w-auto">
                                    <div class="flex">
                                        <span x-text="17 > 0 ? formatNumber(17) : 17" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">17</span>
                                    </div>
                                    <div class="absolute inset-0 flex">
                                        <div class="relative overflow-hidden -mr-8 pr-8">
                                            <div x-data="animate()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.2 + 0;
                                                mounted();
                                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                    if (avalanche.inView($refs.stats)) {
                                                    delay = avalanche.delay.enter;
                                                    } else {
                                                    delay = avalanche.delay.default;
                                                    }
                                                    scrollSettings = {
                                                    start: `top 85%`,
                                                    end: `bottom top`,
                                                    toggleActions: `play none play none`,
                                                    trigger: $refs.stats,
                                                    }
                                                    gsap.from($refs.number, {
                                                    textContent: 0,
                                                    duration: 0.8,
                                                    ease: `circ.out`,
                                                    snap: { textContent: 1 },
                                                    delay: delay + 0.2 + 0.5,
                                                    scrollTrigger: scrollSettings,
                                                    stagger: {
                                                    each: 1.0,
                                                    onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                    },
                                                    }
                                                    });
                                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">17</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p x-data="fadeIn()" x-init="
                                    delay = avalanche.delay.enter + 0.2 + 0 + 0;
                                    type = `chars`;
                                    stagger = 0.01;
                                    mounted();
                                    " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                    states with active projects
                                </p>
                            </div>
                            <div class="relative w-full overflow-hidden">
                                <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    xPercent = {
                                    ...xPercent, active: true,
                                    start: -101,
                                    end: 0,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.2 + 0.6;
                                    mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="py-2 flex justify-start items-center gap-3">
                                <div class="relative w-auto">
                                    <div class="flex">
                                        <span x-text="60 > 0 ? formatNumber(60) : 60" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">60</span>
                                        <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                        +
                                        </span>
                                    </div>
                                    <div class="absolute inset-0 flex">
                                        <div class="relative overflow-hidden -mr-8 pr-8">
                                            <div x-data="animate()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.3 + 0;
                                                mounted();
                                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                    if (avalanche.inView($refs.stats)) {
                                                    delay = avalanche.delay.enter;
                                                    } else {
                                                    delay = avalanche.delay.default;
                                                    }
                                                    scrollSettings = {
                                                    start: `top 85%`,
                                                    end: `bottom top`,
                                                    toggleActions: `play none play none`,
                                                    trigger: $refs.stats,
                                                    }
                                                    gsap.from($refs.number, {
                                                    textContent: 0,
                                                    duration: 0.8,
                                                    ease: `circ.out`,
                                                    snap: { textContent: 1 },
                                                    delay: delay + 0.3 + 0.5,
                                                    scrollTrigger: scrollSettings,
                                                    stagger: {
                                                    each: 1.0,
                                                    onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                    },
                                                    }
                                                    });
                                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">60</span>
                                            </div>
                                        </div>
                                        <div x-data="animateText()" x-init="
                                            opacity.active = false;
                                            yPercent = {
                                            ...yPercent, active: true,
                                            start: 102,
                                            duration: 0.3,
                                            };
                                            setTrigger($refs.stats);
                                            delay = delay + 0.3 + 0 + 1.2;
                                            mounted();
                                            " class="relative ml-2">
                                            <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> +</div>
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <p x-data="fadeIn()" x-init="
                                    delay = avalanche.delay.enter + 0.3 + 0 + 0.1;
                                    type = `chars`;
                                    stagger = 0.01;
                                    mounted();
                                    " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                    years in business
                                </p>
                            </div>
                            <div class="relative w-full overflow-hidden">
                                <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    xPercent = {
                                    ...xPercent, active: true,
                                    start: -101,
                                    end: 0,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.3 + 0.6;
                                    mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="py-2 flex justify-start items-center gap-3">
                                <div class="relative w-auto">
                                    <div class="flex">
                                        <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                        $
                                        </span>
                                        <span x-text="6 > 0 ? formatNumber(6) : 6" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">6</span>
                                        <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                        .4
                                        </span>
                                    </div>
                                    <div class="absolute inset-0 flex">
                                        <div x-data="animateText()" x-init="
                                            opacity.active = false;
                                            yPercent = {
                                            ...yPercent, active: true,
                                            start: 102,
                                            duration: 0.3,
                                            };
                                            type = 'chars';
                                            wordsClass = avalanche.textClass.words.h1;
                                            stagger = 0.03;
                                            setTrigger($refs.stats);
                                            delay = delay + 0.4;
                                            mounted();
                                            ">
                                            <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                    <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">$</div>
                                                    </div>
                                                </div>
                                            </span>
                                        </div>
                                        <div class="relative overflow-hidden -mr-8 pr-8">
                                            <div x-data="animate()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.4 + 0.1;
                                                mounted();
                                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                    if (avalanche.inView($refs.stats)) {
                                                    delay = avalanche.delay.enter;
                                                    } else {
                                                    delay = avalanche.delay.default;
                                                    }
                                                    scrollSettings = {
                                                    start: `top 85%`,
                                                    end: `bottom top`,
                                                    toggleActions: `play none play none`,
                                                    trigger: $refs.stats,
                                                    }
                                                    gsap.from($refs.number, {
                                                    textContent: 0,
                                                    duration: 0.8,
                                                    ease: `circ.out`,
                                                    snap: { textContent: 1 },
                                                    delay: delay + 0.4 + 0.5,
                                                    scrollTrigger: scrollSettings,
                                                    stagger: {
                                                    each: 1.0,
                                                    onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                    },
                                                    }
                                                    });
                                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">6</span>
                                            </div>
                                        </div>
                                        <div x-data="animateText()" x-init="
                                            opacity.active = false;
                                            yPercent = {
                                            ...yPercent, active: true,
                                            start: 102,
                                            duration: 0.3,
                                            };
                                            setTrigger($refs.stats);
                                            delay = delay + 0.4 + 0.1 + 1.2;
                                            mounted();
                                            " class="relative ml-2">
                                            <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                    <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 102%);"> .4</div>
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <p x-data="fadeIn()" x-init="
                                    delay = avalanche.delay.enter + 0.4 + 0.1 + 0.1;
                                    type = `chars`;
                                    stagger = 0.01;
                                    mounted();
                                    " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                    billion in annual revenue
                                </p>
                            </div>
                            <div class="relative w-full overflow-hidden">
                                <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    xPercent = {
                                    ...xPercent, active: true,
                                    start: -101,
                                    end: 0,
                                    };
                                    setTrigger($refs.stats);
                                    delay = delay + 0.4 + 0.6;
                                    mounted();
                                    " x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="py-2 flex justify-start items-center gap-3">
                                <div class="relative w-auto">
                                    <div class="flex">
                                        <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                        #
                                        </span>
                                        <span x-text="22 > 0 ? formatNumber(22) : 22" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">22</span>
                                    </div>
                                    <div class="absolute inset-0 flex">
                                        <div x-data="animateText()" x-init="
                                            opacity.active = false;
                                            yPercent = {
                                            ...yPercent, active: true,
                                            start: 102,
                                            duration: 0.3,
                                            };
                                            type = 'chars';
                                            wordsClass = avalanche.textClass.words.h1;
                                            stagger = 0.03;
                                            setTrigger($refs.stats);
                                            delay = delay + 0.5;
                                            mounted();
                                            ">
                                            <span x-ref="element" class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">
                                                <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                                                    <div style="position:relative;display:inline-block;" class="pb-[0.9%]">
                                                        <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0%, 0.0011%) translate3d(0px, 0px, 0px);">#</div>
                                                    </div>
                                                </div>
                                            </span>
                                        </div>
                                        <div class="relative overflow-hidden -mr-8 pr-8">
                                            <div x-data="animate()" x-init="
                                                opacity.active = false;
                                                yPercent = {
                                                ...yPercent, active: true,
                                                start: 102,
                                                duration: 0.3,
                                                };
                                                setTrigger($refs.stats);
                                                delay = delay + 0.5 + 0.1;
                                                mounted();
                                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <span x-data="{ delay: 0, scrollSettings: {}, }" x-ref="number" x-init="
                                                    if (avalanche.inView($refs.stats)) {
                                                    delay = avalanche.delay.enter;
                                                    } else {
                                                    delay = avalanche.delay.default;
                                                    }
                                                    scrollSettings = {
                                                    start: `top 85%`,
                                                    end: `bottom top`,
                                                    toggleActions: `play none play none`,
                                                    trigger: $refs.stats,
                                                    }
                                                    gsap.from($refs.number, {
                                                    textContent: 0,
                                                    duration: 0.8,
                                                    ease: `circ.out`,
                                                    snap: { textContent: 1 },
                                                    delay: delay + 0.5 + 0.5,
                                                    scrollTrigger: scrollSettings,
                                                    stagger: {
                                                    each: 1.0,
                                                    onUpdate: function() {
                                                    this.targets()[0].innerHTML = formatNumber(Math.ceil(this.targets()[0].textContent));
                                                    },
                                                    }
                                                    });
                                                    " class="text-white lg:text-primary font-light mb-0 leading-tight text-5.25xl">22</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p x-data="fadeIn()" x-init="
                                    delay = avalanche.delay.enter + 0.5 + 0.1 + 0;
                                    type = `chars`;
                                    stagger = 0.01;
                                    mounted();
                                    " x-ref="element" class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]" style="opacity: 1;">
                                    top contractor nationally, according to Engineering News-Record
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-data="animate()" x-init="
                    scale = {
                    ...scale, active: true,
                    start: 1.25,
                    end: 1,
                    ease: 'circ.out',
                    };
                    element = $refs.media;
                    setTrigger($refs.media);
                    delay = delay + 0.7;
                    mounted();
                    " x-ref="media" class="flex-grow w-full min-h-52 relative bg-primary-400" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">
                    <div x-data="animate()" x-init="
                        opacity.active = false;
                        scale = {
                        ...scale, active: true,
                        start: 1.24,
                        end: 1.24,
                        ease: 'none',
                        };
                        yPercent = {
                        ...yPercent, active: true,
                        start: -12,
                        end: 12,
                        ease: 'none',
                        };
                        setTrigger($refs.container);
                        scrollTrigger = true;
                        trigger = $refs.container;
                        scrub = 0.4;
                        start = 'top bottom';
                        mounted();
                        " class="absolute inset-0 !top-1 overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 12%) scale(1.24, 1.24);">
                            <picture>
                                <img src="./images/Cooper-Green_-Crane-Placement-41_medium_rt2-768x575.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                            </picture>
                        </div>
                    </div>
                    <div class="stats-mobile-gradient absolute w-full h-full inset-0"></div>
                </div>
            </div>
        </section>
        <section x-data="" class="-mt-20 md:-mt-24 lg:-mt-24 relative flex flex-col-reverse lg:flex-row bg-white lg:bg-neutral overflow-hidden min-h-[calc(100vh-62px)] xl:min-h-[calc(100vh-70px)] tall-and-wide:min-h-screen-75">
            <div x-data="animate()" x-init="
                opacity.active = false;
                if ($store.getBreakpoint.isDesktop) {
                scale = {
                ...scale, active: true,
                start: 1.24,
                end: 1.24,
                ease: 'none',
                };
                yPercent = {
                ...yPercent, active: true,
                start: 12,
                end: -12,
                ease: 'none',
                };
                }
                setTrigger($refs.container);
                scrub = 1.2;
                start = 'top bottom';
                mounted();
                " class="lg:absolute lg:inset-y-0 lg:left-0 w-screen lg:w-[52.8vw] h-screen-50 lg:h-full overflow-hidden lg:overflow-visible bg-neutral p-8 lg:p-0" x-ref="container">
                <div x-ref="element" class="relative w-full h-full overflow-hidden origin-right creative-carousel-mask" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%) scale(1.24, 1.24);">
                    <div x-data="animate()" x-init="
                        element = [...$refs.container.querySelectorAll('[data-curtain]')];
                        opacity.active = false;
                        xPercent = {
                        ...xPercent, active: true,
                        start: -80,
                        end: 0,
                        duration: 1,
                        };
                        yPercent = {
                        ...yPercent, active: true,
                        start: -20,
                        end: 0,
                        duration: 1,
                        };
                        setTrigger($refs.container);
                        stagger = 0.2;
                        mounted();
                        " class="relative w-full h-full">
                        <div x-data="animate()" x-init="
                            element = $refs.mediaWrapper;
                            opacity.active = false;
                            scale = {
                            ...scale, active: true,
                            start: 1.25,
                            end: 1,
                            duration: 0.6,
                            ease: 'circ.out',
                            };
                            setTrigger($refs.container);
                            delay = delay + 1;
                            mounted();
                            " x-ref="mediaWrapper" class="absolute inset-0 overflow-hidden" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                            <div x-data="animate()" x-init="
                                opacity.duration = 0.2;
                                element = $refs.media;
                                setTrigger($refs.container);
                                delay = delay + 0.7;
                                mounted();
                                " x-ref="media" class="absolute inset-0 overflow-hidden" style="opacity: 1;">
                                <div class="absolute inset-0 overflow-hidden">
                                    <div x-ref="element" class="w-full h-full">
                                        <div x-data="sliders()" x-init="
                                            creativeCarouselHash = 1183201054;
                                            creativeCarouselDelay = 1000;
                                            creativeCarouselSpeed = 1200;
                                            mounted();
                                            " class="w-full h-full">
                                            <div class="swiper creative-carousel-1183201054 w-full h-full swiper-fade swiper-initialized swiper-horizontal swiper-pointer-events swiper-watch-progress swiper-backface-hidden">
                                                <div class="swiper-wrapper" style="transition-duration: 0ms;">
                                                    <div class="swiper-slide swiper-slide-duplicate swiper-slide-duplicate-active" data-swiper-slide-index="6" style="width: 614px; opacity: 0; transform: translate3d(0px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="/images/slider-images/slider-img-1.jpg 3200w" sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                                <img src="./images/Career-Home-Page-6-768x512.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 42% 50%" alt="Two construction workers talking in a warehouse building.">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide swiper-slide-duplicate-next" data-swiper-slide-index="0" style="width: 614px; opacity: 0; transform: translate3d(-614px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="/images/slider-images/slider-img-2.jpg 3200w" sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                                <img src="/images/slider-images/slider-img-2.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 50% 15%" alt="A construction worker is working on rebar at a construction site.">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide" data-swiper-slide-index="1" style="width: 614px; opacity: 0; transform: translate3d(-1228px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="/images/slider-images/slider-img-3.jpg 3200w" sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                                <img src="/images/slider-images/slider-img-3.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 20% 50%" alt="A group of construction workers standing in front of a building, taking field measurements.">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide" data-swiper-slide-index="2" style="width: 614px; opacity: 0; transform: translate3d(-1842px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="/images/slider-images/slider-img-4.jpg 3200w" sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                                <img src="./images/Career-Home-Page-7-768x493.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 0% 50%" alt="A group of construction workers wearing safety vests while the worker in the foreground adjusts his helmet.">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide" data-swiper-slide-index="3" style="width: 614px; opacity: 0; transform: translate3d(-2456px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="/images/slider-images/slider-img-6.jpg 3200w" sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                                <img src="./images/Career-Home-Page-3-768x512.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 20% 70%" alt="A construction worker holding a digital tablet in front of a construction site.">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide" data-swiper-slide-index="4" style="width: 614px; opacity: 0; transform: translate3d(-3070px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="/images/slider-images/slider-img-7.jpg 3200w" sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                                                <img src="./images/Career_construction-operations-768x537.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 0% 30%" alt="Three construction workers wearing hard hats and safety vests, one in hte foreground points out of frame">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide swiper-slide-duplicate swiper-slide-next" data-swiper-slide-index="0" style="width: 614px; opacity: 0; transform: translate3d(-4912px, 0px, 0px); transition-duration: 0ms;">
                                                        <div class="absolute inset-0 w-full h-full">
                                                            <picture>
                                                                <source srcset="
                                                                    /images/slider-images/slider-img-1.jpg 3200w
                                                                    " sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/jpeg">
                                                                <img src="/images/slider-images/slider-img-1.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 50% 15%" alt="A construction worker is working on rebar at a construction site.">
                                                            </picture>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div x-data="animate()" x-init="
                            opacity.start = 1;
                            opacity.end = 0;
                            setTrigger($refs.container);
                            delay = delay + 1;
                            mounted();
                            " x-ref="element" class="absolute inset-0" style="opacity: 0;">
                            <div data-curtain="" class="absolute -left-1/4 -top-1/4 h-[200%] lg:h-[150%] aspect-square bg-secondary rounded-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                            <div data-curtain="" class="absolute -left-1/4 -top-1/4 h-[200%] lg:h-[150%] aspect-square bg-primary-600 rounded-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-data="animate()" x-init="
                element = $refs.contentParallax;
                opacity.active = false;
                yPercent = {
                ...yPercent, active: true,
                start: -30,
                end: 30,
                ease: 'none',
                };
                setTrigger($refs.container);
                scrub = 0.8;
                start = 'top bottom';
                mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                mounted();
                });
                " x-ref="container" class="fluid-container flex-1 flex justify-center lg:justify-end items-center py-16">
                <div x-ref="content" class="lg:w-2/5">
                    <div x-ref="contentParallax" class="flex flex-col items-center sm:items-start gap-5 sm:gap-6" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
                        <p x-data="animateText()" x-init="
                            xPercent = {
                            ...xPercent, active: true,
                            start: -100,
                            duration: 0.4,
                            };
                            scale = {
                            ...scale, active: true,
                            start: 0.5,
                            duration: 0.4,
                            };
                            type = `chars`;
                            stagger = 0.03;
                            setTrigger($refs.content);
                            trigger = $refs.content;
                            mounted();
                            " x-ref="element" class="text-primary-600 text-center sm:text-left text-base font-medium uppercase tracking-widest"></p>
                        <div class="overflow-hidden" style="display: block; text-align: left; position: relative;">
                            <div style="position:relative;display:inline-block;">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);" class="text-primary-600"><b>Join Our Team</b></div>
                            </div>
                        </div>
                        <h2 x-data="animateText()" x-init="
                            opacity.active = false;
                            yPercent = {
                            ...yPercent, active: true,
                            start: 102,
                            duration: 0.4,
                            };
                            type = `words`;
                            wordsClass = avalanche.textClass.words.h1;
                            stagger = 0.03;
                            setTrigger($refs.content);
                            trigger = $refs.content;
                            mounted();
                            " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-4.5xl sm:text-5.5xl sm:mt-2 text-center sm:text-left">
                            <div class="overflow-hidden" style="display: block; text-align: left; position: relative;">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">Advance</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">your</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">Career</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">with</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">us</div>
                            </div>
                        </h2>
                        <p x-data="fadeIn()" x-init="
                            setTrigger($refs.content);
                            trigger = $refs.content;
                            delay = delay + 0.3;
                            stagger = 0.02;
                            mounted();
                            " x-ref="element" class="text-primary-800 text-center sm:text-left text-base leading-relaxed font-normal" style="opacity: 1;">
                            Interested in joining the Eastern family? We are always looking for new talent for wide varieties of positions. We look forward to hearing from you!
                        </p>
                        <div x-data="animate()" x-init="
                            setTrigger($refs.content);
                            element = [...$refs.buttons.querySelectorAll('[data-button]')];
                            stagger = 0.2;
                            delay = delay + 0.5;
                            mounted();
                            " x-ref="buttons" class="w-full items-center justify-center md:justify-start mt-3 sm:mt-4 relative z-10 flex flex-wrap gap-6">
                            <div data-button="" style="opacity: 1;">
                                <div x-data="{
                                    hovered: false,
                                    }" class="inline-flex">
                                    <div @mouseenter="
                                        if (!ScrollTrigger.isTouch) {
                                        hovered = true;
                                        }
                                        " @mouseleave="
                                        if (!ScrollTrigger.isTouch) {
                                        hovered = false;
                                        }
                                        " class="relative inline-flex hover:z-10">
                                        <a x-data="{ hovered: false }" @mouseenter="hovered = true" @mouseleave="hovered = false" class="items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex font-medium text-primary-600 border-current hover:text-current" href="/join-our-team">
                                            Apply Now
                                            <div class="ml-4 w-8 aspect-square border group-hover:border-primary group-hover:border-opacity-100 group-hover:bg-secondary overflow-hidden border-primary/40 relative grid items-center place-content-center transition">
                                                <div class="w-4 h-4 transition text-primary" :class="hovered && 'translate-x-[100%] opacity-0'">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                                <div class="w-4 h-4 transition absolute opacity-0 text-primary -left-1/2" :class="hovered && 'translate-x-[calc(100%+0.4rem)] opacity-100'">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section x-data="" class="-mt-20 md:-mt-24 lg:-mt-24 relative -mb-px w-full min-h-screen tall-and-wide:min-h-screen-75 pb-px bg-primary-900 overflow-hidden">
            <div x-data="sliders()" x-init="
                featuredProjectsHash = '722274584';
                featuredProjectsSpeed = 3000;
                mounted();
                ScrollTrigger.create({
                trigger: $refs.container,
                start: 'top 85%',
                end: 'bottom 10%',
                onEnter: () => {
                featuredProjects.autoplay.start();
                },
                onEnterBack: () => {
                featuredProjects.autoplay.start();
                },
                onLeave: () => {
                featuredProjects.autoplay.stop();
                },
                onLeaveBack: () => {
                featuredProjects.autoplay.stop();
                },
                });
                " class="flex flex-col justify-between sm:justify-end w-full h-full" x-ref="container">
                <div class="relative sm:absolute inset-x-0 sm:top-0 sm:bottom-px min-h-72 h-[50vh] sm:h-auto overflow-hidden">
                    <div x-data="animate()" x-init="
                        scale = {
                        ...scale, active: true,
                        start: 1.5,
                        end: 1,
                        duration: 0.5,
                        ease: 'circ.out',
                        };
                        setTrigger($refs.container);
                        opacity.duration = 0.6;
                        mounted();
                        " x-ref="element" class="absolute inset-0" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">
                        <div x-data="animate()" x-init="
                            opacity.active = false;
                            scale = {
                            ...scale, active: true,
                            start: 1.24,
                            end: 1.24,
                            ease: 'none',
                            };
                            yPercent = {
                            ...yPercent, active: true,
                            start: -12,
                            end: 12,
                            ease: 'none',
                            };
                            trigger = $refs.container;
                            scrub = 0.4;
                            start = 'top bottom';
                            mounted();
                            " class="absolute inset-0 overflow-hidden">
                            <div class="w-full h-full" x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, 12%) scale(1.24, 1.24);">
                                <div class="swiper featured-projects-slideshow-722274584 w-full h-full swiper-fade swiper-initialized swiper-horizontal swiper-pointer-events swiper-watch-progress swiper-backface-hidden">
                                    <div class="swiper-wrapper w-full h-full" style="transition-duration: 0ms;">
                                        <div class="swiper-slide w-full h-full swiper-slide-duplicate swiper-slide-prev" data-swiper-slide-index="0" style="width: 1149px; opacity: 0; transform: translate3d(0px, 0px, 0px); transition-duration: 0ms;">
                                            <div class="absolute inset-0 w-full h-full">
                                                <img src="/images/front-page-slider/2.jpg" class="absolute inset-0 w-full h-full object-cover" alt="default">
                                            </div>
                                        </div>
                                        <div class="swiper-slide w-full h-full swiper-slide-visible swiper-slide-active" data-swiper-slide-index="1" style="width: 1149px; opacity: 0; transform: translate3d(-1149px, 0px, 0px); transition-duration: 0ms;">
                                            <div class="absolute inset-0 w-full h-full">
                                                <img src="/images/front-page-slider/1.jpg" class="absolute inset-0 w-full h-full object-cover" aria-hidden="true">
                                            </div>
                                        </div>
                                        <div class="swiper-slide w-full h-full swiper-slide-next" data-swiper-slide-index="2" style="width: 1149px; opacity: 0; transform: translate3d(-2298px, 0px, 0px); transition-duration: 0ms;">
                                            <div class="absolute inset-0 w-full h-full">
                                                <img src="/images/front-page-slider/3.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                            </div>
                                        </div>
                                        <div class="swiper-slide w-full h-full" data-swiper-slide-index="3" style="width: 1149px; opacity: 0; transform: translate3d(-3447px, 0px, 0px); transition-duration: 0ms;">
                                            <div class="absolute inset-0 w-full h-full">
                                                <img src="/images/front-page-slider/4.jpg" class="absolute inset-0 w-full h-full object-cover" alt="">
                                                <div class="absolute w-full h-full z-10 inset-0 bg-black opacity-40"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="absolute inset-x-0 bottom-0 z-10 hidden sm:block w-full h-[45vh] bg-gradient-to-t from-primary-900 to-transparent"></div>
                <div class="relative sm:absolute sm:bottom-0 z-20 flex flex-col justify-end w-full sm:h-full pb-14 sm:pb-24 pt-0 sm:pt-0">
                    <div class="absolute inset-x-0 top-[-200%] sm:hidden w-full h-[200%] bg-gradient-to-t from-primary-900 to-transparent"></div>
                    <div x-ref="content" class="flex justify-between fluid-container h-full w-full sm:h-auto pb-5 sm:pb-0">
                        <div class="relative w-full">
                            <div x-show="featuredProjectsActiveIndex == 0" x-transition.duration.800ms="" class="absolute inset-x-0 bottom-0 z-20 w-full sm:max-w-md 2xl:max-w-lg flex flex-col items-center sm:items-start gap-6">
                                <p class="text-white font-medium text-center sm:text-left text-2xl sm:text-3.5xl leading-tight">
                                    We help you build with confidence, using expert craftsmanship and real accountability.
                                </p>
                            </div>
                            <div x-show="featuredProjectsActiveIndex == 1" x-transition.duration.800ms="" class="absolute inset-x-0 bottom-0 z-20 w-full sm:max-w-md 2xl:max-w-lg flex flex-col items-center sm:items-start gap-6" style="display: none;">
                                <p class="text-white font-medium text-center sm:text-left text-2xl sm:text-3.5xl leading-tight">
                                    From foundation to finish, we bring structure, speed, and solid results to every project.
                                </p>
                            </div>
                            <div x-show="featuredProjectsActiveIndex == 2" x-transition.duration.800ms="" class="absolute inset-x-0 bottom-0 z-20 w-full sm:max-w-md 2xl:max-w-lg flex flex-col items-center sm:items-start gap-6" style="display: none;">
                                <p class="text-white font-medium text-center sm:text-left text-2xl sm:text-3.5xl leading-tight">
                                    Eastern delivers dependable construction solutions tailored to meet your goals.
                                </p>
                            </div>
                            <div x-show="featuredProjectsActiveIndex == 3" x-transition.duration.800ms="" class="absolute inset-x-0 bottom-0 z-20 w-full sm:max-w-md 2xl:max-w-lg flex flex-col items-center sm:items-start gap-6" style="display: none;">
                                <p class="text-white font-medium text-center sm:text-left text-2xl sm:text-3.5xl leading-tight">
                                    Your vision deserves a team that shows up, follows through, and builds it right the first time.
                                </p>
                            </div>
                            <div class="absolute z-20 right-0 bottom-0 hidden sm:block">
                            </div>
                        </div>
                    </div>
                    <div x-data="revealer()" x-init="
                        xPercent = {
                        start: 101,
                        end: -101,
                        };
                        setTrigger($refs.content);
                        start = 'top bottom';
                        mounted();
                        " class="fluid-container my-6 sm:mt-16 sm:mb-0 relative z-20">
                        <div class="relative w-full h-0.5 overflow-hidden">
                            <div x-ref="element" class="absolute inset-0" style="opacity: 1;">
                                <div class="swiper-pagination-featured-projects-722274584 swiper-pagination-progressbar swiper-pagination-horizontal"><span class="swiper-pagination-progressbar-fill" style="transform: translate3d(0px, 0px, 0px) scaleX(0.25) scaleY(1); transition-duration: 3000ms;"></span></div>
                            </div>
                            <div x-ref="curtain" class="absolute z-10 inset-0 w-full h-full bg-primary-600" style="translate: none; rotate: none; scale: none; transform: translate(-101%, 0%);">
                            </div>
                        </div>
                    </div>
                    <div class="absolute z-10 left-0 top-0 hidden sm:flex justify-end items-center w-[224px] h-full group" x-data="{
                        displayNext: false,
                        }" @mouseenter="displayNext = true" @mouseleave="displayNext = false">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-900/80 to-transparent transition -translate-x-over" :class="displayNext ? 'sm:translate-x-0' : '-translate-x-over'"></div>
                        <div class="transition ease-in-out-circ opacity-0" :class="displayNext ? 'opacity-100 -translate-x-[calc(112px-50%)]' : 'opacity-0'">
                            <div class="relative inline-flex hover:z-10">
                                <button x-data="{
                                    hovered: false,
                                    }" @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " x-ref="box" class="featured-projects-prev-722274584 relative w-14 h-14 focus:outline-none group" aria-label="Previous">
                                    <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                                    <div class="absolute inset-0 border-2 transition-all border-white/20" :class="{
                                        'border-white/20' : !hovered,
                                        'border-4 border-secondary scale-90' : hovered,
                                        }"></div>
                                    <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                                        'opacity-0 scale-50' : !hovered,
                                        'opacity-40 scale-100' : hovered,
                                        }"></div>
                                    <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                                        <div class="-scale-x-100">
                                            <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                                <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-secondary relative w-full">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                                <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-white absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="absolute z-10 right-0 top-0 hidden sm:flex items-center w-[224px] h-full group" x-data="{
                        displayNext: true,
                        }">
                        <div class="absolute inset-0 bg-gradient-to-l from-primary-900/80 to-transparent transition sm:translate-x-0" :class="displayNext ? 'sm:translate-x-0' : 'translate-x-over'"></div>
                        <div class="transition ease-in-out-circ opacity-100 translate-x-[calc(112px-50%)]" :class="displayNext ? 'opacity-100 translate-x-[calc(112px-50%)]' : 'opacity-0'">
                            <div class="relative inline-flex hover:z-10">
                                <button x-data="{
                                    hovered: false,
                                    }" @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " x-ref="box" class="featured-projects-next-722274584 relative w-14 h-14 focus:outline-none group" aria-label="Next">
                                    <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                                    <div class="absolute inset-0 border-2 transition-all border-white/20" :class="{
                                        'border-white/20' : !hovered,
                                        'border-4 border-secondary scale-90' : hovered,
                                        }"></div>
                                    <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                                        'opacity-0 scale-50' : !hovered,
                                        'opacity-40 scale-100' : hovered,
                                        }"></div>
                                    <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                                        <div>
                                            <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                                <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-secondary relative w-full">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                                <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-white absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="fluid-container block sm:hidden">
                        <div class="relative flex justify-between overflow-hidden">
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                xPercent = {
                                ...xPercent, active: true,
                                start: -101,
                                end: 0,
                                duration: 0.3,
                                };
                                setTrigger($refs.content);
                                delay = delay + 0.5;
                                start = 'top bottom';
                                mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                <div x-data="magnetic()" class="relative inline-flex hover:z-10">
                                    <button x-data="{
                                    hovered: false,
                                    }" @mousemove="
                                    magnetize($event, $el, $refs.arrow, -12);
                                    magnetize($event, $el, $refs.box);
                                    " @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    demagnetize($refs.arrow);
                                    demagnetize($refs.box);
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " x-ref="box" class="featured-projects-prev-722274584 relative w-14 h-14 focus:outline-none group" aria-label="Previous">
                                    <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                                    <div class="absolute inset-0 border-2 transition-all border-white/20" :class="{
                                        'border-white/20' : !hovered,
                                        'border-4 border-secondary scale-90' : hovered,
                                        }"></div>
                                    <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                                        'opacity-0 scale-50' : !hovered,
                                        'opacity-40 scale-100' : hovered,
                                        }"></div>
                                    <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                                        <div class="-scale-x-100">
                                            <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                                <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-secondary relative w-full">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                                <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-white absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </button>
                                </div>
                            </div>
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                xPercent = {
                                ...xPercent, active: true,
                                start: 101,
                                end: 0,
                                duration: 0.3,
                                };
                                setTrigger($refs.content);
                                delay = delay + 0.5;
                                start = 'top bottom';
                                mounted();
                                " x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                <div x-data="magnetic()" class="relative inline-flex hover:z-10">
                                    <button x-data="{
                                    hovered: false,
                                    }" @mousemove="
                                    magnetize($event, $el, $refs.arrow, -12);
                                    magnetize($event, $el, $refs.box);
                                    " @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    demagnetize($refs.arrow);
                                    demagnetize($refs.box);
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " x-ref="box" class="featured-projects-next-722274584 relative w-14 h-14 focus:outline-none group" aria-label="Next">
                                    <div class="absolute inset-0 border-2 border-primary scale-110 opacity-0 group-focus-visible:opacity-80 transition-all"></div>
                                    <div class="absolute inset-0 border-2 transition-all border-white/20" :class="{
                                        'border-white/20' : !hovered,
                                        'border-4 border-secondary scale-90' : hovered,
                                        }"></div>
                                    <div class="absolute inset-0 border border-secondary transition opacity-0 scale-50" :class="{
                                        'opacity-0 scale-50' : !hovered,
                                        'opacity-40 scale-100' : hovered,
                                        }"></div>
                                    <div x-ref="arrow" class="relative w-full h-full flex justify-center items-center">
                                        <div>
                                            <div class="relative flex justify-center items-center w-4 min-w-0 shrink-0 aspect-square overflow-hidden transition scale-100" :class="hovered ? 'scale-125' : 'scale-100'">
                                                <div x-show="!hovered" x-transition:enter="transition ease-in-out-circ delay-150 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-secondary relative w-full">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                                <div x-show="hovered" x-transition:enter="transition ease-in-out-circ delay-75 duration-300" x-transition:enter-start="-translate-x-over" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out-circ duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-over" class="text-white absolute inset-0 flex justify-center items-center w-full h-full" style="display: none;">
                                                    <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section x-data="" class="-mt-20 md:-mt-24 lg:-mt-24 bg-primary-900 min-h-[calc(100vh-62px)] xl:min-h-[calc(100vh-70px)] tall-and-wide:min-h-screen-75 relative -mb-px pb-px overflow-hidden">
            <div class="absolute -bottom-px inset-x-0 h-[90%] bg-neutral"></div>
            <div x-ref="container" class="relative w-full h-full">
                <div class="absolute inset-0 bg-primary-900">
                    <div class="absolute inset-x-0 top-px bottom-0 z-0">
                        <div x-data="animate()" x-init="
                            scale = {
                            ...scale, active: true,
                            start: 1.5,
                            end: 1,
                            duration: 0.5,
                            ease: 'circ.out',
                            };
                            setTrigger($refs.container);
                            opacity.duration = 0.6;
                            mounted();
                            " x-ref="element" class="absolute inset-0" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">
                            <div x-data="animate()" x-init="
                                opacity.active = false;
                                scale = {
                                ...scale, active: true,
                                start: 1.24,
                                end: 1.24,
                                ease: 'none',
                                };
                                yPercent = {
                                ...yPercent, active: true,
                                start: -12,
                                end: 12,
                                ease: 'none',
                                };
                                setTrigger($refs.container);
                                scrub = 1.2;
                                start = 'top bottom';
                                mounted();
                                " class="absolute inset-0 overflow-hidden">
                                <div x-ref="element" class="absolute inset-0 w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0%, 11.9739%) translate3d(0px, 0px, 0px) scale(1.24, 1.24);">
                                    <picture>
                                        <source srcset="/images/hero-images/default-hero.jpg 3200w" 
                                            sizes="(min-width: 1280px) 100vw, (min-width: 768px) 1440px, 400vw" type="image/webp">
                                        <img src="./images/home-builders-from-beginning-768x539.jpg" class="absolute inset-0 w-full h-full object-cover" aria-hidden="true">
                                    </picture>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-b from-primary-900 from-20% md:from-30% to-transparent z-10 absolute inset-0 h-3/4 w-full opacity-80"></div>
                </div>
                <div x-data="animate()" x-init="
                    opacity.active = false;
                    yPercent = {
                    ...yPercent, active: true,
                    start: 10,
                    end: -25,
                    ease: 'none',
                    };
                    setTrigger($refs.container);
                    scrub = 1.2;
                    start = 'top bottom';
                    mounted();
                    " class="absolute inset-0">
                    <div x-ref="element" style="translate: none; rotate: none; scale: none; transform: translate(0%, -24.9619%) translate3d(0px, 0px, 0px);">
                        <div x-data="animate()" x-init="
                            yPercent = {
                            ...yPercent, active: true,
                            duration: 1.6,
                            ease: 'circ.out',
                            };
                            mm.add(avalanche.breakpoint(avalanche.screens.lg, 'max'), () => {
                            yPercent.start = 30;
                            yPercent.end = 0;
                            });
                            mm.add(avalanche.breakpoint(avalanche.screens.lg), () => {
                            yPercent.start = 10;
                            yPercent.end = -20;
                            });
                            setTrigger($refs.container);
                            element = $refs.amp;
                            delay = delay + 0.4;
                            mounted();
                            " x-ref="amp" class="w-full h-full text-primary-400" style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0%, -16.2872%) translate3d(0px, 0px, 0px);">
                            <div class="opacity-40" "w-[300vw]="" md:w-[190vw]="" translate-x-[-20%]="" md:translate-x-[-5%]="" lg:translate-x-[-47%]="" "="">
                            <svg viewBox="0 0 390 327" stroke="currentColor" stroke-width="1" fill="none">
                                <path d="M389.653 98.0331L389.594 98.0473C389.594 146.38 356.89 183.079 308.805 183.079C302.183 183.079 295.375 182.809 288.812 181.05L287.945 180.818L288.567 181.465C301.203 194.601 308.045 212.068 308.045 230.795C308.045 259.382 294.256 283.297 270.92 300.074C247.58 316.855 214.685 326.494 176.498 326.494C129.279 326.494 85.2001 319.955 52.935 303.766C20.6857 287.584 0.25 261.771 0.25 223.183C0.25 195.862 14.0399 174.544 35.4861 159.129C56.9382 143.71 86.0432 134.206 116.626 130.538L116.649 130.046C96.1109 125.602 80.5387 118.067 70.1042 108.022C59.6751 97.9821 54.3658 85.4243 54.3658 70.8985C54.3658 49.8155 67.5641 32.1751 88.0936 19.7853C108.622 7.39596 136.451 0.279949 165.615 0.25C185.006 0.250021 203.873 5.06595 217.883 14.4265C231.887 23.7825 241.038 37.674 241.038 55.8558C241.038 67.6027 236.869 78.5188 228.827 86.4979C220.785 94.4756 208.852 99.5353 193.288 99.5353C182.652 99.5353 171.145 96.8169 162.306 90.9469C153.475 85.0828 147.307 76.0764 147.307 63.467C147.307 44.5555 161.398 32.7626 177.278 32.7626C182.957 32.7626 187.528 34.0294 190.674 36.4308C193.81 38.825 195.556 42.3667 195.556 46.986C195.556 52.8345 191.516 57.2647 187.378 61.7544L187.28 61.8601C183.207 66.2788 179.067 70.7701 179.067 76.6818C179.067 81.3009 180.749 85.1097 183.734 87.7612C186.716 90.41 190.977 91.8846 196.106 91.8846C207.087 91.8846 215.986 88.4357 222.139 82.2216C228.291 76.0077 231.674 67.0547 231.674 56.0955C231.674 31.03 208.113 12.9648 176.049 12.9648C158.472 12.9648 144.058 18.4961 134.038 28.383C124.018 38.2701 118.415 52.4896 118.415 69.8197C118.415 87.1381 123.126 101.799 131.52 112.135C139.918 122.476 151.99 128.473 166.665 128.473C174.207 128.473 180.801 125.931 188.373 123.012C189.784 122.468 191.228 121.911 192.719 121.355C202.226 117.81 213.685 114.269 230.404 114.269C248.399 114.269 260.185 117.814 272.406 121.49C272.843 121.621 273.281 121.753 273.719 121.885C286.428 125.7 299.85 129.521 321.337 129.521C337.387 129.521 350.785 124.296 360.174 115.361C369.562 106.425 374.923 93.795 374.923 79.0191C374.923 54.0191 357.83 35.6741 335.115 31.3082L334.928 31.7605C348.324 40.8559 354.876 52.7065 354.876 68.6211C354.876 89.7971 338.25 105.199 314.471 105.199C290.711 105.199 274.067 88.2869 274.067 65.8643C274.067 53.1091 278.991 42.7164 287.789 35.5078C296.545 28.3333 309.159 24.2958 324.625 24.2527V24.2824H324.875C343.994 24.2824 360.182 31.6854 371.593 44.6118C382.982 57.5147 389.627 75.9376 389.653 98.0331ZM299.491 231.394V231.29L299.461 231.26C299.437 210.945 292.303 194.474 279.598 183.079C266.874 171.668 248.589 165.37 226.327 165.37C205.598 165.37 188.905 170.834 177.389 180.27C165.869 189.71 159.549 203.109 159.549 218.928C159.549 232.831 164.202 244.506 172.404 253.182C180.605 261.857 192.338 267.519 206.476 269.428L206.623 268.958C198.649 264.898 192.781 259.847 188.905 253.646C185.03 247.445 183.134 240.077 183.134 231.364C183.134 208.934 201.764 192.269 226.837 192.269C239.37 192.269 249.654 196.57 256.805 203.758C263.957 210.947 267.99 221.04 267.99 232.652C267.99 245.912 262.5 256.824 253.349 264.426C244.194 272.031 231.362 276.332 216.673 276.332C192.732 276.332 174.78 268.353 162.809 254.674C150.834 240.989 144.819 221.569 144.819 198.642C144.819 173.594 154.208 151.311 171.417 136.141L172.023 135.607L171.221 135.706C147.294 138.633 127.553 148.049 113.793 162.952C100.032 177.855 92.2715 198.228 92.2715 223.034C92.2715 249.62 100.669 272.978 117.006 289.691C133.345 306.404 157.601 316.446 189.27 316.446C252.571 316.446 299.491 281.873 299.491 231.394Z" vector-effect="non-scaling-stroke" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class=" justify-center items-center flex flex-col gap-6 py-10 xl:py-16 fluid-container w-full h-full">
                <div class="max-w-3xl relative z-20 flex flex-col">
                    <div x-ref="content" class="text-center">
                        f
                        <h2 x-data="animateText()" x-init="
                            opacity.active = false;
                            yPercent = {
                            ...yPercent, active: true,
                            start: 102,
                            duration: 0.4,
                            };
                            type = `words`;
                            wordsClass = avalanche.textClass.words.h1;
                            stagger = 0.03;
                            setTrigger($refs.content);
                            mounted();
                            " x-ref="element" class="text-white font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                            <div class="overflow-hidden" style="display: block; text-align: center; position: relative;">
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">Our</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">Guiding</div>
                                <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" class="pb-[0.9%]">Principles</div>
                            </div>
                        </h2>
                        <p x-data="fadeIn()" x-init="
                            delay = avalanche.delay.default + 0.2;
                            mounted();
                            " x-ref="element" class="text-white mt-4 text-center mx-auto max-w-2xl text-lg leading-relaxed" style="opacity: 1;">
                            At Eastern Companies, our guiding principles shape everything we do. Integrity, innovation, and excellence drive our commitment to delivering quality construction solutions. We believe in collaboration, continuous improvement, and building lasting relationships. These values are the foundation of our success and the key to shaping the future of our industry.
                        </p>
                    </div>
                </div>
                <div x-data="animate()" x-init="
                    setTrigger($refs.buttons);
                    element = [...$refs.buttons.querySelectorAll('[data-button]')];
                    stagger = 0.2;
                    delay = delay + 0.1;
                    mounted();
                    " x-ref="buttons" class="w-full justify-center mt-3 sm:mt-4 relative z-10 flex flex-wrap gap-6">
                    <div data-button="" class="w-full md:w-fit" style="opacity: 1;">
                        <div x-data="{
                            hovered: false,
                            }" class="w-full md:w-fit inline-flex">
                            <div @mouseenter="
                                if (!ScrollTrigger.isTouch) {
                                hovered = true;
                                }
                                " @mouseleave="
                                if (!ScrollTrigger.isTouch) {
                                hovered = false;
                                }
                                " class="w-full md:w-fit relative inline-flex hover:z-10">
                                <a class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex text-white font-medium text-sm uppercase tracking-wide px-10 hover:px-9 py-6 bg-primary" href="/leadership">
                                    See For Yourself
                                    <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                        <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                        </svg>
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
</div>
</section>
<section x-data="" class="-mt-20 md:-mt-24 lg:-mt-24">
<div class="overflow-hidden pt-14 pb-8 xl:py-20 gray-mode-block bg-neutral" x-data="sliders()" x-init="
    carouselSliderHash = '384642784';
    isCarouselLandscape = 'false' == 'true';
    carouselSlideLength = 4;
    mounted();
    ">
<div class="fluid-container max-w-none hidden lg:block">
<div class="border-t border-black border-opacity-10"></div>
</div>
<div class="lg:flex justify-between">
<div class="lg:w-1/3 flex-shrink-0 sm:mb-9 lg:mb-0 fluid-container lg:pr-0 lg:mr-0 lg:max-w-none lg:border-r border-black border-opacity-10 relative" x-ref="intro">
<div class="lg:pr-8 lg:mt-14">
<h2 class="text-primary-600 font-sans leading-none font-semibold text-4.5xl sm:text-5.5xl">
Our Dynamic Companies
</h2>
<p class="text-primary-800 mt-4 text-base leading-relaxed font-normal">
Our story is about the outstanding results that come from hard work, bold vision, and perseverance.
</p>
<div class="hidden lg:flex justify-start gap-4 mt-14 lg:mb-8">
<button class="carousel-slider-prev-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" title="Previous">
<span class="text-primary w-4 h-4">
<svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
</svg>
</span>
</button>
<button class="carousel-slider-next-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary" title="Next">
<span class="text-primary w-4 h-4">
<svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
</svg>
</span>
</button>
</div>
</div>
</div>
<div class="sm:border-t lg:border-none border-black border-opacity-10">
    <div class="swiper carousel-slider-384642784 w-screen lg:max-w-[72.6vw] !mr-0 flex swiper-initialized swiper-horizontal swiper-pointer-events swiper-autoheight swiper-backface-hidden">
        <div class="swiper-wrapper" x-init="
            $nextTick(() => {
            let maxHeight = 0;
            Array.from($el.children).forEach((e) => {
            const height = e.getBoundingClientRect().height;
            maxHeight = height > maxHeight ? height : maxHeight;
            });
            const introHeight = $refs.intro.getBoundingClientRect().height;
            $el.style.height = introHeight > maxHeight && $store.getBreakpoint.isDesktop ? `${introHeight}px` : `${maxHeight}px`;
            });
            " style="transition-duration: 0ms; transform: translate3d(-920.727px, 0px, 0px); height: 535px;">
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-duplicate-next" data-swiper-slide-index="1" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Glass and Aluminum/EGA
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Evolving from a metal roofing company that began almost 25 years ago to being a sought-after specialist in building envelope systems, EGA is the go-to contractor for countless clients and contractor partners.</B>With vast experience gained from our portfolio of completed projects, we deliver comprehensive envelope design assistance such as waterproofing, roofing, wall panels, and glass/glazing systems.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate" data-swiper-slide-index="2" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Contractors Corporation
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Eastern Contractors Corporation focuses on industrial building throughout the Southeastern United States and wherever our clients or partners take us.</b>
                            Having installed roofing and metal panels for many industrial and commercial clients, the 2018 expansion of Hyundai in Montgomery, AL allowed Eastern to showcase its full industrial contracting capabilities. Next, we successfully executed the $200M for SK Battery project in Commerce, GA as general contractor.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-prev" data-swiper-slide-index="3" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 3 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Builders
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Seeking continued growth and diversification, it was time to expand beyond industrial clients.</b>
                            As a result, Peter’s next move was to seek opportunities in commercial construction. The commercial business unit was created and called iugis, meaning dependable, sure, and trustworthy. iugis focuses on the commercial and institutional side of the construction business such as education, healthcare, housing, hospitality, and more.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-active" data-swiper-slide-index="0" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-transparent" :class="carouselActiveIndex == 0 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Power<br>Solutions
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Recognizing and anticipating the ever-growing renewable energy market, Eastern Companies created Eastern Power Solutions to meet the demand of the renewable clean energy market.</b>
                            EPS is devoted to helping its clients “go green” by developing and maintaining successful utility-grade solar projects located in various environmental terrains, while ensuring control over safety, quality, timeliness, and productivity. Experienced in solar power generation, EPS is making significant headway in the renewable energy markets.
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-next" data-swiper-slide-index="1" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Glass and Aluminum/EGA
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Evolving from a metal roofing company that began almost 25 years ago to being a sought-after specialist in building envelope systems, EGA is the go-to contractor for countless clients and contractor partners.</B>With vast experience gained from our portfolio of completed projects, we deliver comprehensive envelope design assistance such as waterproofing, roofing, wall panels, and glass/glazing systems.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto" data-swiper-slide-index="2" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Builders Construction
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Seeking continued growth and diversification, it was time to expand beyond industrial clients.</b>
                            As a result, Peter’s next move was to seek opportunities in commercial construction. The commercial business unit was created and called iugis, meaning dependable, sure, and trustworthy. iugis focuses on the commercial and institutional side of the construction business such as education, healthcare, housing, hospitality, and more.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate-prev" data-swiper-slide-index="3" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 3 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Eastern Contractors Corporation
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Eastern Contractors Corporation focuses on industrial building throughout the Southeastern United States and wherever our clients or partners take us.</b>
                            Having installed roofing and metal panels for many industrial and commercial clients, the 2018 expansion of Hyundai in Montgomery, AL allowed Eastern to showcase its full industrial contracting capabilities. Next, we successfully executed the $200M for SK Battery project in Commerce, GA as general contractor.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-duplicate-active" data-swiper-slide-index="0" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-transparent" :class="carouselActiveIndex == 0 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Project programming services
                            </p>
                        </div>
                        <p  class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>We fast-track project success by comparing your plan against thousands of similar projects. </b>
                            This information allows us to build a budget and plan that fits your needs, translates easily into design, and reduces costly rework.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate swiper-slide-duplicate-next" data-swiper-slide-index="1" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 1 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Design management
                            </p>
                        </div>
                        <p  class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>The earlier we get involved, the better the outcome—and design is where it all begins.</b>
                            Using tried and true methods like integrated project delivery and design-build, we operate at a higher level and deliver a streamlined, stress-free process with your goals as our guide.
                        </p>
                    </div>
                </div>
            </div>
            <div class="swiper-slide basis-[calc(100vw-3.2rem)] sm:basis-auto swiper-slide-duplicate" data-swiper-slide-index="2" style="width: 306.909px;">
                <div class="flex flex-col gap-6 relative overflow-hidden transition-all py-9 sm:py-14 px-[0.4rem] sm:px-8 lg:px-6 sm:border-l border-opacity-10 sm:hover:bg-white h-full border-black" :class="carouselActiveIndex == 2 ? 'border-transparent' : 'border-black'">
                    <div class="relative flex flex-col gap-2">
                        <div>
                            <p class="text-current font-medium transition text-xl sm:text-2xl text-primary-600">
                                Engineering services
                            </p>
                        </div>
                        <p class="text-current transition text-sm leading-relaxed text-primary-800">
                            <b>Our engineers are problem solvers who come up with creative construction solutions and lead design-assist and design-build efforts across a range of projects.</b>
                            By working hand-in-hand with our construction teams, they’re uniquely empowered to offer innovative solutions that others can’t.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div class="border-t border-black border-opacity-10 hidden sm:block lg:hidden"></div>
<div class="fluid-container max-w-none hidden lg:block">
    <div class="border-t border-black border-opacity-10"></div>
</div>
<div class="fluid-container flex lg:hidden justify-between gap-4 sm:mt-9">
    <button class="carousel-slider-prev-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary rotate-180" title="Previous">
        <span class="text-primary w-4 h-4">
            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
            </svg>
        </span>
    </button>
    <button class="carousel-slider-next-384642784 border border-primary border-opacity-40 w-14 h-14 grid items-center place-content-center transition hover:scale-110 hover:bg-secondary" title="Next">
        <span class="text-primary w-4 h-4">
            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
            </svg>
        </span>
    </button>
</div>
</div>
</section>
</main>