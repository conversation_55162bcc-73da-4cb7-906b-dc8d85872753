<div id="smooth-wrapper" style="inset: 0px; width: 100%; height: 100%; position: fixed; overflow: hidden;">
<div id="smooth-content" style="transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -6592, 0, 1); box-sizing: border-box; width: 100%; overflow: visible;">
<div data-portal="destination" data-portal-namespace="secondary" class="page-template-default page page-id-8 -mb-px">
    <div x-data="" x-init="
        $store.header.bg_color = 'white';
        $store.header.theme = 'dark';
        $store.header.bg = false;
        $store.header.border_color = 'primary';
        $store.header.border = true;
        " class="hidden"></div>
    <div id="maincontent"></div>
    <section x-data="" x-ref="hero" class="text-white relative pt-32 md:pt-36">
        <div class="bg-primary-800 w-full absolute -top-px inset-x-0 h-full"></div>
        <div x-data="animate()" x-init="
            mm.add(avalanche.breakpoint(avalanche.screens.md), () => {
            element = $refs.content;
            opacity.active = false;
            yPercent = {
            ...yPercent, active: true,
            start: 0,
            end: 0,
            ease: 'none',
            };
            trigger = $refs.hero;
            scrub = 1;
            start = 'left left';
            mounted();
            });
            " x-ref="content" class="relative pb-16 sm:pb-20" style="translate: none; rotate: none; scale: none; transform: translate(0%, 0%);">
            <div class="fluid-container">
                <div class="max-w-3xl">
                    <p x-data="animateText()" x-init="
                        xPercent = {
                        ...xPercent, active: true,
                        start: 0,
                        duration: 0.4,
                        };
                        scale = {
                        ...scale, active: true,
                        start: 0.5,
                        duration: 0.4,
                        };
                        type = `chars`;
                        stagger = 0.03;
                        trigger = $refs.hero;
                        mounted();
                        " x-ref="element" class="text-secondary mb-3 text-base font-medium uppercase tracking-widest"></p>
                    <div class="overflow-hidden" style="display: block; text-align: start; position: relative;">
                        <div style="position:relative;display:inline-block;">
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">O</div>
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">u</div>
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">r</div>
                        </div>
                        <div style="position:relative;display:inline-block;">
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">S</div>
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">t</div>
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">o</div>
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">r</div>
                            <div style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);">y</div>
                        </div>
                    </div>
                    <p></p>
                    <h1 x-data="fadeIn()" x-init="
                        scrollTrigger = false;
                        delay = avalanche.delay.enter + 0.4;
                        mounted();
                        " x-ref="element" class="text-current font-sans leading-none font-semibold text-balance text-4.5xl md:text-6xl lg:text-7xl" style="opacity: 1;">
                        Built on Purpose Since Day One
                    </h1>
                    <p x-data="fadeIn()" x-init="
                        scrollTrigger = false;
                        delay = avalanche.delay.enter + 0.4;
                        mounted();
                        " x-ref="element" class="text-current font-medium mt-4 md:mt-6 text-lg leading-relaxed" style="opacity: 1;">
                        Eastern and our family of Companies continue to build on the foundation laid many years ago to be the proactive and comprehensive contractor we are today.
                    </p>
                </div>
            </div>
        </div>
        <div class="fluid-container relative z-10">
            <div x-data="revealer()" x-init="
                setTrigger($refs.container);
                element = $refs.media;
                mounted();
                " x-ref="container" class="aspect-square short-desktop:aspect-16/7 md:aspect-16/9 relative">
                <div class="absolute z-20 right-0 lg:right-20 -top-16 sm:-top-12 md:-top-16 flex justify-center w-full md:w-auto">
                    <div x-data="magnetic()" class="relative hover:z-10" x-ref="button">
                        <div x-data="{
                        animation: null,
                        on() {
                        this.animation.play();
                        },
                        off() {
                        this.animation.pause();
                        },
                        init() {
                        this.animation = gsap.to($refs.spinHover, {
                        duration: 10,
                        rotate: '360deg',
                        ease: 'none',
                        repeat: -1,
                        });
                        this.animation.pause();
                        }
                        }" @click="
                        $store.overlay.highlight.toggle();
                        $store.overlay.highlight.active = 'hero';
                        " @mousemove="
                        magnetize($event, $el, $refs.ampersand, 8);
                        magnetize($event, $el, $refs.core, 12);
                        magnetize($event, $el, $refs.mantle, 6);
                        magnetize($event, $el, $refs.crust, -3);
                        magnetize($event, $el, $refs.text, -12);
                        " @mouseenter="
                        if (!ScrollTrigger.isTouch) {
                        on();
                        }
                        " @mouseleave="
                        demagnetize($refs.ampersand);
                        demagnetize($refs.core);
                        demagnetize($refs.mantle);
                        demagnetize($refs.crust);
                        demagnetize($refs.text);
                        if (!ScrollTrigger.isTouch) {
                        off();
                        }
                        " class="relative">
                        <button title="Open  By the numbers" class="w-32 relative flex justify-center items-center sm:w-24 md:w-[7.5rem] aspect-square rounded-full focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-400/70 group">
                            <div x-ref="pulse" class="absolute inset-0 flex lg:hidden justify-center items-center">
                                <div class="w-2/5 aspect-square rounded-full bg-secondary animate-ping">
                                </div>
                            </div>
                            <div class="absolute inset-0 hidden sm:flex justify-center items-center p-1 lg:group-hover:scale-[115%] transition duration-300 ease-in-out-circ">
                                <div x-ref="spinHover" class="flex-1">
                                    <div x-ref="text" class="w-full h-full">
                                        <div x-data="animate()" x-init="
                                            mm.add(avalanche.breakpoint(avalanche.screens.sm), () => {
                                            element = $refs.spinScroll;
                                            opacity.active = false;
                                            rotation = {
                                            ...rotation, active: true,
                                            start: 0,
                                            end: 180,
                                            ease: 'none',
                                            };
                                            trigger = $refs.button;
                                            start = 'top bottom';
                                            end = 'bottom top';
                                            scrub = 1;
                                            mounted();
                                            });
                                            " x-ref="spinScroll" class="w-full h-full" style="translate: none; rotate: none; scale: none; transform: rotate(180deg);">
                                            <div x-data="animate()" x-init="
                                                mm.add(avalanche.breakpoint(avalanche.screens.sm), () => {
                                                opacity.active = false;
                                                scale = {
                                                ...scale, active: true,
                                                start: 0,
                                                end: 1,
                                                };
                                                setTrigger($refs.button);
                                                delay = delay + 0.3;
                                                start = 'top bottom';
                                                mounted();
                                                });
                                                " x-ref="element" class="relative w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                                <div class="hidden" x-init="
                                                    $nextTick(() => {
                                                    let destination = document.querySelector('#text-path-876397167');
                                                    if (destination) {
                                                    destination.setAttribute('x-text', `'${$el.innerText.trim()}'`);
                                                    }
                                                    });
                                                    ">
                                                    Built on Purpose
                                                </div>
                                                <svg class="w-full h-full animate-spin-super-slow" viewBox="0 0 200 200">
                                                    <path id="textPath" d="M 83,0 A 83,83 0 0 1 -83,0 A 83,83 0 0 1 83,0" transform="translate(100,100)" fill="none" stroke-width="0" />
                                                    <text>
                                                        <textpath fill="currentColor" class="text-secondary lg:group-hover:text-primary-400 text-[23.5px] uppercase font-medium tracking-widest transition" xlink:href="#textPath" startOffset="0%" id="text-path-876397167" x-text="'Built on Purpose'">
                                                            Built on Purpose
                                                        </textpath>
                                                    </text>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div x-ref="crust" class="absolute inset-0">
                                <div x-data="animate()" x-init="
                                    mm.add(avalanche.breakpoint(avalanche.screens.sm), () => {
                                    opacity.active = false;
                                    scale = {
                                    ...scale, active: true,
                                    start: 0,
                                    end: 1,
                                    };
                                    setTrigger($refs.button);
                                    delay = delay + 0.25;
                                    start = 'top bottom';
                                    mounted();
                                    });
                                    " x-ref="element" class="relative w-full h-full flex justify-center items-center" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                    <div class="w-[63%] aspect-square rounded-full border border-secondary lg:group-hover:border-secondary opacity-30 lg:group-hover:scale-[120%] transition duration-400 ease-in-out-circ">
                                    </div>
                                </div>
                            </div>
                            <div x-ref="mantle" class="absolute inset-0">
                                <div x-data="animate()" x-init="
                                    mm.add(avalanche.breakpoint(avalanche.screens.sm), () => {
                                    opacity.active = false;
                                    scale = {
                                    ...scale, active: true,
                                    start: 0,
                                    end: 1,
                                    };
                                    setTrigger($refs.button);
                                    delay = delay + 0.1;
                                    start = 'top bottom';
                                    mounted();
                                    });
                                    " x-ref="element" class="relative w-full h-full flex justify-center items-center" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                    <div class="w-[58%] aspect-square rounded-full border-[3px] lg:border-[6px] border-secondary lg:group-hover:border-secondary opacity-30 lg:group-hover:opacity-40 lg:group-hover:scale-125 transition-all duration-500 ease-in-out-circ">
                                    </div>
                                </div>
                            </div>
                            <div x-ref="core" class="w-[53%] relative flex justify-center items-center aspect-square rounded-full p-3">
                                <div x-data="animate()" x-init="
                                    opacity.active = false;
                                    scale = {
                                    ...scale, active: true,
                                    start: 0,
                                    end: 1,
                                    };
                                    setTrigger($refs.button);
                                    start = 'top bottom';
                                    mounted();
                                    " x-ref="element" class="absolute inset-0" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                    <div class="relative w-full h-full rounded-full bg-primary-800 border border-secondary lg:group-hover:border-primary lg:group-hover:scale-110 transition ease-in-out-circ">
                                        <div class="absolute inset-0 rounded-full bg-secondary scale-0 lg:group-hover:scale-100 transition"></div>
                                    </div>
                                </div>
                                <div x-ref="ampersand" class="relative flex-1">
                                    <div x-data="animate()" x-init="
                                        opacity.active = false;
                                        scale = {
                                        ...scale, active: true,
                                        start: 0,
                                        end: 1,
                                        };
                                        setTrigger($refs.button);
                                        delay = delay + 0.15;
                                        start = 'top bottom';
                                        mounted();
                                        " x-ref="element" class="relative w-full h-full" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">
                                        <div class="text-secondary lg:group-hover:text-primary-600 lg:group-hover:scale-[118%] transition" style="padding-top:8px">
                                            <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 78.48 56.16">
                                                <image width="327" height="234" transform="scale(.24)" xlink:href="data:image/png;base64,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"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
                <template x-teleport="#overlay-highlight" data-teleport-template="true">
                    <div x-show="
                        $store.overlay.highlight.active == 'hero'
                        " class="relative">
                        <div class="grid gap-4">
                            <h2 class="text-primary-600 !leading-none font-sans font-semibold text-3xl md:text-4xl xl:text-5.5xl">
                                By the numbers
                            </h2>
                            <div x-ref="stats">
                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span x-text="4000 > 0 ? formatNumber(4000) : 4000" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                </span>
                                                <span class="text-current font-light ml-2 opacity-0 invisible leading-tight text-5.25xl">
                                                +
                                                </span>
                                            </div>
                                            <div class="absolute inset-0 flex">
                                                <div class="relative overflow-hidden -mr-8 pr-8">
                                                    <div>
                                                        <span x-data="" x-ref="number" x-init="
                                                            $refs.number.innerHTML = formatNumber(Math.ceil($refs.number.textContent));
                                                            " class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        4000
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="relative ml-2">
                                                    <span x-ref="element" class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    +
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]">
                                            employees
                                        </p>
                                    </div>
                                    <div class="relative w-full overflow-hidden">
                                        <div "="" x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral"></div>
                                    </div>
                                </div>
                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span x-text="220 > 0 ? formatNumber(220) : 220" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                </span>
                                                <span class="text-current font-light ml-2 opacity-0 invisible leading-tight text-5.25xl">
                                                +
                                                </span>
                                            </div>
                                            <div class="absolute inset-0 flex">
                                                <div class="relative overflow-hidden -mr-8 pr-8">
                                                    <div>
                                                        <span x-data="" x-ref="number" x-init="
                                                            $refs.number.innerHTML = formatNumber(Math.ceil($refs.number.textContent));
                                                            " class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        220
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="relative ml-2">
                                                    <span x-ref="element" class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    +
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]">
                                            active projects
                                        </p>
                                    </div>
                                    <div class="relative w-full overflow-hidden">
                                        <div "="" x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral"></div>
                                    </div>
                                </div>
                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span x-text="17 > 0 ? formatNumber(17) : 17" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                </span>
                                            </div>
                                            <div class="absolute inset-0 flex">
                                                <div class="relative overflow-hidden -mr-8 pr-8">
                                                    <div>
                                                        <span x-data="" x-ref="number" x-init="
                                                            $refs.number.innerHTML = formatNumber(Math.ceil($refs.number.textContent));
                                                            " class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        17
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]">
                                            states with active projects
                                        </p>
                                    </div>
                                    <div class="relative w-full overflow-hidden">
                                        <div "="" x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral"></div>
                                    </div>
                                </div>
                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span x-text="60 > 0 ? formatNumber(60) : 60" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                </span>
                                                <span class="text-current font-light ml-2 opacity-0 invisible leading-tight text-5.25xl">
                                                +
                                                </span>
                                            </div>
                                            <div class="absolute inset-0 flex">
                                                <div class="relative overflow-hidden -mr-8 pr-8">
                                                    <div>
                                                        <span x-data="" x-ref="number" x-init="
                                                            $refs.number.innerHTML = formatNumber(Math.ceil($refs.number.textContent));
                                                            " class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        60
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="relative ml-2">
                                                    <span x-ref="element" class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    +
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]">
                                            years in business
                                        </p>
                                    </div>
                                    <div class="relative w-full overflow-hidden">
                                        <div "="" x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral"></div>
                                    </div>
                                </div>
                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                $
                                                </span>
                                                <span x-text="6 > 0 ? formatNumber(6) : 6" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                </span>
                                                <span class="text-current font-light ml-2 opacity-0 invisible leading-tight text-5.25xl">
                                                .4
                                                </span>
                                            </div>
                                            <div class="absolute inset-0 flex">
                                                <div>
                                                    <span x-ref="element" class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    $
                                                    </span>
                                                </div>
                                                <div class="relative overflow-hidden -mr-8 pr-8">
                                                    <div>
                                                        <span x-data="" x-ref="number" x-init="
                                                            $refs.number.innerHTML = formatNumber(Math.ceil($refs.number.textContent));
                                                            " class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        6
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="relative ml-2">
                                                    <span x-ref="element" class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    .4
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]">
                                            billion annual revenue
                                        </p>
                                    </div>
                                    <div class="relative w-full overflow-hidden">
                                        <div "="" x-ref="element" class="w-full h-px bg-white/20 lg:bg-neutral"></div>
                                    </div>
                                </div>
                                <div class="relative">
                                    <div class="py-2 flex justify-start items-center gap-3">
                                        <div class="relative w-auto">
                                            <div class="flex">
                                                <span class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                #
                                                </span>
                                                <span x-text="22 > 0 ? formatNumber(22) : 22" class="text-current font-light mb-0 opacity-0 invisible leading-tight text-5.25xl">
                                                </span>
                                            </div>
                                            <div class="absolute inset-0 flex">
                                                <div>
                                                    <span x-ref="element" class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                    #
                                                    </span>
                                                </div>
                                                <div class="relative overflow-hidden -mr-8 pr-8">
                                                    <div>
                                                        <span x-data="" x-ref="number" x-init="
                                                            $refs.number.innerHTML = formatNumber(Math.ceil($refs.number.textContent));
                                                            " class="text-primary font-light mb-0 leading-tight text-5.25xl">
                                                        22
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-primary-800 font-medium mt-0 mb-8 last:mb-0 sm:w-auto leading-tight text-[15px]">
                                            top contractor nationally, according to Engineering News-Record
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
            <div class="relative w-full h-full overflow-hidden">
                <div x-ref="media" class="absolute inset-0 w-full h-full" style="opacity: 1;">
                    <div class="absolute inset-0 w-full h-full overflow-hidden">
                        <div x-ref="element" class="absolute inset-0 w-full h-full">
                            <picture>
                                <img src="/images/our-history-0.jpg" class="absolute inset-0 w-full h-full object-cover" alt="Workers look up at a crane placing a truss on a tall building">
                            </picture>
                        </div>
                    </div>
                </div>
                <div x-ref="cover" class="bg-primary-600 absolute inset-0" style="opacity: 0;"></div>
                <div x-ref="curtain" class="bg-primary absolute inset-0" style="translate: none; rotate: none; scale: none; transform: translate(101%, 0%);"></div>
            </div>
        </div>
</div>
<div class="h-16 md:h-60 bg-white w-full absolute bottom-0"></div>
</section>
<main class="relative outer-grid overflow-hidden -mb-px">
    <div>
        <div class="fluid-container text-center">
            <div>
                <h2 class="text-primary-600 !leading-tight font-sans font-semibold text-3xl md:text-4xl xl:text-5.5xl mb-6 text-center">
                    Our Core Values & Principles
                </h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 md:py-8">
                    <div class="values-grid py-5 lg:py-10 lg:border-r border-light-gray">
                        <div class="flex flex-col justify-center items-center">
                            <p class="text-primary-600 mb-2 font-medium text-xl lg:text-2xl leading-tight">
                                Hard Work
                            </p>
                            <p class="text-current mt-0 mb-8 last:mb-0 max-w-[16rem] text-base leading-relaxed font-normal">
                                We have a performance culture that values hard work. Work ethic is baked into our company by our founders.
                            </p>
                        </div>
                    </div>
                    <div class="values-grid py-5 lg:py-10 lg:border-r border-light-gray">
                        <div class="flex flex-col justify-center items-center">
                            <p class="text-primary-600 mb-2 font-medium text-xl lg:text-2xl leading-tight">
                                Bold Vision
                            </p>
                            <p class="text-current mt-0 mb-8 last:mb-0 max-w-[16rem] text-base leading-relaxed font-normal">
                                Our direction is more than mere growth of our company. It’s leaving a legacy for the next generation to do bigger and greater things.
                            </p>
                        </div>
                    </div>
                    <div class="values-grid py-5 lg:py-10 lg:border-r border-light-gray">
                        <div class="flex flex-col justify-center items-center">
                            <p class="text-primary-600 mb-2 font-medium text-xl lg:text-2xl leading-tight">
                                Perseverance
                            </p>
                            <p class="text-current mt-0 mb-8 last:mb-0 max-w-[16rem] text-base leading-relaxed font-normal">
                                Together, we make it happen” is an acknowledgement of our bold vision.  Without leadership’s commitment and the team’s collective effort, our goals cannot be realized.
                            </p>
                        </div>
                    </div>
                    <div class="values-grid py-5 lg:py-10 lg:border-r border-light-gray">
                        <div class="flex flex-col justify-center items-center">
                            <p class="text-primary-600 mb-2 font-medium text-xl lg:text-2xl leading-tight">
                                Diversity
                            </p>
                            <p class="text-current mt-0 mb-8 last:mb-0 max-w-[16rem] text-base leading-relaxed font-normal">
                                We bring diverse expertise and creative solutions to every project, ensuring successful delivery across commercial, industrial, and manufacturing facilities.
                            </p>
                        </div>
                    </div>
                    <div class="values-grid py-5 lg:py-10 lg:border-r border-light-gray">
                        <div class="flex flex-col justify-center items-center">
                            <p class="text-primary-600 mb-2 font-medium text-xl lg:text-2xl leading-tight">
                                Control
                            </p>
                            <p class="text-current mt-0 mb-8 last:mb-0 max-w-[16rem] text-base leading-relaxed font-normal">
                                Our hands-on approach and self-performed enclosure work give us unmatched control over cost and schedule from start to finish.
                            </p>
                        </div>
                    </div>
                    <div class="values-grid py-5 lg:py-10 lg:border-r border-light-gray">
                        <div class="flex flex-col justify-center items-center">
                            <p class="text-primary-600 mb-2 font-medium text-xl lg:text-2xl leading-tight">
                                Commitment
                            </p>
                            <p class="text-current mt-0 mb-8 last:mb-0 max-w-[16rem] text-base leading-relaxed font-normal">
                                Our deep commitment to control, collaboration, and follow-through ensures we overcome challenges and deliver project success—together.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="-mt-20 md:-mt-24 lg:-mt-24" x-data="">
        <div class="fluid-container flex flex-col gap-8 lg:gap-28 lg:items-stretch lg:flex-row lg:justify-between">
            <div class="relative w-full basis-1/2 aspect-square lg:w-full lg:aspect-auto">
                <div class="absolute z-20 right-0 md:bottom-10 md:right-10 flex justify-center md:justify-end -top-16 md:top-auto w-full md:w-auto">
                </div>
                <div class="absolute z-10 inset-0 w-full h-full overflow-hidden">
                    <div x-ref="background" class="absolute inset-0 w-full h-full">
                        <picture>
                            <img src="/images/our-history.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 50% 50%" alt="Group of construction workers in safety gear posing together at a construction site, proudly representing our company's mission.">
                        </picture>
                    </div>
                </div>
            </div>
            <div class="w-full basis-1/2 lg:py-6 xl:py-20">
                <div x-ref="content" class="text-left mb-8">
                    <p x-data="animateText()" x-init="
                        xPercent = {
                        ...xPercent, active: true,
                        start: -100,
                        duration: 0.4,
                        };
                        scale = {
                        ...scale, active: true,
                        start: 0.5,
                        duration: 0.4,
                        };
                        type = `chars`;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary mb-3 text-base font-medium uppercase tracking-widest">
                        About Us
                    </p>
                    <h2 x-data="animateText()" x-init="
                        opacity.active = false;
                        yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                        };
                        type = `words`;
                        wordsClass = avalanche.textClass.words.h1;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                        Who we are
                    </h2>
                    <p class="text-current mt-4 text-base leading-relaxed font-normal">
                        Over the years, Eastern Companies has grown from its roots as a self-performing roofing contractor to a conglomerate of subsidiaries that is equipped to meet virtually any construction need. With the talents of our diverse, intensely driven team of professionals, Eastern Companies continues to grow and achieve new heights.
                    </p>
                    <p class="text-current mt-4 text-base leading-relaxed font-normal">
                        Our story is about the outstanding results that come from hard work, bold vision, and perseverance. We approach each project with a single-minded focus to fulfill our commitment and exceed expectations. “Together, we make it happen.”
                    </p>
                </div>
                <div class="border-b border-light-gray mb-3">
                    <p class="text-current font-medium mb-3 text-base leading-relaxed">
                        Our purpose
                    </p>
                    <p class="text-current mb-3 text-base leading-relaxed font-normal">
                        To build spaces that stand for something—quality, trust, and the people who depend on them.
                    </p>
                </div>
                <div class="border-b border-light-gray mb-3">
                    <p class="text-current font-medium mb-3 text-base leading-relaxed">
                        Our foundation
                    </p>
                    <p class="text-current mb-3 text-base leading-relaxed font-normal">
                        Rooted in integrity, driven by precision, and built to last—every project, every time.
                    </p>
                </div>
            </div>
        </div>
    </div>
    <div x-data="">
        <div class="fluid-container flex flex-col gap-8 lg:gap-28 lg:items-stretch lg:flex-row-reverse lg:justify-between">
            <div class="relative w-full basis-1/2 aspect-square lg:w-full lg:aspect-auto">
                <div class="absolute z-20 left-0 md:bottom-10 md:left-10 flex justify-center md:justify-end -top-16 md:top-auto w-full md:w-auto">
                </div>
                <div class="absolute z-10 inset-0 w-full h-full overflow-hidden">
                    <div x-ref="background" class="absolute inset-0 w-full h-full">
                        <picture>
                            <img src="/images/our-history-2.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 50% 50%" alt="Two gentlemen shaking hands at a formal event, encircled by other participants and a seated dining area in the background.">
                        </picture>
                    </div>
                </div>
            </div>
            <div class="w-full basis-1/2 lg:py-6 xl:py-20">
                <div x-ref="content" class="text-left mb-8">
                    <p x-data="animateText()" x-init="
                        xPercent = {
                        ...xPercent, active: true,
                        start: -100,
                        duration: 0.4,
                        };
                        scale = {
                        ...scale, active: true,
                        start: 0.5,
                        duration: 0.4,
                        };
                        type = `chars`;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary mb-3 text-base font-medium uppercase tracking-widest">
                        Stepping Stone
                    </p>
                    <h2 x-data="animateText()" x-init="
                        opacity.active = false;
                        yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                        };
                        type = `words`;
                        wordsClass = avalanche.textClass.words.h1;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                        From Specialty to Scale
                    </h2>
                    <p class="text-current mt-4 text-base leading-relaxed font-normal">
                        Capitalizing on years of hard work and success, Eastern secured and completed the game-changing $200M program for the SKBA battery facility in Georgia as a general contractor. This opportunity established Eastern’s ability to advance from a reputable commercial enclosure contractor to a full-service general contractor.
                    </p>
                </div>
            </div>
        </div>
    </div>
    <div x-data="">
        <div class="fluid-container flex flex-col gap-8 lg:gap-28 lg:items-stretch lg:flex-row lg:justify-between">
            <div class="relative w-full basis-1/2 aspect-square lg:w-full lg:aspect-auto">
                <div class="absolute z-20 right-0 md:bottom-10 md:right-10 flex justify-center md:justify-end -top-16 md:top-auto w-full md:w-auto">
                </div>
                <div class="absolute z-10 inset-0 w-full h-full overflow-hidden">
                    <div x-ref="background" class="absolute inset-0 w-full h-full">
                        <picture>
                            <img src="/images/our-history-3.jpg" class="absolute inset-0 w-full h-full object-cover" style="object-position: 85% 90%" alt="A construction worker in a safety vest, hard hat, and sunglasses smiles while standing next to heavy machinery at a worksite, capturing the spirit of a true Brasfield and Gorrie story.">
                        </picture>
                    </div>
                </div>
            </div>
            <div class="w-full basis-1/2 lg:py-6 xl:py-20">
                <div x-ref="content" class="text-left mb-8">
                    <p x-data="animateText()" x-init="
                        xPercent = {
                        ...xPercent, active: true,
                        start: -100,
                        duration: 0.4,
                        };
                        scale = {
                        ...scale, active: true,
                        start: 0.5,
                        duration: 0.4,
                        };
                        type = `chars`;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary mb-3 text-base font-medium uppercase tracking-widest">
                        The Rooftop Moment
                    </p>
                    <h2 x-data="animateText()" x-init="
                        opacity.active = false;
                        yPercent = {
                        ...yPercent, active: true,
                        start: 102,
                        duration: 0.4,
                        };
                        type = `words`;
                        wordsClass = avalanche.textClass.words.h1;
                        stagger = 0.03;
                        setTrigger($refs.content);
                        mounted();
                        " x-ref="element" class="text-primary-600 font-sans leading-none font-semibold text-5xl md:text-6xl text-balance">
                        Humble Beginnings, Bold Vision
                    </h2>
                    <p class="text-current mt-4 text-base leading-relaxed font-normal">
                        Peter Kim set out to serve his adopted country through military service, but financial hardship changed his path. He joined his brother-in-law’s roofing business and helped rebuild an elementary school roof destroyed by a hurricane—without pay, but with purpose. Sitting atop that finished roof, counting rooftops on the horizon, Peter envisioned the future that would become Eastern Corporation.
                    </p>
                </div>
                <div class="flex justify-center w-full md:justify-start items-center gap-6 mt-8">
                    <div x-data="animate()" x-init="
                        setTrigger($refs.buttons);
                        element = [...$refs.buttons.querySelectorAll('[data-button]')];
                        stagger = 0.2;
                        delay = delay + 0.1;
                        mounted();
                        " x-ref="buttons" class="w-full lg:items-stretch mt-3 sm:mt-4 relative z-10 flex flex-wrap gap-6">
                        <div data-button="" class="w-full md:w-fit">
                            <div x-data="{
                                hovered: false,
                                }" class="w-full md:w-fit inline-flex">
                                <div @mouseenter="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = true;
                                    }
                                    " @mouseleave="
                                    if (!ScrollTrigger.isTouch) {
                                    hovered = false;
                                    }
                                    " class="w-full md:w-fit relative inline-flex hover:z-10">
                                    <a href="/our-history-2" data-portal-prevent="self" class="w-full md:w-fit items-center justify-center outline-offset-4 leading-none select-none whitespace-nowrap cursor-pointer transition-all group inline-flex text-white font-medium text-sm uppercase tracking-wide px-10 hover:px-9 py-6 bg-primary hover:bg-secondary hover:text-primary-600" href="./html/corporate-responsibility.html">
                                        See More
                                        <span class="w-0 group-hover:w-3 transition-all group-hover:translate-x-2">
                                            <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0.453124 8.3832L0.453124 6.58908L11.2178 6.58908L6.28398 1.65528L7.5578 0.38146L14.6625 7.48614L7.55781 14.5908L6.28399 13.317L11.2178 8.38319L0.453124 8.3832Z" fill="currentColor" />
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php echo view('pages/_years'); ?>
</main>